using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.ModelBinding.Binders;
using Newtonsoft.Json;
using QuickMessenger.API.Data.Model;

namespace QuickMessenger.API.Helpers
{
    public static class Extensions
    {
         public static void AddApplicationError(this HttpResponse response, string message)
        {
            response.Headers.Add("Application-Error",message);
            response.Headers.Add("Access-Control-Expose-Headers", "Application-Error");
            response.Headers.Add("Access-Control-Allow-Origin", "*");
        }

        public static Photo  GetFirstElement(this ICollection<Photo> Collections) 
        {
            foreach(var o in Collections)
            return o;

            return new Photo();
        }

        public static DateTime GetLocalTime(this DateTime time){
            
        TimeZoneInfo gmtPlusOneTimeZone = TimeZoneInfo.FindSystemTimeZoneById("W. Europe Standard Time"); // Time zone ID for GMT+1

        // Convert the DateTime object to GMT+1
        DateTime gmtPlusOneTime = TimeZoneInfo.ConvertTime(time, gmtPlusOneTimeZone);
        return gmtPlusOneTime;
        }
        public static string GenerateValidNameId(this string strValue){
            Regex regex = new Regex("[-!$%^&*()_+|~=`{}\\:\";//['<>?,./]");
            var ValueToReturn = regex.Replace(strValue, "_").ToLower();
            ValueToReturn = ValueToReturn.Replace("]", "");
            ValueToReturn = ValueToReturn.Replace(" and ", "_");
            return ValueToReturn.Replace(" ","_");
        }

        public static string RemoveSpecialCharatcters(this string strValue){
            Regex regex = new Regex("[-!$%^&*()_+|~=`{}\\:\";//['<>?,./]");
            var ValueToReturn = regex.Replace(strValue, "");
            ValueToReturn = ValueToReturn.Replace("]", "");
           return ValueToReturn;
        }
        public static string RemoveSpacesAndSpecialCharatcters(this string strValue){
            Regex regex = new Regex("[-!$%^&*()_+|~=`{}\\:\";//['<>?,./ ]");
            var ValueToReturn = regex.Replace(strValue, "");
            ValueToReturn = ValueToReturn.Replace("]", "");
           return ValueToReturn;
        }

        public static string GetItemsNamesFromList(this Order order)
        {
            int count = order.PickupItems.Count();
            if( count > 0)
            {
                var items = order.PickupItems;
                switch(count)
                {
                    case 0:
                    return "";

                    case 1:
                    return $"{items.ElementAt(0).Name}" ;
                
                    case 2:
                    return $"{items.ElementAt(0).Name}, {items.ElementAt(1).Name}" ;

                    case 3: 
                    return $"{items.ElementAt(0).Name}, {items.ElementAt(1).Name}, {items.ElementAt(2).Name}" ;
                    
                    default:
                    return $"{items.ElementAt(0).Name}, {items.ElementAt(2).Name}, {items.ElementAt(3).Name}..." ;
                }
            }
            else if(order.ProductOrders.Count() > 0)
            {
                var items = order.ProductOrders;
               
                switch(items.Count())
                {
                    case 0:
                    return "";

                    case 1:
                    return $"{items.ElementAt(0).Product.Name}" ;
                
                    case 2:
                    return $"{items.ElementAt(0).Product.Name}, {items.ElementAt(1).Product.Name}" ;

                    case 3: 
                    return $"{items.ElementAt(0).Product.Name}, {items.ElementAt(1).Product.Name}, {items.ElementAt(2).Product.Name}" ;
                    
                    default:
                    return $"{items.ElementAt(0).Product.Name}, {items.ElementAt(2).Product.Name}, {items.ElementAt(3).Product.Name}..." ;
                }
                
            }
            
            return ""; 
        
            
        }

        public static int GetNumberOfProducts(this Order order)
        {
            int count = order.PickupItems.Count();
            
             foreach(var temp in order.ProductOrders)
                {
                    count += temp.Quantity;
                }
                return count;
        }

        public static string FormatForMoney(this double val)
        {
           return  String.Format("{0:#,##0}", val);
        }
    public  class TrimmingConverter : JsonConverter
        {
            public override bool CanConvert(Type objectType)
            {
                return objectType == typeof(string);
            }

            public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
            {
                if (reader.TokenType == JsonToken.String)
                if (reader.Value != null)
                    return (reader.Value as string).Trim();

                return reader.Value;
            }

            public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
            {
                var text = (string)value;
                if (text == null)
                writer.WriteNull();
                else
                writer.WriteValue(text.Trim());
            }
        }
 }
}