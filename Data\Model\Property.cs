using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace QuickMessenger.API.Data.Model
{
    public class Property
    {
        public int Id { get; set; }
        [Required]
        public Prod_Category Prod_Category { get; set; }        
        public int Prod_CategoryId { get; set; }
        [Required]
        public Prod_PropertyType Prod_PropertyType { get; set; }
        public int Prod_PropertyTypeId { get; set; }
        public Prod_MeasurementType Prod_MeasurementType { get; set; }
        public int? Prod_MeasurementTypeId { get; set; }
        public ICollection<ProductProperty> ProductProperties{ get; set; }

    }
}