using System;
using Microsoft.VisualBasic;
using QuickMessenger.API.Data.DTO.Shared;

namespace QuickMessenger.API.Data.DTO.Staff
{
    public class RiderListLiteDto
    {
        public int Id { get; set; }
        public string FirstName { get; set; }
        public string Lastname { get; set; }
        public string PhotoUrl { get; set; }
    }

    public class RiderEarningDto{
        public string ClientName { get; set; }
        public AddressDto2 Address { get; set; }
        public double Earninig  { get; set; }
        public DateTime Date { get; set; }
    }

    public class RiderEarningGroup{
       public string Name { get; set; }
        public int OrdersCount { get; set; }
        //Amount made that week
        public double Amount { get; set; }

        public DateTime? StartDate { get; set; }
        public DateTime? EndDate {get; set;}
    }
} 