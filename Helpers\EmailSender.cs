using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Amazon.SimpleEmail;
using Amazon.SimpleEmail.Model;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.Extensions.Options;
using SendGrid;
using SendGrid.Helpers.Mail;
using Content = Amazon.SimpleEmail.Model.Content;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using QuickMessenger.API.Data.DTO.ThirdParty;
using MimeKit;
using MailKit.Net.Smtp;
using MailKit;
using System.IO;

namespace QuickMessenger.API.Helpers
{
    public class EmailSender : IEmailSender
    {
        public EmailSender(IOptions<AuthMessageSenderOptions> optionsAccessor)
        {
            Options = optionsAccessor.Value;
        }

        public AuthMessageSenderOptions Options { get; } //set only via Secret Manager

        public async Task SendEmailAsync(string email, string subject, string message)
        {
            // await Execute3(Options.EmailSetting, subject, message, email);
            await Execute4(Options.EmailSetting, subject, message, email);
            // await SendZohoEmailAsync(Options.EmailSetting, email, subject, message);
            // await SendEmailViaZohoApi(Options.EmailSetting, Options.ZohoSetting, email, subject, message);
        }

        public async Task Execute(AuthMessageSenderOptions2 credentials, string subject, string message, string email)
        {
            var client = new AmazonSimpleEmailServiceClient(credentials.Key_Id, credentials.Secrete_Key, Amazon.RegionEndpoint.EUNorth1);
            if (subject.Split("-")[0].CompareTo("inquiry") == 0)
            {
                var sendEmailRequest = new SendEmailRequest()
                {
                    Source = email,
                    Destination = new Destination()
                    {
                        ToAddresses = new List<string>() { email }
                    },
                    Message = new Message()
                    {
                        Subject = new Content()
                        {
                            Data = "INQUIRY FROM QUICKWAKA SITE"
                        },
                        Body = new Body()
                        {
                            Text = new Content()
                            {
                                Data = message.Replace("\n", "<br/>")
                            }
                        }
                    }
                };
                var sendEmailResponse = await client.SendEmailAsync(sendEmailRequest);
            }
            else
            {
                var sendEmailRequest = new SendEmailRequest()
                {
                    Source = "<EMAIL>",
                    Destination = new Destination()
                    {
                        ToAddresses = new List<string>() { email }
                    },
                    Message = new Message()
                    {
                        Subject = new Content()
                        {
                            Data = subject
                        },
                        Body = new Body()
                        {
                            Html = new Content
                            {
                                Charset = "UTF-8",
                                Data = message
                            },
                        }
                    }
                };
                var sendEmailResponse = await client.SendEmailAsync(sendEmailRequest);
            }


        }

        public async Task Execute2(string apiKey, string subject, string message, string email)
        {
            var client = new SendGridClient(apiKey);
            var msg = new SendGridMessage()
            {
                From = new EmailAddress("<EMAIL>", "QuickWaka"),
                Subject = subject,
                //PlainTextContent = message,
                HtmlContent = message,
                ReplyTo = new EmailAddress(email)
            };
            msg.AddTo(new EmailAddress(email));
            msg.SetClickTracking(false, false);
            var res = await client.SendEmailAsync(msg);

        }

        //sending with smtp client
        public async Task Execute3(EmailSetting smtpSetting, string subject, string message, string email)
        {

            try
            {
                using (var client = new System.Net.Mail.SmtpClient(smtpSetting.Server))
                {
                    client.UseDefaultCredentials = false;
                    client.Credentials = new System.Net.NetworkCredential(smtpSetting.Username, smtpSetting.Password);
                    client.EnableSsl = true;
                    client.Port = Int32.Parse(smtpSetting.Port);
                    if (subject.Split("-")[0].CompareTo("inquiry") == 0)
                    {
                        subject = "INQUIRY FROM QUICKWAKA SITE";
                        smtpSetting.Username = email;
                        // email = "<EMAIL>";
                        email = "<EMAIL>";
                    }

                    var mailMessage = new System.Net.Mail.MailMessage()
                    {
                        From = new System.Net.Mail.MailAddress(smtpSetting.Username, "QuickWaka"),
                        Subject = subject,
                        Body = message,
                        BodyEncoding = System.Text.Encoding.UTF8,
                        IsBodyHtml = true
                    };

                    mailMessage.To.Add(email);

                    await client.SendMailAsync(mailMessage);
                }

            }
            catch (System.Exception ex)
            {
                Console.WriteLine(ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Improved SMTP sender using MailKit. Does not mutate the incoming smtpSetting, selects secure socket options by port,
        /// logs key steps, and retries authentication with the From address when the primary username fails.
        /// This can be used as a safer replacement for Execute3.
        /// </summary>
        public async Task Execute4(EmailSetting smtpSetting, string subject, string message, string email)
        {
            // Do not modify the incoming smtpSetting object.
            var server = string.IsNullOrWhiteSpace(smtpSetting.Server) ? "smtp.zoho.com" : smtpSetting.Server.Trim();
            var port = 587;
            if (!string.IsNullOrWhiteSpace(smtpSetting.Port) && int.TryParse(smtpSetting.Port, out var p))
                port = p;

            var fromAddress = string.IsNullOrWhiteSpace(smtpSetting.FromEmail) ? smtpSetting.Username : smtpSetting.FromEmail;
            var displayName = "QuickWaka";

            try
            {
                var emailMessage = new MimeMessage();
                emailMessage.From.Add(new MailboxAddress(displayName, fromAddress));
                emailMessage.To.Add(new MailboxAddress("", email));
                emailMessage.Subject = subject;

                var builder = new BodyBuilder { HtmlBody = message };
                emailMessage.Body = builder.ToMessageBody();

                // If this is an inquiry, set a sensible reply-to and keep original sender semantics.
                if (subject.Split('-')[0].Equals("inquiry", StringComparison.OrdinalIgnoreCase))
                {
                    // do not overwrite smtpSetting; set ReplyTo so responses go to the enquiring user
                    emailMessage.ReplyTo.Add(new MailboxAddress("", email));
                }

                // Prepare protocol log path
                var logFileName = "smtp-execute4.log";
                var logPath = Path.Combine(Directory.GetCurrentDirectory(), logFileName);
                Console.WriteLine($"[Execute4] Protocol log: {logPath}");

                // Attach a ProtocolLogger so we capture the full SMTP/TLS exchange
                using (var client = new MailKit.Net.Smtp.SmtpClient(new ProtocolLogger(logPath)))
                {
                    // Choose secure option by port
                    var secure = MailKit.Security.SecureSocketOptions.Auto;
                    if (port == 465) secure = MailKit.Security.SecureSocketOptions.SslOnConnect;
                    else if (port == 587) secure = MailKit.Security.SecureSocketOptions.StartTls;

                    Console.WriteLine($"[Execute4] Connecting to {server}:{port} using {secure}");
                    await client.ConnectAsync(server, port, secure);

                    // Remove unsupported/undesired mechanisms
                    client.AuthenticationMechanisms.Remove("XOAUTH2");

                    var primaryUser = smtpSetting.Username;
                    var password = smtpSetting.Password;

                    // Try authenticating with the configured Username first, then fall back to FromEmail when different
                    try
                    {
                        Console.WriteLine($"[Execute4] Authenticating as {primaryUser}");
                        await client.AuthenticateAsync(primaryUser, password);
                    }
                    catch (Exception authEx)
                    {
                        Console.WriteLine($"[Execute4] Primary auth failed for {primaryUser}: {authEx.Message}");
                        if (!string.IsNullOrWhiteSpace(fromAddress) && !string.Equals(fromAddress, primaryUser, StringComparison.OrdinalIgnoreCase))
                        {
                            try
                            {
                                Console.WriteLine($"[Execute4] Retrying auth as {fromAddress}");
                                await client.AuthenticateAsync(fromAddress, password);
                            }
                            catch (Exception authEx2)
                            {
                                Console.WriteLine($"[Execute4] Fallback auth also failed: {authEx2.Message}");
                                throw; // bubble up to caller
                            }
                        }
                        else
                        {
                            throw; // rethrow original auth exception
                        }
                    }

                    // Send and disconnect
                    Console.WriteLine($"[Execute4] Sending message to {email}");
                    await client.SendAsync(emailMessage);
                    await client.DisconnectAsync(true);
                    Console.WriteLine("[Execute4] Message sent and disconnected");
                }
            }
            catch (MailKit.Net.Smtp.SmtpCommandException ex)
            {
                Console.WriteLine($"[Execute4] SMTP command error: {ex.StatusCode} - {ex.Message}");
                throw;
            }
            catch (MailKit.Net.Smtp.SmtpProtocolException ex)
            {
                Console.WriteLine($"[Execute4] SMTP protocol error: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Execute4] General SMTP error: {ex.Message}");
                throw;
            }
        }

        public static async Task<string> GetZohoAccessToken(ZohoSetting zohoSetting)
        {
            var client = new HttpClient();
            var request = new HttpRequestMessage(
                HttpMethod.Post,
                "https://accounts.zoho.com/oauth/v2/token"
            );

            var content = new FormUrlEncodedContent(new Dictionary<string, string>
            {
                { "grant_type", "refresh_token" },
                { "client_id", zohoSetting.ClientId },
                { "client_secret", zohoSetting.ClientSecret },
                { "refresh_token", zohoSetting.RefreshToken }
            });

            request.Content = content;
            var response = await client.SendAsync(request);
            var responseBody = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                var error = JsonSerializer.Deserialize<ZohoErrorResponse>(responseBody);
                if (error?.error == "invalid_grant")
                {
                    // Refresh token expired! Trigger re-authentication.
                    throw new Exception("Refresh token expired. Re-authenticate with Zoho.");
                }
                throw new Exception($"Zoho API error: {responseBody}");
            }

            var tokenResponse = JsonSerializer.Deserialize<ZohoTokenResponse>(responseBody);
            return tokenResponse.access_token;
        }

        public async Task SendEmailViaZohoApi(EmailSetting smtpSetting, ZohoSetting zohoSetting, string toEmail, string subject, string body)
        {
            try
            {
                var token = await EmailSender.GetZohoAccessToken(zohoSetting);
                var client = new HttpClient();
                client.DefaultRequestHeaders.Add("Authorization", $"Zoho-oauthtoken {token}");

                var requestBody = new
                {
                    fromAddress = smtpSetting.FromEmail,
                    toAddress = toEmail,
                    subject = subject,
                    content = body
                };

                var jsonContent = new StringContent(
                    JsonSerializer.Serialize(requestBody),
                    Encoding.UTF8,
                    "application/json"
                );
                string url = $"https://mail.zoho.com/api/accounts/{smtpSetting.AccountId}/messages";
                var response = await client.PostAsync(
                    url,
                    jsonContent
                );

                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception("Failed to send email via Zoho API.");
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                throw;
            }
        }

        public async Task<string> SendZohoEmailAsync(EmailSetting smtpSetting, string toEmail, string subject, string body)
        {
            try
            {
                var emailMessage = new MimeMessage();
                // Use the configured FromEmail if available; fall back to Username
                var fromAddress = string.IsNullOrWhiteSpace(smtpSetting.FromEmail) ? smtpSetting.Username : smtpSetting.FromEmail;
                emailMessage.From.Add(new MailboxAddress("QuickWaka", fromAddress));
                emailMessage.To.Add(new MailboxAddress("", toEmail));
                emailMessage.Subject = subject;

                var bodyBuilder = new BodyBuilder { HtmlBody = body };
                emailMessage.Body = bodyBuilder.ToMessageBody();

                using (var client = new SmtpClient())
                {
                    // Resolve server and port from settings; choose secure socket options based on common ports
                    var server = string.IsNullOrWhiteSpace(smtpSetting.Server) ? "smtp.zoho.com" : smtpSetting.Server;
                    var port = 587;
                    if (!string.IsNullOrWhiteSpace(smtpSetting.Port) && int.TryParse(smtpSetting.Port, out var parsedPort))
                        port = parsedPort;

                    var secure = MailKit.Security.SecureSocketOptions.Auto;
                    if (port == 465) secure = MailKit.Security.SecureSocketOptions.SslOnConnect; // SMTPS
                    else if (port == 587) secure = MailKit.Security.SecureSocketOptions.StartTls; // Submission w/ STARTTLS

                    await client.ConnectAsync(server, port, secure);

                    // Authenticate with SMTP credentials (Username should match the SMTP account for Zoho)
                    client.AuthenticationMechanisms.Remove("XOAUTH2");
                    try
                    {
                        await client.AuthenticateAsync(smtpSetting.Username, smtpSetting.Password);
                    }
                    catch
                    {
                        // Retry with FromEmail as username if different
                        if (!string.IsNullOrWhiteSpace(fromAddress) && !string.Equals(fromAddress, smtpSetting.Username, StringComparison.OrdinalIgnoreCase))
                        {
                            await client.AuthenticateAsync(fromAddress, smtpSetting.Password);
                        }
                        else
                        {
                            throw;
                        }
                    }

                    await client.SendAsync(emailMessage);
                    await client.DisconnectAsync(true);
                }

                return "Support Message Sent Successfully";
            }
            catch (MailKit.Net.Smtp.SmtpCommandException ex)
            {
                Console.WriteLine($"SMTP command error: {ex.StatusCode} - {ex.Message}");
                throw;
            }
            catch (MailKit.Net.Smtp.SmtpProtocolException ex)
            {
                Console.WriteLine($"SMTP protocol error: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"SMTP general error: {ex.Message}");
                throw;
            }
        }
    }

}