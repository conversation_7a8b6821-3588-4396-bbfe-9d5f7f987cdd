using System.Collections.Generic;
using System.Threading.Tasks;
using Google.Apis.Auth;
using Microsoft.AspNetCore.Identity;
using QuickMessenger.API.Data.DTO.Client;
using QuickMessenger.API.Data.DTO.Shared;
using QuickMessenger.API.Data.DTO.Staff;
using QuickMessenger.API.Data.Model;

namespace QuickMessenger.API.Data.IService
{
    public interface IUserService
    {
        Task<IdentityResult>  CreateStaff(StaffDetailDto staff);
        Task<Client>  CreateClient(ClientRegisterDto client);
        Task<Client>  CreateClient(Client client);
        Task SendStaffCreationEmail(StaffDetailDto userToBeCreated);
        Task SendClientRegisterationEmail(User userToBeCreated, string url);
        Task SendPassswordResetEmail(User userToBeCreated, string url);

        Task SendClientOrderConfirmationEmail(User user, Cart cart,ICollection<Order> orders);
        Task<bool>  UpdateStaff(StaffDetailDto staff, User user, string picturePath);

        Task<bool> UpdateClientLastLogin(User user);                    

        Task ContactUS (Contact contact);
    }
}