using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using AutoMapper;
using FirebaseAdmin.Messaging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using QuickMessenger.API.Data.DTO.Client;
using QuickMessenger.API.Data.Services;
using QuickMessenger.API.Data.DTO.Vendor;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.IService;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Helpers.Params;
using System.Linq;

namespace QuickMessenger.API.Controllers.Staff
{
    [ApiController] 
    [Authorize(Policy = "OrderMgt")]
    [Route("api/qm_475/staff/[controller]")]
    public class OrderController : ControllerBase
    {
        private readonly IVendorRepo _vendorRepo;
        private readonly IUserRepo _iUserepo;
        private readonly IMapper _mapper;
        private readonly IOrderRepo _orderRepo;
        private readonly IUserRepo _userRepo;
        private readonly IProductRepo _productRepo;
        private readonly INotificationService _inotificationService;

        public OrderController(IVendorRepo vendorRepo, IUserRepo iUserepo, IMapper _mapper, IOrderRepo _orderRepo,
         IUserRepo userRepo, IProductRepo productRepo, 
        INotificationService inotificationService)
        {
            this._productRepo = productRepo;
            this._userRepo = userRepo;
            this._orderRepo = _orderRepo;
            this._vendorRepo = vendorRepo;
            this._iUserepo = iUserepo;
            this._mapper = _mapper;
            _inotificationService = inotificationService;
        }
        [Authorize("ProdMgt")]
        [HttpGet]
        public async Task<IActionResult> GetOrders([FromQuery] OrderParams orderParams)
        {
            var orders = await _orderRepo.GetAllPaidOrders(orderParams);

            var ordersToReturn = _mapper.Map<IEnumerable<OrderListDto>>(orders);
            var objToreturn = new
            {
                Orders = ordersToReturn,
                Pagination = new
                {
                    CurrentPage = orders.CurrentPage,
                    PageSize = orders.PageSize,
                    TotalCount = orders.TotalCount,
                    TotalPages = orders.TotalPages
                }
            };
            return Ok(objToreturn);

        }

        [HttpGet("{Id}")]
        public async Task<IActionResult> GetOrder(int Id)
        {
            var order = await _orderRepo.GetOrder(Id);
            if (null == order)
                return BadRequest();
            var orderToReturn = _mapper.Map<OrderDetailDto>(order);
            orderToReturn.CartTrackingId = order.Cart.TrackingId;

            return Ok(orderToReturn);

        }

        [Authorize("ProdMgt")]
        [HttpPut("{Id}/pending")]
        public async Task<IActionResult> UpdatePending(int Id, OrderDetailDto orderDto)
        {
            var order = await _orderRepo.GetNoLazyOrder(Id);
            var productOrders = await _orderRepo.GetProductOrdersByOrder(Id);
            if (null == order)
                return BadRequest();

            order.State = "Pending";
            foreach(ProductOrder po in productOrders)
                    {
                        po.State = "Pending";
                    }
            if (await _orderRepo.SaveAll())
                return NoContent();

            return BadRequest();

        }

        [Authorize("ProdMgt")]
        [HttpPut("{Id}/ontheway")]
        public async Task<IActionResult> UpdateOnTheWay(int Id, OrderDetailDto orderDto)
        {
            var order = await _orderRepo.GetNoLazyOrder(Id);
             var productOrders = await _orderRepo.GetProductOrdersByOrder(Id);
            if (null == order)
                return BadRequest();
            order.State = "On The Way";
             foreach(ProductOrder po in productOrders)
                    {
                        po.State = "Picked";
                    }
            if (await _orderRepo.SaveAll())
                return NoContent();

            return BadRequest();

        }

        [Authorize("ProdMgt")]
        [HttpPut("{Id}/delivered")]
        public async Task<IActionResult> UpdateDelivered(int Id, OrderDetailDto orderDto)
        {
            var order = await _orderRepo.GetNoLazyOrder(Id);
             var productOrders = await _orderRepo.GetProductOrdersByOrder(Id);
            if (null == order)
                return BadRequest();
             foreach(ProductOrder po in productOrders)
                {
                        po.State = "Delivered";
                }   
            order.State = "Delivered";
            order.TimeDelivered = DateTime.Now;
            if (await _orderRepo.SaveAll())
                return NoContent();

            return BadRequest();

        }

        [Authorize("ProdMgt")]
        [HttpPut("{Id}/update")]
        public async Task<IActionResult> UpdateOrder(int Id, UpdateOrderDto orderDto)
        {

            var orderToupdate = await _orderRepo.GetNoLazyOrder(Id);
            var productOrders = await _orderRepo.GetProductOrdersByOrder(Id);
            if (null == orderToupdate)
                return BadRequest();

            orderToupdate.RiderId = orderDto.RiderId;
            switch (orderDto.State.ToLower())
            {
                case "delivered":
                    orderToupdate.TimeDelivered = DateTime.Now;
                    orderToupdate.State = "Delivered";
                    foreach(ProductOrder po in productOrders)
                    {
                        po.State = "Delivered";
                    }
                    break;

                case "on the way":
                    orderToupdate.State = "On The Way";
                   foreach(ProductOrder po in productOrders)
                    {
                        po.State = "Picked";
                    }
                    break;
                default:
                    orderToupdate.State = "Pending";
                    foreach(ProductOrder po in productOrders)
                    {
                        po.State = "Pending";
                    }
                    break;

            }

            if (await _orderRepo.SaveAll()){
                //register order notification
                await _inotificationService.SendOrderNotification("Your Order on Quick Waka","Your order have just arrived", orderToupdate, NotificationTopic.CLIENTORDER);
                return Ok();
            }
                

            return BadRequest();
        }

        //Driver update an order product 
        [HttpPut("update/picked")]
        public async Task <IActionResult> ModifyOrderstateReady(UpdateOrderItemDTO productOrder ){
                 var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
                var user = await _iUserepo.GetRider(userId);
                var order = await _orderRepo.GetOrder(productOrder.OrderId);
                if(user.Id != order.RiderId )
                 return BadRequest(new { Error = "You are not authorised to update this order" });
                var productOrders = await _orderRepo.GetProductOrders(productOrder.ProductIds, productOrder.OrderId);
                var PickUpItems = await _orderRepo.GetPickupsByOrderId(productOrder.PickupIds, productOrder.OrderId);
                foreach(PickupItem pu in PickUpItems)
                {
                    pu.State = "Picked";
                }
                 foreach(ProductOrder po in productOrders)
                 {
                    po.State = "Picked";
                 }

                 if(await _orderRepo.SaveAll())
                    return NoContent();

                    return BadRequest();

        }

        //This is a rider method to get non delivered orders 
        [HttpGet("forrider")]
        public async Task<IActionResult> GetAllCarts([FromQuery] DateRangeParam dateRange){

            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            if(null != dateRange.Start && null != dateRange.End)
            {
                var carts = await _orderRepo.GetCartSummaryByRider(userId, dateRange.Start ?? DateTime.Now, dateRange.End ?? DateTime.Now, dateRange.SearchParam);
                return Ok(carts);     
            }else {
            var carts = await  _orderRepo.GetCartSummaryByRider(userId);
            return Ok(carts);            
            }
        }   

        //non delivered orders for rider
        [HttpGet("forrider/today")]
        public async Task<IActionResult> GetAllCartsForToday(){
            var now = DateTime.Now;
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var carts = await _orderRepo.GetCartSummaryByRider(userId, now);
            return Ok(carts);
            
        }

        [HttpGet("forrider/pending")]
        public async Task<IActionResult> GetPendingCartSummaryByRider([FromQuery] DateRangeParam dateRange){
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            if(dateRange != null && dateRange.Start != null && dateRange.End != null)
            {
                var cartToRet = await _orderRepo.GetPendingCartSummaryByRider(userId, dateRange.Start.Value, dateRange.End.Value, dateRange.SearchParam);
                return Ok(cartToRet);     
            }
            var carts = await _orderRepo.GetPendingCartSummaryByRider(userId, dateRange.SearchParam);
            return Ok(carts);
        }

        
        [HttpGet("forrider/delivered")]
        public async Task<IActionResult> GetDeliveredCartSummaryByRider([FromQuery] DateRangeParam dateRange){
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            if(dateRange != null && dateRange.Start != null && dateRange.End != null)
            {
                var cartToRet = await _orderRepo.GetDeliveredCartSummaryByRider(userId, dateRange.Start.Value, dateRange.End.Value, dateRange.SearchParam);
                return Ok(cartToRet);     
            }
            var carts = await _orderRepo.GetDeliveredCartSummaryByRider(userId);
            return Ok(carts);
        }

        //for ridder on the way
         [HttpGet("forrider/ontheway")]
        public async Task<IActionResult> GetOnTheWayCartSummaryByRider([FromQuery] DateRangeParam dateRange){
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
               if(dateRange != null && dateRange.Start != null && dateRange.End != null)
            {
                var cartToRet = await _orderRepo.GetDeliveredCartSummaryByRider(userId, dateRange.Start.Value, dateRange.End.Value, dateRange.SearchParam);
                return Ok(cartToRet);     
            }
            var carts = await _orderRepo.GetOntheWayCartSummaryByRider(userId, dateRange.SearchParam);
            return Ok(carts);
            
        }

        [HttpPut("{Id}/update/delivered")]
        public async Task <IActionResult> UpdateDelivered(int Id, [FromQuery] DateRangeParam dateRange){
                 var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
                var user = await _iUserepo.GetRider(userId);
                var order = await _orderRepo.GetOrder(Id);
                if(user.Id != order.RiderId )
                 return BadRequest(new { Error = "You are not authorised to update this order" });   
                 order.State = "Delivered";

                 if(await _orderRepo.SaveAll())
                    return NoContent();
                    return BadRequest();
        }

        [HttpPut("{Id}/update/ontheway")]
        public async Task <IActionResult> UpdateOntheWay(int Id ){
                 var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
                var user = await _iUserepo.GetRider(userId);
                var order = await _orderRepo.GetOrder(Id);
                
                if(user.Id != order.RiderId )
                 return BadRequest(new { Error = "You are not authorised to update this order" });   
                 order.State = "On The Way";

                 if(await _orderRepo.SaveAll())
                 {
                    
                   await _inotificationService.SendOrderNotification("Your Quick Waka Order","Your oder is on the way",order, NotificationTopic.CLIENTORDER);
                    return NoContent();

                 }
                   
                    return BadRequest();
        }
    
        
        //Rider update an Order product 
        [HttpPut("update/pending")]
        public async Task <IActionResult> ModifyOrderstatePending(UpdateOrderItemDTO productOrder ){
                 var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
                var user = await _iUserepo.GetRider(userId);
                var order = await _orderRepo.GetOrder(productOrder.OrderId);
                if(user.Id != order.RiderId )
                 return BadRequest(new { Error = "You are not authorised to update this order" });

                   var productOrders = await _orderRepo.GetProductOrders(productOrder.ProductIds, productOrder.OrderId);
                var PickUpItems = await _orderRepo.GetPickupsByOrderId(productOrder.PickupIds, productOrder.OrderId);
                foreach(PickupItem pu in PickUpItems)
                {
                    pu.State = "Pending";
                }
                 foreach(ProductOrder po in productOrders)
                 {
                    po.State = "Pending";
                 }

                 if(await _orderRepo.SaveAll())
                    return NoContent();
                    return NoContent();
        }
        
        [HttpPut("cart/{Id}/update/delivered")]
        public async Task <IActionResult> UpdateRiderCartAsDelivered(int Id ){
                 var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
                var Cart = await _orderRepo.GetCart(Id);
                var user = await _iUserepo.GetRider(userId);
                 if(null == Cart)
                    return BadRequest(new { Error = "Cart does not exist" });
                //get orders by cart and rider
                var orders  = await _orderRepo.GetOrderByCartAndRider(Id, userId); 
                foreach(Order order in orders)
                {
                    order.State = "Delivered";
                }
                 if(await _orderRepo.SaveAll())
                    return NoContent();
                    return NoContent();
        }

        [HttpPut("cart/{Id}/update/ontheway")]
        public async Task <IActionResult> UpdateRiderCartAsOntheWay(int Id ){
                 var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
                var user = await _iUserepo.GetRider(userId);
                var Cart = await _orderRepo.GetCart(Id);
                if(null == Cart)
                    return BadRequest(new { Error = "Cart does not exist" });
                var orders = await _orderRepo.GetOrderByCartAndRider(Id, userId);
              foreach(Order order in orders)
              {
                 order.State = "On The Way";
              }
                

                 if(await _orderRepo.SaveAll())
                 {
                    
                   await _inotificationService.SendOrderNotification("Your Quick Waka Order","Your oder is on the way",Cart, NotificationTopic.CLIENTORDER);
                   

                 }
                   
                  return NoContent();
        }
    

        [HttpGet("{cartId}/details")]
        public async Task <IActionResult> GetOrderDetailsForRider(int cartId ){
            //rewrite the code to make irt a list of carts 
            
                var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
                var Cart = await _orderRepo.GetCartDetailsForRider(cartId, userId);
                var pickups = await _orderRepo.GetPickupItemsByRiderAndCart(userId, cartId);
                var objtoreturn  = new CartDetailtViewForRiderDto{
                    FirstName = Cart.Client.FirstName,
                    Lastname = Cart.Client.LastName,
                    Phone = Cart.Client.PhoneNumber,
                    Date = Cart.Date,
                    ClientId = Cart.ClientId,
                    CartId = Cart.Id,
                    PickupItems = _mapper.Map<ICollection<PickupItemViewDto>>(pickups),
                    Vendors = await _orderRepo.GetRiderProductOrdersGroupedByVendors(userId, cartId)
                };

                return Ok(objtoreturn);
        }
    
        //get riders earnings for the week
        [HttpGet("forrider/earnings/week")]
        public async Task<IActionResult> GetRiderEarningsForWeek(){
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            DateTime today = DateTime.Now;
            int daysSinceLastMonday = DateTime.Today.DayOfWeek - DayOfWeek.Monday;
            if (daysSinceLastMonday < 0)
            {
                daysSinceLastMonday += 7; // Go back to the previous week
            }
            DateTime lastMonday = DateTime.Today.AddDays(-daysSinceLastMonday);
                var earnings = await _orderRepo.GetRiderEarningsForDateRange(lastMonday, today, userId);
                var sum = earnings.Sum(e => e.Earninig);

                return Ok(new {
                    Earnings = earnings,
                    Total = sum
                });
        }

         //group riders earning by week 
         [HttpGet("forrider/earnings/week/group")]
        public async Task<IActionResult> GetRiderEarningsGroupedByWeek(){
           
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var earnings = await _orderRepo.GetRiderEarningsGroupedByWeek(userId);
            return Ok(earnings);

        }

        //group riders earning by month
        [HttpGet("forrider/earnings/month/group")]
        public async Task<IActionResult> GetRiderEarningsGroupedByMonth(){
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var earnings = await _orderRepo.GetRiderEarningsGroupedByMonth(userId);
            return Ok(earnings);
        }

        //get riders earnings for the month
        [HttpGet("forrider/earnings/month")]
        public async Task<IActionResult> GetRiderEarningsForMonth(){
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            DateTime today = DateTime.Now;
            DateTime firstDayOfMonth = new DateTime(today.Year, today.Month, 1);
                var earnings = await _orderRepo.GetRiderEarningsForDateRange(firstDayOfMonth
                , today, userId);
                var sum = earnings.Sum(e => e.Earninig);

                return Ok(new {
                    Earnings = earnings
                });
            
        }
   

        //get riders earning for a date range 
        [HttpGet("forrider/earnings")]
        public async Task<IActionResult> GetRiderEarningsForMonth([FromQuery] OrderParams orderParams){
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var earnings = await _orderRepo.GetRiderEarningsForDateRange(orderParams.FromDate ?? DateTime.Now, orderParams.ToDate ?? DateTime.Now, userId);
       
                var sum = earnings.Sum(e => e.Earninig);

                return Ok(new {
                    Earnings = earnings,
                    Total = sum
                });
        }
   
    }
}