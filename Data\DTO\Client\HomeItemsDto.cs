using System.Collections.Generic;
using QuickMessenger.API.Data.DTO.Product;
using QuickMessenger.API.Data.DTO.Vendor;

namespace QuickMessenger.API.Data.DTO.Client
{
    public class HomeItemsDto
    {
        public ICollection<ServiceDto2> Services { get; set; }
        public  ICollection<VendorListDtoLite> Vendors { get; set; }
    }

    public class SearchResultDto
    {
        public ICollection<ProductListForClientDto> Products { get; set; }
        public ICollection<VendorListDtoLite> Vendors { get; set; }
    }

    
}