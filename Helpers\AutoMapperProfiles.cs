using System.Text.RegularExpressions;
using AutoMapper;
using QuickMessenger.API.Data.DTO.Vendor;
using QuickMessenger.API.Data.DTO.Client;
using QuickMessenger.API.Data.DTO.Product;
using QuickMessenger.API.Data.DTO.Shared;
using QuickMessenger.API.Data.DTO.Staff;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Data.Sample;
using static Google.Apis.Auth.GoogleJsonWebSignature;

namespace QuickMessenger.API.Helpers
{
    public class AutoMapperProfiles: Profile
    {
        public AutoMapperProfiles()
        {
            #region Remove From Production
            CreateMap<SampleStaff,Admin>();
            CreateMap<SampleStaff,Rider>();
            CreateMap<SampleStaff,FrontDesk>();
            CreateMap<SampleStaff,Client>();
            #endregion

            CreateMap<SuperAdmin, UserDetailDto>();

            CreateMap<User, StaffDetailDto>().ForMember(dest => dest.Role, opt => 
            {
                opt.MapFrom(src => src.GetType().Name);
            }).ForMember(dest => dest.Phone, 
                        opt => {
                            opt.MapFrom(src => src.PhoneNumber);
                            });
            CreateMap<Client, ClientListDto>();
            CreateMap<Client, ClientDetailDto>();
            CreateMap<User, ClientDetailDto>();
             CreateMap<Client, UserDetailDto>().ForMember(dest => dest.Phone, opt => {
                  opt.MapFrom(src => src.PhoneNumber);
              });
             CreateMap<UserDetailDto, Client>();
             CreateMap<UserDetailDto, User>();
             CreateMap<ClientDetailViewDto, User>();
             CreateMap<User, ClientDetailViewDto>();
              CreateMap<User, UserDetailDto>().ForMember(dest => dest.Phone, opt => {
                  opt.MapFrom(src => src.PhoneNumber);
              });
            CreateMap<Rider, RiderListLiteDto>();

            CreateMap<ClientDetailDto, Client>().ForMember(dest => dest.SearchParam, opt =>{
                opt.MapFrom( src => $"{src.LastName} {src.FirstName} {src.Email} {src.Role} {src.PhoneNumber}".ToUpper());
            }).ForMember(dest => dest.UserName, 
                        opt => {
                            opt.MapFrom(src => src.Email);
                            });
            CreateMap<ClientRegisterDto, Client>().ForMember(dest => dest.SearchParam, opt =>{
                opt.MapFrom( src => $"{src.LastName} {src.FirstName} {src.Email} {src.Phone}".ToUpper());
            }).ForMember(dest => dest.PhoneNumber, 
                        opt => {
                            opt.MapFrom(src => src.Phone);
                            }).ForMember(dest => dest.UserName, 
                        opt => {
                            opt.MapFrom(src => src.Email);
                            });;
            CreateMap<Address, AddressDto>();
            CreateMap<Address, AddressDto2>();
            CreateMap<AddressDto2, Address>().ForMember(dest => dest.SearchParam, opt =>{
                opt.MapFrom( src => $"{src.LastName} {src.FirstName} {src.Street} {src.City} {src.State}".ToUpper());
            });
            
            CreateMap<AddressDto, Address>();
            CreateMap<StaffDetailDto, Admin>().ForMember(dest => dest.SearchParam, opt =>{
                opt.MapFrom( src => $"{src.LastName} {src.FirstName} {src.Email} {src.Role} {src.Phone}".ToUpper());
            }).ForMember(dest => dest.PhoneNumber, 
                        opt => {
                            opt.MapFrom(src => src.Phone);
                            }).ForMember(dest => dest.UserName, 
                        opt => {
                            opt.MapFrom(src => src.Email);
                            });
            CreateMap<StaffDetailDto, FrontDesk>().ForMember(dest => dest.SearchParam, opt =>{
                opt.MapFrom( src => $"{src.LastName} {src.FirstName} {src.Email} {src.Role} {src.Phone}".ToUpper());
            }).ForMember(dest => dest.PhoneNumber, 
                        opt => {
                            opt.MapFrom(src => src.Phone);
                            }).ForMember(dest => dest.UserName, 
                        opt => {
                            opt.MapFrom(src => src.Email);
                            });
            CreateMap<StaffDetailDto, Rider>().ForMember(dest => dest.SearchParam, opt =>{
                opt.MapFrom( src => $"{src.LastName} {src.FirstName} {src.Email} {src.Role} {src.Phone}".ToUpper());
            }).ForMember(dest => dest.PhoneNumber, 
                        opt => {
                            opt.MapFrom(src => src.Phone);
                            }).ForMember(dest => dest.UserName, 
                        opt => {
                            opt.MapFrom(src => src.Email);
                            });

                            CreateMap<StaffDetailDto, User>().ForMember(dest => dest.PhoneNumber, 
                        opt => {
                            opt.MapFrom(src => src.Phone);
                            }).ForMember(dest => dest.UserName, opt => {
                                opt.MapFrom(src => src.Email);
                            });
            CreateMap<Prod_CategoryDto, Prod_Category>();
            CreateMap<Prod_CategoryForUpdateDto, Prod_Category>().ForMember(dest => dest.SearchParam, opt => {
                opt.MapFrom(src => $"{src.Name} {src.NameId} {src.Description}".ToUpper().Trim());
            });
            CreateMap<Prod_Category, Prod_CategoryDto>();
            CreateMap<OrderKpiDto, DailyOrderKpiDto>();
            CreateMap<OrderKpiDto, HourlyOrderKpiDto>();
            CreateMap<OrderKpiDto, RangeOrderKpiDto>();
            CreateMap<OrderKpiDto, MonthOrderKpiDto>();
            CreateMap<ServiceDto, Service>().ForMember(dest => dest.NameId, opt => {
                opt.MapFrom(src => src.Name.GenerateValidNameId());
            }).ForMember(dest => dest.SearchParam, opt=> {
                opt.MapFrom(service => $"{service.Name} {service.NameId}".RemoveSpecialCharatcters().ToUpper().Trim());
            });
            
            CreateMap<Service, ServiceDto>();
            CreateMap<Service, ServiceDto2>();
            CreateMap<Service, ServiceListDto>();
            CreateMap<Service, ServiceLiteDto>();
            CreateMap<Service, ServiceLiteDto2>();
            CreateMap<Prod_Category, Prod_CategoryListDto>();
            CreateMap<Prod_Category, Prod_CategoryListDtoLite>();
            CreateMap<Prod_Category, Prod_CategoryListDtoLiteParent>();
            CreateMap<Prod_Category, Prod_CategoryListDtoLite2>();
            CreateMap<Prod_Category, Prod_CategoryListDtoLiteParent2>();
            CreateMap<Prod_MeasurementTypeDto, Prod_MeasurementType>().ForMember( dest => dest.SearchParam, opt => {
                opt.MapFrom(src => $"{src.Name} {src.Description} {src.Symbol}".ToUpper());
            });
            CreateMap<Prod_MeasurementType, Prod_MeasurementTypeDto>();
            CreateMap<Prod_PropertyType, Prod_PropertyTypeDto>();
            CreateMap<Prod_PropertyTypeDto, Prod_PropertyType>().ForMember( dest => dest.SearchParam, opt => {
                opt.MapFrom(src => $"{src.Name} {src.Description}".ToUpper());
            });
            CreateMap<Prod_PropertyDto, Property>();
            CreateMap<Property, Prod_PropertyDto>().ForMember(dest => dest.PropertyTypeName, 
            opt => {
                opt.MapFrom(src => src.Prod_PropertyType.Name);
            }).ForMember(dest => dest.MeasurementTypeName, opt=> {
                opt.MapFrom(src => src.Prod_MeasurementType.Name);
            }).ForMember(dest => dest.MeasurementTypeSymbol, opt => {
                opt.MapFrom(src => src.Prod_MeasurementType.Symbol);
            }).ForMember(dest => dest.CanDelete, opt => {
                opt.MapFrom(src => !(src.ProductProperties.Count > 0));
            });
            CreateMap<Vendor, VendorDto>();
            CreateMap<Vendor, VendorDtoLite>().ForMember(dest => dest.ImageUrl, opt => {
                opt.MapFrom(src => src.Logo.Url);
            }).ForMember(dest => dest.Address, opt => {
                opt.MapFrom(src => $"{src.Address.City}, {src.Address.State}");
            });
            CreateMap<Vendor, VendorListDto>();
            CreateMap<Vendor, VendorListDtoLite>().ForMember(dest => dest.Address, opt => {
                opt.MapFrom(src => $"{src.Address.City}, {src.Address.Country}");
            }).ForMember(dest => dest.ImageUrl, opt =>{
                opt.MapFrom(src => src.Logo.Url);
            });
            CreateMap<VendorDto, Vendor>().ForMember( dest => dest.SearchParam, opt => {
                opt.MapFrom(src => $"{src.Name} {src.Email} {src.Phone} {src.Phone2} {src.Address.Street} {src.Address.City} {src.Address.State}".ToUpper());
            }).ForMember(dest => dest.UniqueParam, opt => {
                opt.MapFrom(vendor =>  $"{vendor.Name}{vendor.Address.Country}{vendor.Address.State}{vendor.Address.City}{vendor.Address.Street}"
                .RemoveSpacesAndSpecialCharatcters().ToUpper());
            } );
            CreateMap<Order, OrderListDto>().ForMember(dest => dest.ClientName, opt => {
                opt.MapFrom(order => $"{order.Client.FirstName} {order.Client.LastName}" );
            }).ForMember(dest => dest.Time, opt => {
                opt.MapFrom(order => order.Date );
            }).ForMember(dest=> dest.RiderName, opt => {
                opt.MapFrom(order => $"{order.Rider.FirstName} {order.Rider.LastName}" );
            }).ForMember(dest=> dest.Service, opt => {
                opt.MapFrom(order => $"{order.Service.Name}" );
            }).ForMember(dest => dest.ItemsNames, opt => {
                opt.MapFrom(order => order.GetItemsNamesFromList());
            }).ForMember(dest => dest.NumberOfItems, opt => {
                opt.MapFrom(order => order.GetNumberOfProducts());
            });
            
            CreateMap<Order, OrderDetailDto>();
            CreateMap<Order, OrderListAppViewDto>().ForMember(p => p.Date, opt => {
                opt.MapFrom(o => o.Date.GetLocalTime() );
            }).ForMember(p => p.TimeDelivered, opt => {
                opt.MapFrom(o => o.TimeDelivered.GetLocalTime());
            });
            CreateMap<ProductOrder, ProductOrderDto>().ForMember(p => p.Date, opt => {
                opt.MapFrom(o => o.Order.Date.GetLocalTime());});

            CreateMap<ProductOrderDto2, ProductOrder>();
            CreateMap<ProductOrder, ProductOrderDto2>();
            CreateMap<ServiceOrdersForCreateDto, Order>();
            CreateMap<Order, ServiceOrdersForViewDto>().ForMember(p => p.Subtotal, opt => {
                opt.MapFrom(o => o.Cost - o.DeliveryCharge - o.ServiceCharge);});
;
            CreateMap<Order, ServiceOrdersForViewDto2>();
            CreateMap<ServiceOrdersForCreateDto, ServiceOrdersForCreateDtoLazy>();
            CreateMap<ServiceOrdersForCreateDtoLazy, Order>();

            CreateMap<Order, ServiceOrdersForCreateDto>();
            CreateMap<CartForCreateDto, Cart>();
            CreateMap<Cart, CartForViewDto>();
            CreateMap<Cart, CartForViewDto2>();
            CreateMap<Cart, CartForCreateDto>();
            CreateMap<Address, Address>();
            CreateMap<SampleOrder, Order>();
            CreateMap<PickupItem, PickupItemDto>();
            CreateMap<PickupItemDto2, PickupItem>();
             CreateMap<PickupItem, PickupItemDto2>();
             CreateMap<PickupItem, PickupItemViewDto>();
            CreateMap<PhotoDto, Photo>();
            CreateMap<Photo, PhotoDto>();
            CreateMap<Order, OrderDetailDto2>();
            CreateMap<Product, ProductDto>().ForMember( dest => dest.CanDelete, opt => {
                opt.MapFrom(p => p.ProductOrders.Count == 0);
            });
            CreateMap<ProductDto, Product>().ForMember(dest => dest.UniqueParam, opt => {
                opt.MapFrom(src => src.Name.RemoveSpecialCharatcters().ToUpper());
            });
            CreateMap<ProductForCreateDto, Product>().ForMember(dest => dest.UniqueParam, opt => {
                opt.MapFrom(src => src.Name.RemoveSpecialCharatcters().ToUpper());
            });
            CreateMap<ProductPropertyDto, ProductProperty>().ForMember(dest => dest.NValue, opt => {
                opt.MapFrom(src => src.getValue() == 0 ? (int?)null: src.getValue() );
            }).ForMember(dest => dest.Value, opt => {
                    opt.MapFrom(src => src.getValue() == 0? src.Value: null);
            });

            CreateMap<ProductProperty, ProductPropertyDto>();
            
            CreateMap<Product, ProductListDto>().ForMember(dest => dest.PictureUrl, opt => {
                opt.MapFrom(src => src.Photos.GetFirstElement().Url);
            });
            CreateMap<Product, ProductListForClientDto>().ForMember(dest => dest.PictureUrl, opt => {
                opt.MapFrom(src => src.Photos.GetFirstElement().Url);
            });

            CreateMap<User, UserDetailDto>().ForMember(d => d.Phone, opt => {
                opt.MapFrom(src => src.PhoneNumber);
            });
            CreateMap<FacebookUserDto, UserDetailDto>();
            CreateMap<Payload, UserDetailDto>().ForMember(dest => dest.FirstName, opt => {
                opt.MapFrom(src => src.Name.Split(new[] {' '})[0]);
            }).ForMember(dest => dest.LastName,  opt => {
                opt.MapFrom(src => src.Name.Split(new[] {' '})[1]);
            });
            CreateMap<Rider, RiderDetailViewDto>();
        }
    }
}