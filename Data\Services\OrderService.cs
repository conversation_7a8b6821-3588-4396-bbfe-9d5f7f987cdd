using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using QuickMessenger.API.Data.DTO.Staff;
using QuickMessenger.API.Data.DTO.ThirdParty;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.IService;
using QuickMessenger.API.Data.Model;

namespace QuickMessenger.API.Data.Services
{
    public class OrderService : IOrderService
    {
        private readonly IOrderRepo _orderRepo;
        private readonly IMapper _mapper;
        private readonly IUserRepo _userepo;
        private readonly IProductRepo _productRepo;
        private readonly IGeoService _geoService;
        private readonly IPhotoService _photoService;
        private readonly IUserService _userService;

        public OrderService(IOrderRepo orderRepo, IMapper mapper, IUserRepo userepo,

        IProductRepo productRepo, IGeoService geoService, IPhotoService photoService,  IUserService userService)
        {
            this._photoService = photoService;
            this._userService = userService;
            this._geoService = geoService;
            this._productRepo = productRepo;
            _userepo = userepo;
            _mapper = mapper;
            _orderRepo = orderRepo;
            
        }

    public OrderKpiDto GetOrderKpiReport(ICollection<Order> Orders)
    {
        //TODO: Remember to make task if need be
        //  return  Task.Run(() => {
        var orderValues = Orders.Sum(o => o.Cost);
        double ProductValue = 0;
        var Riders = _userepo.GetAllActiveRiders();
        foreach (var order in Orders)
        {
            if (order.ProductOrders.Count > 0)
                ProductValue += order.ProductOrders.Sum(PO => PO.Quantity * PO.Product.Price);
            if (order.PickupItems.Count > 0)
                ProductValue += order.PickupItems.Sum(PU => PU.Value);
        }
        var Delivered = Orders.Where(o => !String.IsNullOrEmpty(o.State) && "Delivered".CompareTo(o.State) == 0).ToList();
        double deliveredTimeSum = Delivered.Sum(d => (d.TimeDelivered - d.Date).TotalMinutes);
        var Pending = Orders.Where(o => !String.IsNullOrEmpty(o.State) && o.State.CompareTo("Pending") == 0).ToList();
        var deliveryResponseTime = deliveredTimeSum / Delivered.Count;
        deliveryResponseTime = Double.IsNaN(deliveryResponseTime) ? 0 : Math.Round(deliveryResponseTime, 2);
        var orderPerRider = (Double)Orders.Count / Riders.Result.Count;
        var OrderReport = new OrderKpiDto
        {
            Total = Orders.Count,
            Delivered = Delivered.Count,
            Pending = Pending.Count,
            OnTheWay = Orders.Count - Delivered.Count - Pending.Count,
            Value = ProductValue + orderValues,
            AverageOrderPerRider = Math.Round(orderPerRider, 3),
            AverageOrderResponseTime = deliveryResponseTime
        };

        return OrderReport;
        // });

    }

    public async Task<ICollection<OrderKpiDto>> GetChartOrderReportWithDateRange(DateTime start, DateTime end)
    {
        List<OrderKpiDto> reports = new List<OrderKpiDto>();
        int count = (int)(end - start).TotalDays;
        for (int i = 0; i <= count; i++)
        {
            var Date = start.AddDays(i);
            var Orders = await _orderRepo.GetOrdersByDate(Date);
            var report = GetOrderKpiReport(Orders);
            var dailyOrerReport = _mapper.Map<OrderKpiDto>(report);
            dailyOrerReport.DayOfTheWeek = Date.DayOfWeek.ToString();
            reports.Add(dailyOrerReport);
        }

        return reports;

    }
    public async Task<DailyOrderKpiDto> GetOrderKpiReportWithDate(DateTime Date)
    {
        var Orders = await _orderRepo.GetOrdersByDate(Date);
        var report = GetOrderKpiReport(Orders);
        var dailyOrerReport = _mapper.Map<DailyOrderKpiDto>(report);
        dailyOrerReport.Date = Date;
        return dailyOrerReport;
    }

    public async Task<RangeOrderKpiDto> GetOrderKpiReportWithDateRange(DateTime start, DateTime end)
    {
        var Orders = await _orderRepo.GetOrdersByDateRange(start, end);
        var report = GetOrderKpiReport(Orders);

        var dateRangeReport = _mapper.Map<RangeOrderKpiDto>(report);
        dateRangeReport.StartDate = start;
        dateRangeReport.EndDate = end;
        dateRangeReport.AverageDailyOrderDelivered = (int)(report.Delivered / (end - start).TotalDays);
        return dateRangeReport;

    }
    public async Task<MonthOrderKpiDto> GetOrderKpiReportWithMonth(int year, int month)
    {
        int days = 1;
        switch (month)
        {
            case 1:
            case 3:
            case 5:
            case 7:
            case 8:
            case 10:
            case 12:
                days = 31;
                break;
            case 4:
            case 6:
            case 9:
            case 11:
                days = 30;
                break;
            default:
                if (year % 4 == 0)
                    days = 28;
                else
                    days = 27;
                break;
        }
        var today = new DateTime();
        if (today.Month == month)
            days = today.Day;

        var orders = await _orderRepo.GetOrdersWithMonth(year, month);
        var report = GetOrderKpiReport(orders);
        var dateRangeReport = _mapper.Map<MonthOrderKpiDto>(report);
        var newDate = new DateTime(year, month, 1);
        dateRangeReport.Month = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(newDate.Month);
        dateRangeReport.AverageDailyOrderDelivered = (int)(report.Delivered) / days;
        return dateRangeReport;
    }

    public async Task<ICollection<HourlyOrderKpiDto>> GetHourlyOrderKpiReportWithDate(DateTime Date)
    {
        var Orders = await _orderRepo.GetOrdersByDate(Date);
        var hours = Orders.Select(o => o.Date.Hour).ToList().Distinct();
        List<HourlyOrderKpiDto> reports = new List<HourlyOrderKpiDto>();
        foreach (int h in hours)
        {
            var orders = Orders.Where(o => o.Date.Hour == h).ToList();
            var report = GetOrderKpiReport(orders);
            var dailyOrerReport = _mapper.Map<HourlyOrderKpiDto>(report);
            dailyOrerReport.Hour = h;
            reports.Add(dailyOrerReport);
        }
        return reports;
    }

    public async Task<double> GetTotalCartCost(Cart Cart)
        {
            Cart.Cost = 0;
            bool dontCalculateCharge = false;
            foreach (Order order in Cart.Orders)
            {

                List<Address> addressList = new List<Address>();
                var add = await _userepo.GetAddress(order.AddressId ?? 0);
                if(null == add)
                dontCalculateCharge = true;
                addressList.Add(add);
                order.DeliveryCharge = 0;
                if (null != order.PickupItems && order.PickupItems.Count > 0)
                {
                    foreach (PickupItem pickup in order.PickupItems)
                    {

                        var pickupAdd = await _userepo.GetAddress(pickup.AddressId ?? 0);
                        if(null == pickupAdd)
                        dontCalculateCharge = true;
                        addressList.Add(pickupAdd);
                    }
                }

                if (null != order.ProductOrders && order.ProductOrders.Count > 0)
                {
                    foreach (ProductOrder po in order.ProductOrders)
                    {
                        var product = await _productRepo.GetProductWithVendor(po.ProductId);
                        if (null != product.Vendor)
                        {
                            addressList.Add(product.Vendor.Address);
                        }
                        order.Cost += product.Price * po.Quantity;
                    }
                }
                if(!dontCalculateCharge)
                order.DeliveryCharge = await _geoService.GetErrandPricing(addressList);
                var service  = await _productRepo.GetService(order.ServiceId);
                order.ServiceCharge = service.ServiceCharge;
                order.Cost = order.Cost + order.DeliveryCharge + order.ServiceCharge;
                order.RiderEarning = 0.4 * order.DeliveryCharge; 
                Cart.Cost += order.Cost;
            }
            return Cart.Cost;
        }

    public async Task<bool> Checkout(IFlutterData flutter, int CartId){
        
                var Cart = await _orderRepo.GetCart(CartId);
                if (flutter.data.status.CompareTo("successful") == 0 
                    && flutter.data.amount >= (Cart.Cost) 
                    && flutter.data.customer.email.CompareTo(Cart.Client.Email) == 0
                    && flutter.data.currency.CompareTo("NGN") == 0
                    && flutter.data.tx_ref.CompareTo(Cart.Tx_ref) == 0
                    // && paystack.data.domain.CompareTo("test") != 0
                    // TODO last check for cartid with the returned cart id && 
                )
                {
                    Cart.State = "Paid";
                    Cart.DatePaid = DateTime.Now;

                    if(await _orderRepo.SaveAll())
                    {
                        // _orderRepo.DetachAllEntities();
                        var Orders = await _orderRepo.GetOrdersByCart(Cart.Id);
                        foreach (Order order in Orders)          
                        {
                            var address = await _userepo.GetAddress(order.AddressId ?? 0);
                            if(null != address)
                            {
                                order.SearchParam += $"{address.City} {address.Street} {address.State} {address.LastName} {address.FirstName} {address.Phone}";
                                Address add = _mapper.Map<Address>(address);
                                add.Id = 0;
                                add.User = null;
                                add.UserId = null;
                                order.Address = add;
                                //add
                            }
                            order.State = "Pending";
                            // var pickupItems = _orderRepo.get
                            if(null != order.PickupItems && order.PickupItems.Count > 0) 
                            {
                                foreach(PickupItem pi in order.PickupItems)
                                {
                                    var pickupAddress = await _userepo.GetAddress(pi.AddressId ?? 0);
                                    if(null != pickupAddress)
                                    {
                                        pi.AddressId = 0;
                                        var add = _mapper.Map<Address>(pickupAddress);
                                        add.Id = 0;
                                        add.User = null;
                                        add.UserId = null;
                                        pi.Address = add;
                                    }
                                    
                                }
                            }

                            if(null != order.ProductOrders && order.ProductOrders.Count > 0)
                            {
                                foreach(ProductOrder po in order.ProductOrders)
                                {
                                    po.State = "Pending";
                                }
                            }
                            order.Date = DateTime.Now;

                        }
                       if (await _orderRepo.SaveAll())
                       {
                            await  _userService.SendClientOrderConfirmationEmail(Cart.Client, Cart, Orders);
                            return true;
                       }
                        
                    }
                    

                }
                return false;
    }

    public async Task<double> GetTotalCartCost(int Id)
    {
        var cart = await _orderRepo.GetCart(Id);
        return cart.Cost;
    }

    public async Task<bool> AddPickupPhoto(IFormFile file, int Id, string Mpath, int maxPhotos)
    {
        var pickup = await _orderRepo.GetPickupWithPhotos(Id);
        var productPhotos = pickup.Photos;
        var Path = $"{Mpath}_{pickup.Id}";
        if (productPhotos != null && productPhotos.Count >= maxPhotos)
            return false;

        if (productPhotos != null && productPhotos.Count > 0)
        {
            Path = Path + "_" + _photoService.GetNextIndex(maxPhotos, productPhotos);
        }
        else Path = Path + "_1";
        
        string url = await _photoService.AddPhoto(file, Path);
        Photo photo = new Photo
        {
            PublicId = Path,
            DateAdded = DateTime.Now,
            PickupItem = pickup,
            PickupItemId = pickup.Id,
            Url = url
        };
        _orderRepo.Add(photo);
        return await _orderRepo.SaveAll();

    }
}
}