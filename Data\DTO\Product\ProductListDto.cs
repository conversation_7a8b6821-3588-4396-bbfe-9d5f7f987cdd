
using QuickMessenger.API.Data.DTO.Vendor;

namespace QuickMessenger.API.Data.DTO.Product
{
    public class ProductListDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public double price { get; set; }
        public Prod_CategoryListDtoLite Prod_Category { get; set; }
        public VendorListDtoLite Vendor { get; set; }
        public bool OutOfStock { get; set; }
        public string PictureUrl { get; set; }

    }

    public class ProductListForClientDto: ProductListDto{
        public ServiceLiteDto Service { get; set; }
        public string PictureUrl { get; set; }
    }





}