using System;

namespace QuickMessenger.API.Data.DTO.Staff
{
    public class DashBoardDto
    {
        
    }

    public class OrderKpiDto
    {
        public int Total { get; set; }
        public int Delivered { get; set; }
        public int Pending { get; set; }
        public int OnTheWay { get; set; }
        public double Value { get; set; }
        public string DayOfTheWeek { get; set; }
         public double AverageOrderPerRider { get; set; }
         //in minutes
         public double AverageOrderResponseTime { get; set; }
    }

    public class DailyOrderKpiDto: OrderKpiDto
    {
        public DateTime Date { get; set; }
    }

    public class HourlyOrderKpiDto: OrderKpiDto
    {
        public int Hour { get; set; }
    }

    public class RangeOrderKpiDto: OrderKpiDto
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int AverageDailyOrderDelivered { get; set; }
    }

    public class MonthOrderKpiDto: OrderKpiDto
    {
       public string Month { get; set; }
       public int AverageDailyOrderDelivered { get; set; }
    }

    public class OrdersByLocationList{
        public string Country { get; set; }
        public string State { get; set; }
        public string City { get; set; }
        public int Orders { get; set; }
    }

    public class OrdersByVendorList{
        public int  Id { get; set; }
        public string photoUrl { get; set; }
        public string Name { get; set; }
        public string City { get; set; }
        public int Orders { get; set; }
    }

     public class OrdersByProductList{
        public int  Id { get; set; }
        public string photoUrl { get; set; }
        public string Category { get; set; }
        public string Name { get; set; }
        public int Orders { get; set; }
    }

      public class OrdersByRiderList{
        public int  Id { get; set; }
        public string photoUrl { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public int Orders { get; set; }
    }
    
}