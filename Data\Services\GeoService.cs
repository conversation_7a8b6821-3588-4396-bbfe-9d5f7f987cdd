using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using QuickMessenger.API.Data.DTO.Shared;
using QuickMessenger.API.Data.IService;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Enums;
using QuickMessenger.API.Helpers;

namespace QuickMessenger.API.Data.Services
{
    public class GeoService : IGeoService
    {
        private readonly IConfiguration _config;
        public GeoService(IConfiguration config)
        {
            this._config = config;

        }
        
        public async Task<double> GetErrandPricing(List<Address> Addresses)
        {

            if(Addresses.Count < 2)
            return Int32.Parse(_config.GetSection("DeliveryCharge:baseCharge").Value);
            //TODO we need to add an address that will be used to measure relative distance of a deliveery
            var mapApiKey = _config.GetSection("GoogleMapApi:ApiKey").Value;
            string origins   = $"{OfficeAddresses.Addresses["abakaliki"]}|";
            string destinations =  $"{OfficeAddresses.Addresses["abakaliki"]}|";
            for(int i = 0; i < Addresses.Count; i++)
            {
                destinations += $"{Addresses[i].Street} {Addresses[i].City} {Addresses[i].State}|";
                origins += $"{Addresses[i].Street} {Addresses[i].City} {Addresses[i].State}|";
            }
             origins = origins.Remove(origins.Length-1);
             destinations = destinations.Remove(destinations.Length-1);

            var path = "https://maps.googleapis.com/maps/api/distancematrix/json?mode=walking&key="
                         + mapApiKey + "&origins=" +  origins + "&destinations=" + destinations ;

            var client = new HttpClient();
            var uri = new Uri(path);
            var response = await client.GetAsync(uri);
            DistanceMatrix distancematrix = null;
             if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                distancematrix = Newtonsoft.Json.JsonConvert.DeserializeObject<DistanceMatrix> (content);
            }

            return CalculateDeliveryCharge(distancematrix);
        }

        private double CalculateDeliveryCharge(DistanceMatrix distanceMatrix){

            int baseCharge = Int32.Parse(_config.GetSection("DeliveryCharge:baseCharge").Value);
            int baseDistance = Int32.Parse(_config.GetSection("DeliveryCharge:baseDistance").Value);
            int chargePerStop = Int32.Parse(_config.GetSection("DeliveryCharge:chargePerStop").Value);
            int subsequentBaseCharge = Int32.Parse(_config.GetSection("DeliveryCharge:subsequentBaseCharge").Value);
            int subsequentBaseDistance = Int32.Parse(_config.GetSection("DeliveryCharge:subsequentBaseDistance").Value);
           /*
           Get the farthest distance from the destination and then 
           subsequently get the farthest distance from each stops until all stops have been reached
           */
            int totalDistance = 0;
            int rows = distanceMatrix.rows.Count;
            if(distanceMatrix.rows.Count < 1)
            return 0;
            List<int> destinationsChecked = new List<int>();
            destinationsChecked.Add(0);
           
                int rowmaximumIdex = 0;
                int count = 0;
                int columns = rows;
                //loop thru the elements of the first row and find the path to destination that has the farthest distance 
                while(count < rows)
                {
                    int rowMaximum = 0;
                    int tempMaximumIndex = rowmaximumIdex;
                    for(int j = 0; j<columns; j++)
                    {
                        if(rowMaximum < distanceMatrix.rows[tempMaximumIndex].elements[j].distance.value && !destinationsChecked.Contains(j))
                        {
                            rowmaximumIdex = j;
                            rowMaximum = distanceMatrix.rows[tempMaximumIndex].elements[j].distance.value;
                        } 
                    }
                    destinationsChecked.Add(rowmaximumIdex);
                    totalDistance += rowMaximum;
                    count++;
                }

                int unitDistance = totalDistance / baseDistance;
                bool distanceNotUpToBase = false;
                if (unitDistance == 0)
                {
                    distanceNotUpToBase= true;
                    unitDistance = 1;
                }
                double distanceCharge = unitDistance * baseCharge;
                int rem = totalDistance % baseDistance;
                if( rem > 0 && !distanceNotUpToBase)
                {
                    int subsequentUnitDistance = rem / subsequentBaseDistance;
                    subsequentUnitDistance += 1;
                    distanceCharge += (subsequentUnitDistance * subsequentBaseCharge);
                }
                double stopCharge = chargePerStop * Math.Max((rows - 2),0);

            // get the combined distance from all stops
            return (distanceCharge + stopCharge);
        }
    }
}