using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace QuickMessenger.API.Data.Model
{
    public partial class  Address
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Phone { get; set; }
        public string Street { get; set; }
        public string Country { get; set; }
         public string State { get; set; }
         public string City {get; set;}
         public int? UserId { get; set; }
         public User User { get; set; }
         public int Id { get; set; }
         public Vendor Vendor { get; set; }
         public int? VendorId { get; set; }
         public ICollection<Order> Orders { get; set; }
         public ICollection<PickupItem> PickupItems { get; set; }
         public string SearchParam { get; set; }
         //longitude and latitude
        public double Longitude { get; set; }
        public double Latitude { get; set; }

    }
}