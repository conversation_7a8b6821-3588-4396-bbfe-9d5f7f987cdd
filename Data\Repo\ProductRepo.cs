using System;
using System.Collections.Generic;
using System.Linq;
// using System.Linq.Async;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using QuickMessenger.API.Data.DTO.Product;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Helpers;
using QuickMessenger.API.Helpers.Params;

namespace QuickMessenger.API.Data.Repo
{
    public class ProductRepo : IProductRepo
    {
        private readonly DataContext _context;
        private readonly IMapper _mapper;
        public ProductRepo(DataContext context, IMapper mapper)
        {
            _mapper = mapper;
            _context = context;

        }
        public void Add<T>(T entity) where T : class
        {
            //use AddCategory(Prod_Category category) to Add Categories
            if(typeof(T) == typeof(Prod_Category))
            throw new System.ArgumentException();

            _context.Add(entity);
        }

        public void AddCategory(Prod_Category category)
        {
             category.SearchParam = $"{category.Name} {category.NameId} {category.Description} ".ToUpper();
            _context.Add(category);
        }
        public void AddAll<T>(IEnumerable<T> entities) where T : class
        {
            _context.AddRange(entities);
        }
        public async Task<bool> AnyPropertyExists(IEnumerable<Property> Properties, Prod_Category Category, List<int> IdsToBeDeleted)
        {
           foreach(Property pp in Properties)
           {
                if( await _context.Properties
                        .AsQueryable().AnyAsync( p => p.Prod_CategoryId == Category.Id && 
                        p.Prod_PropertyTypeId == pp.Prod_PropertyTypeId
                        && (p.Prod_MeasurementTypeId == pp.Prod_MeasurementTypeId
                        ) &&
                        (!IdsToBeDeleted.Contains(p.Id))
                    ))
             return true;
           }
           return false;
        }
        public async Task<bool> AnyPropertyHasRecord(List<int> propertyIds)
        {
            return await _context.ProductProperties.AsQueryable().AnyAsync( p => propertyIds
            .Contains(p.PropertyId)) ;
        }
        public async  Task<bool> CategoryHasRecords(int Id)
        {
            var hasRecords = await _context.Prod_Categories.AsQueryable().Where(p => p.ParentId == Id).AnyAsync() ||
                    await _context.ProductProperties.AsQueryable().Where(pp => pp.Property.Prod_CategoryId == Id).AnyAsync()||
                    await _context.Products.AsQueryable().Where(p => p.Prod_CategoryId == Id).AsQueryable().AnyAsync();
            return hasRecords;
        }
        public async Task<bool> CategoryNameIdExists(string NameId)
        {
           var val = 
           await _context.Prod_Categories.AsQueryable().AsQueryable().AnyAsync(pc => NameId.CompareTo(pc.NameId) ==0 );
           return val;
        }
        public async Task<bool> CategoryNameIdExists(string NameId, int Id)
        {
            return await _context.Prod_Categories
            .AsQueryable().AnyAsync(pc => pc.NameId.CompareTo(NameId)== 0 && pc.Id != Id);
        }
        public void Delete<T>(T entity) where T : class
        {
            _context.Remove(entity);
        }
        public void DeleteAll<T>(IEnumerable<T> entities) where T : class
        {
            _context.RemoveRange(entities);
        }
        public async Task<ICollection<Prod_Category>> GetCanBeParentCategories()
        {
            var categories =  _context.Prod_Categories
            .AsEnumerable().Where(p => 
                p.NameId.Count(c => c =='/') < 3).OrderBy(c => c.Name)
            .ToList();
            
            return categories;
            
        }
        public async Task<PagedList<Prod_Category>> GetCategories(Prod_CategoryParams ProdCatParam)
        {
           // var categories = _context.Prod_Categories.Include(p => p.Parent);
          
            if(!string.IsNullOrEmpty(ProdCatParam.SearchTerm))
            {
                 ProdCatParam.SearchTerm = ProdCatParam.SearchTerm.ToUpper().Trim();
                 var parentSearch  =  ProdCatParam.SearchTerm.Replace(" ","_");
                var  Icategories = _context.Prod_Categories.Include(p => p.Parent).ThenInclude(p => p.Parent)
                .Where(c => c.SearchParam.Contains(ProdCatParam.SearchTerm) || c.SearchParam.Contains(parentSearch))
                .OrderBy(c => c.Name);

                return await PagedList<Prod_Category>.CreateAsync(Icategories, ProdCatParam.PageNumber, ProdCatParam.PageSize);
            }
            var categories = _context.Prod_Categories.Include(p => p.Parent).ThenInclude(p => p.Parent).OrderBy(c => c.Name);
            return await PagedList<Prod_Category>.CreateAsync(categories, ProdCatParam.PageNumber, ProdCatParam.PageSize);
        }
           public async Task<ICollection<Prod_Category>> GetCategories( )
        {
            var categories = await _context.Prod_Categories.Include(p => p.Parent).OrderBy(c => c.Name).ToListAsync();
            return categories;
        }

        public async Task<Prod_Category> GetCategory(int Id)
        {
            return await _context.Prod_Categories.AsQueryable().Where(pc => pc.Id == Id)
            .Include(p => p.Parent)
            .Include( p => p .Properties)
            .Select(p => new Prod_Category{
                    Id = p.Id,
                    Name = p.Name,
                    NameId = p.NameId,
                    Description = p.Description,
                    Parent = p.Parent,
                    ParentId = p.ParentId,
                    ImageUrl = p.ImageUrl,
                    CanDelete =  ! (p.SubCategories.Any() || p.Products.Any() || 
                    _context.ProductProperties.AsQueryable().Where(pp => pp.Property.Prod_CategoryId == Id).Any()
                    )
            }).FirstOrDefaultAsync();
        }

        public async Task<Prod_Category> GetCategoryWithoutParent(int Id)
        {
            return await _context.Prod_Categories.AsQueryable().Where(pc => pc.Id == Id)
            .FirstOrDefaultAsync();
        }


        public async Task<Prod_Category> GetTrackedCategory(int Id)
        {
            return await _context.Prod_Categories.AsQueryable().Where(pc => pc.Id == Id)
            .FirstOrDefaultAsync();
        }

        public async Task<ICollection<Property>> GetCategoryPropertiesWithRelations(int CategoryId)
        {

        var  Category = await _context.Prod_Categories.Include(p => p.Parent)
        .ThenInclude(p => p.Parent).AsQueryable().Where(c => c.Id == CategoryId).FirstOrDefaultAsync();
         List<int> ids = new List<int>();
         ids.Add(CategoryId);
          if(null != Category.Parent)
          {
              ids.Add(Category.Parent.Id);
                if(null != Category.Parent.Parent)
                ids.Add(Category.Parent.Parent.Id);
          }
          

          var properties =   await  _context.Properties
           .AsQueryable().Where(p => ids.Contains(p.Prod_CategoryId))
           .Include(p => p.Prod_PropertyType)
           .Include(m => m.Prod_MeasurementType)
           .Include(p => p.ProductProperties)
           .Include(p => p.Prod_Category)
        //    .Select(p => new Property{
        //                     Prod_Category = p.Prod_Category,
        //                     Prod_CategoryId = p.Prod_CategoryId,
        //                     Prod_MeasurementType = p.Prod_MeasurementType,
        //                     Prod_MeasurementTypeId = p.Prod_MeasurementTypeId,
        //                     Id = p.Id,
        //                     Prod_PropertyTypeId = p.Prod_PropertyTypeId,
        //                     Prod_PropertyType = p.Prod_PropertyType,
        //                     CanDelete = ! (p.ProductProperties.Count > 0)

        //    })
           .ToListAsync();
            
           return properties;
        }

         public async Task<ICollection<Property>> GetCategoryProperties(int CategoryId)
        {
          return  await  _context.Properties
           .AsQueryable().Where(p => p.Prod_CategoryId == CategoryId)
           .ToListAsync();
        }
        public async Task<IEnumerable<Prod_MeasurementType>> GetMeasurementTypes()
        {
            return await _context.Prod_MeasurementTypes.AsAsyncEnumerable()
            .Select(mt=> new Prod_MeasurementType{
                Id = mt.Id,
                Name = mt.Name,
                Symbol = mt.Symbol
                // CanDelete = !(
                //      _context.ProductProperties
                //     .Where( pp => pp.Property.Prod_MeasurementTypeId == mt.Id).Any()
                //  )
            }).OrderBy(c => c.Name).ToListAsync();
        }
        public async Task<PagedList<Prod_MeasurementType>> GetMeasurementTypes(Prod_MeasurementTypeParam measurementTypeParam)
        {    
            if(!string.IsNullOrEmpty(measurementTypeParam.SearchTerm))
            {
                measurementTypeParam.SearchTerm = measurementTypeParam.SearchTerm.ToUpper();
                var  ImeasurementTypes = _context.Prod_MeasurementTypes
                .AsQueryable().Where(c => c.SearchParam.Contains(measurementTypeParam.SearchTerm))
                .Select( p => new Prod_MeasurementType{
                 Id = p.Id,
                 Name =  p.Name,
                 Symbol = p.Symbol
                //  CanDelete = !(
                //      _context.ProductProperties
                //     .Where( pp => pp.Property.Prod_MeasurementTypeId == p.Id).Any()
                //  )
             }).OrderBy(c => c.Name); 

                return await PagedList<Prod_MeasurementType>.CreateAsync(ImeasurementTypes, measurementTypeParam.PageNumber, measurementTypeParam.PageSize);
            }
            var measurementTypes = _context.Prod_MeasurementTypes.AsQueryable()
            .Select( p => new Prod_MeasurementType{
                 Id = p.Id,
                 Name =  p.Name,
                 Symbol = p.Symbol,
                 CanDelete = ! p.Properties.Any()
             }).OrderBy(c => c.Name); 
            return await PagedList<Prod_MeasurementType>.CreateAsync(measurementTypes, measurementTypeParam.PageNumber, measurementTypeParam.PageSize);
        }
        public async Task<ICollection<Property>> GetProperties(ICollection<Prod_PropertyDto> prod_Properties)
        {
           return await _context.Properties
           .AsQueryable().Where(p => prod_Properties.Select(pp => pp.Id).Contains(p.Id))
           .ToListAsync();
        }
        public async Task<ICollection<ProductProperty>> GetProductProperties(int PropertyCategory, int ProductCategory)
        {
           return await _context.ProductProperties
           .AsQueryable().Where(p => p.Property.Prod_CategoryId == PropertyCategory && p.Product.Prod_CategoryId == ProductCategory )
           .ToListAsync();
        }

        public async Task<ICollection<Property>> GetProperties(List<int> Ids)
        {
            var properties =  await _context.Properties
                                .AsQueryable().Where( p=> Ids.Contains(p.Id)).ToListAsync();
            return properties;
        }

        public async Task<IEnumerable<Prod_PropertyType>> GetPropertyTypes()
        {
            return await _context.Prod_PropertyTypes.AsQueryable().Select(p => new Prod_PropertyType{
                Id = p.Id,
                Name = p.Name,
                Description = p.Description,
                CanDelete = !(
                    _context.ProductProperties
                    .AsQueryable().Where( pp => pp.Property.Prod_PropertyTypeId == p.Id).Any()
                    )
            }).OrderBy(c => c.Name).ToListAsync();
        }

        public async Task<PagedList<Prod_PropertyType>> GetPropertyTypes(Prod_PropertyTypeParams Prod_PropertyTypeParams)
        {
             
            if(!string.IsNullOrEmpty(Prod_PropertyTypeParams.SearchTerm))
            {
                Prod_PropertyTypeParams.SearchTerm = Prod_PropertyTypeParams.SearchTerm.ToUpper();
                var  IpropertyTypes = _context.Prod_PropertyTypes
                .AsQueryable().Where(c => c.SearchParam.Contains(Prod_PropertyTypeParams.SearchTerm))
                .Select( p => new Prod_PropertyType{
                 Id = p.Id,
                 Name = p.Name,
                 Description = p.Description
                //  CanDelete = !(
                //      _context.ProductProperties
                //     .Where( pp => pp.Property.Prod_PropertyTypeId == p.Id).Any()
                //     )
             }).OrderBy(c => c.Name);

                return await PagedList<Prod_PropertyType>
                .CreateAsync(IpropertyTypes, Prod_PropertyTypeParams.PageNumber, Prod_PropertyTypeParams.PageSize);
            }
            var propertyTypes = _context.Prod_PropertyTypes.AsQueryable()
            .Select( p => new Prod_PropertyType{
                 Id = p.Id,
                 Name = p.Name,
                 Description = p.Description
                 //CanDelete = !p.Properties.Any()
             }).OrderBy(c => c.Name);

            return await PagedList<Prod_PropertyType>
            .CreateAsync(propertyTypes, Prod_PropertyTypeParams.PageNumber, Prod_PropertyTypeParams.PageSize);
        }

        public async Task<bool> Prod_PropertyTypeNameExists(string Name)
        {
            return await _context.Prod_PropertyTypes
            .AsQueryable().AnyAsync(pt => pt.Name.ToLower()
            .CompareTo(Name.ToLower()) == 0 );
        }

        public async Task<bool> Prod_PropertyTypeNameExists(string Name, int Id)
        {
            return await _context.Prod_PropertyTypes
            .AsQueryable().AnyAsync(pt => pt.Name.ToLower()
            .CompareTo(Name.ToLower()) == 0 
            &&
            pt.Id != Id);
        } 

        public async Task<bool> Prod_MeasurementTypeNameExists(string Name)
        {
            return await _context.Prod_MeasurementTypes
            .AsQueryable().AnyAsync(pt => pt.Name.ToLower()
            .CompareTo(Name.ToLower()) == 0 );
        }

        public async Task<bool> Prod_MeasurementTypeNameExists(string Name, int Id)
        {
            
            return await _context.Prod_MeasurementTypes
            .AsQueryable().AnyAsync(pt => pt.Name.ToLower()
            .CompareTo(Name.ToLower()) == 0 
            &&
            pt.Id != Id);
        } 

        public async Task<bool> SaveAll()
        {
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> PropertyTypeHasRecords(int Id)
        { 
            return await _context.ProductProperties.AsQueryable().AnyAsync(p => p.Property.Prod_PropertyTypeId == Id);
        }

        public async Task<bool> MeasurementTypeHasRecords(int Id)
        {
          return await _context.ProductProperties.AsQueryable().AnyAsync(p => p.Property.Prod_MeasurementTypeId == Id);
        }


        public async Task<Prod_PropertyType> GetTrackedPropertyType(int Id)
        {
            return await _context.Prod_PropertyTypes.AsQueryable().Where(p => p.Id == Id)
            .FirstOrDefaultAsync();
        }

        public async Task<Prod_PropertyType> GetPropertyType(int Id)
        {
            return await _context.Prod_PropertyTypes.AsQueryable().Where((p => p.Id == Id)).Select(
                p=>new Prod_PropertyType{
                Id = p.Id,
                Name = p.Name,
                Description = p.Description,
                CanDelete = !(
                    _context.ProductProperties
                    .AsQueryable().Where( pp => pp.Property.Prod_PropertyTypeId == p.Id).Any()
                    )
            }
            ).FirstOrDefaultAsync();
        }
        public async Task<Prod_MeasurementType> GetMeasurementType(int Id)
        {
            return await _context.Prod_MeasurementTypes.AsQueryable().Where(m => m.Id == Id)
            .Select(mt => new Prod_MeasurementType{
                Id = mt.Id,
                Name = mt.Name,
                Symbol = mt.Symbol,
                CanDelete = !(
                     _context.ProductProperties
                    .AsQueryable().Where( pp => pp.Property.Prod_MeasurementTypeId == mt.Id).Any()
                 )
            }).FirstOrDefaultAsync();
        }

        public async Task<Prod_MeasurementType> GetTrackedMeasurementType(int Id)
        {
            return await _context.Prod_MeasurementTypes.AsQueryable().Where(m => m.Id == Id)
            .FirstOrDefaultAsync();
        }

        public async Task<IEnumerable<Property>> GetPropertyTypeProperties(int Id)
        {
            return  await  _context.Properties
           .AsQueryable().Where(p => p.Prod_PropertyTypeId == Id)
           .ToListAsync();
        }

        public async Task<IEnumerable<Property>> GetMeasurementTypeProperties(int Id)
        {
            return  await  _context.Properties
           .AsQueryable().Where(p => p.Prod_MeasurementTypeId == Id)
           .ToListAsync();
        }

        public async Task<PagedList<Product>> GetInactiveProducts(ProductParams productParams)
        {
             if(!string.IsNullOrEmpty(productParams.SearchTerm))
            {
                 productParams.SearchTerm = productParams.SearchTerm.ToUpper().Trim();
                var  Iproducts = _context.Products
                .AsQueryable().Where(c => c.Deactivated == true &&
                 c.SearchParam.Contains(productParams.SearchTerm)
                 || c.Prod_Category.SearchParam.Contains(productParams.SearchTerm)
                 || c.Service.SearchParam.Contains(productParams.SearchTerm)
                 || (null != c.Prod_Category.Parent && c.Prod_Category.SearchParam.Contains(productParams.SearchTerm))
                 || (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && c.Prod_Category.Parent.Parent.SearchParam.Contains(productParams.SearchTerm) )
                 )
                 .Include(p => p.Prod_Category).ThenInclude(p => p.Parent)
                 .Include(p => p.Vendor).OrderBy(c => c.Name);

                return await PagedList<Product>.CreateAsync(Iproducts, productParams.PageNumber, productParams.PageSize);
            }
            var products = _context.Products.AsQueryable().Where(c => c.Deactivated == true )
            .Include(p => p.Photos).Include(p => p.Prod_Category).ThenInclude(p => p.Parent)
            .Include(p => p.Vendor).OrderBy(c => c.Name);
            return await PagedList<Product>.CreateAsync(products, productParams.PageNumber, productParams.PageSize);
        }

        public async Task<PagedList<Product>> GetProducts(ProductParams productParams)
        {
            // var vendorIds = productParams.VendorIds.ToList();
             if(!string.IsNullOrEmpty(productParams.SearchTerm))
            {
                 productParams.SearchTerm = productParams.SearchTerm.ToUpper().Trim();
                var  Iproducts = _context.Products
                .AsQueryable().Where(c => c.Deactivated == productParams.Deactivated &&
                c.SearchParam.Contains(productParams.SearchTerm)
                 || c.Prod_Category.SearchParam.Contains(productParams.SearchTerm)
                 || c.Service.SearchParam.Contains(productParams.SearchTerm)
                 || c.Vendor.SearchParam.Contains(productParams.SearchTerm)
                 || (null != c.Prod_Category.Parent && c.Prod_Category.SearchParam.Contains(productParams.SearchTerm))
                 || (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && c.Prod_Category.Parent.Parent.SearchParam.Contains(productParams.SearchTerm) )
                //  && (productParams.VendorIds.Count() == 0 || (c.VendorId !=null && vendorIds.Contains(c.VendorId.Value)))
                )
                .Include(p => p.Photos).Include(p => p.Prod_Category).ThenInclude(p => p.Parent).ThenInclude(p => p.Parent )
                .Include(p => p.Vendor).OrderBy(c => c.Name);

                return await PagedList<Product>.CreateAsync(Iproducts, productParams.PageNumber, productParams.PageSize);
            }
            var products = _context.Products.AsQueryable().Where(c => c.Deactivated == productParams.Deactivated  
                                                                //  && (productParams.VendorIds.Count() == 0 || (c.VendorId !=null && vendorIds.Contains(c.VendorId.Value)))
                                                                 )
            .Include(p => p.Photos).Include(p => p.Prod_Category).ThenInclude(p => p.Parent).ThenInclude(p => p.Parent)
            .Include(p => p.Vendor).OrderBy(c => c.Name);
            return await PagedList<Product>.CreateAsync(products, productParams.PageNumber, productParams.PageSize);
        }

        public async Task<ICollection<Prod_Category>> GetCategoriesByService(string NameId)
        {
            NameId = NameId.ToLower();
            var service = await _context.Services.AsQueryable().Where(s => s.NameId.CompareTo(NameId) == 0).FirstOrDefaultAsync();
            var firstParent = await _context.Products
            .AsQueryable().Where(c =>!c.Deactivated 
                    && (c.Service.NameId.CompareTo(NameId)== 0 || service.Generic)
                    && null == c.Prod_Category.Parent)
            .Select(p => p.Prod_Category)
            .Distinct().ToListAsync();

            //var  firstParent =  ParentCategories.Where(c => null == c.Parent).ToList();

            var secondParents =  await _context.Products
            .AsQueryable().Where(c => !c.Deactivated && (c.Service.NameId
            .CompareTo(NameId)== 0 || service.Generic)
            && (null != c.Prod_Category.Parent && null == c.Prod_Category.Parent.Parent))
            .Select(p => p.Prod_Category.Parent)
            .Distinct().ToListAsync();

            var thirdParents = await _context.Products.AsQueryable().Where(c =>!c.Deactivated && (c.Service.NameId
            .CompareTo(NameId)== 0 || service.Generic) && 
            null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent)
            .Select(p => p.Prod_Category.Parent.Parent)
            .Distinct().ToListAsync();

            firstParent.AddRange(secondParents);
            firstParent.AddRange(thirdParents);
            firstParent = firstParent.GroupBy(p => p.Id).Select(o => o.First()).ToList();
            return firstParent;
        }

        public async Task<ICollection<Prod_Category>> GetCategoriesBySearch(string Search)
        {
            Search = Search.ToUpper().Trim();
            // service = await _context.Services.AsQueryable().Where(s => s.NameId.CompareTo(NameId) == 0).FirstOrDefaultAsync();
            var firstParent = await _context.Products
            .AsQueryable().Where(c =>!c.Deactivated &&
                  c.SearchParam.Contains(Search)
                                                    || c.Prod_Category.SearchParam.Contains(Search)
                                                    || (null != c.Prod_Category.Parent && c.Prod_Category.Parent.SearchParam.Contains(Search))
                                                    || (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && c.Prod_Category.Parent.Parent.SearchParam.Contains(Search) )
                    && null == c.Prod_Category.Parent)
            .Select(p => p.Prod_Category)
            .Distinct().ToListAsync();

            //var  firstParent =  ParentCategories.AsQueryable().Where(c => null == c.Parent).ToList();

            var secondParents =  await _context.Products
            .AsQueryable().Where(c => !c.Deactivated && (c.SearchParam.Contains(Search)
                                                    || c.Prod_Category.SearchParam.Contains(Search)
                                                    || (null != c.Prod_Category.Parent && c.Prod_Category.Parent.SearchParam.Contains(Search))
                                                    || (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && c.Prod_Category.Parent.Parent.SearchParam.Contains(Search) ))
            && (null != c.Prod_Category.Parent && null == c.Prod_Category.Parent.Parent))
            .Select(p => p.Prod_Category.Parent)
            .Distinct().ToListAsync();

            var thirdParents = await _context.Products.AsQueryable().Where(c =>!c.Deactivated && (
                                                        c.SearchParam.Contains(Search)
                                                    || c.Prod_Category.SearchParam.Contains(Search)
                                                    || (null != c.Prod_Category.Parent && c.Prod_Category.Parent.SearchParam.Contains(Search))
                                                    || (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && c.Prod_Category.Parent.Parent.SearchParam.Contains(Search) ))
             && (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent))
            .Select(p => p.Prod_Category.Parent.Parent)
            .Distinct().ToListAsync();

            firstParent.AddRange(secondParents);
            firstParent.AddRange(thirdParents);
            var retfirstParent = firstParent.GroupBy(p => p.Id).Select(o => o.First()).ToList();
            return retfirstParent;
        }


        public async Task<ICollection<Prod_Category>> GetCategoriesByVendor(int Id)
        {
            var firstParent = await _context.Products
            .AsQueryable().Where(c =>!c.Deactivated && c.VendorId == Id && null == c.Prod_Category.Parent)
            .Select(p => p.Prod_Category)
            .Distinct().ToListAsync();

            //var  firstParent =  ParentCategories.AsQueryable().Where(c => null == c.Parent).ToList();

            var secondParents =  await _context.Products
            .AsQueryable().Where(c => !c.Deactivated && c.VendorId == Id 
            && (null != c.Prod_Category.Parent && null == c.Prod_Category.Parent.Parent))
            .Select(p => p.Prod_Category.Parent)
            .Distinct().ToListAsync();

            var thirdParents = await _context.Products.AsQueryable().Where(c =>!c.Deactivated && c.VendorId == Id
             && null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent)
            .Select(p => p.Prod_Category.Parent.Parent)
            .Distinct().ToListAsync();

            firstParent.AddRange(secondParents);
            firstParent.AddRange(thirdParents);
            firstParent = firstParent.GroupBy(p => p.Id).Select(o => o.First()).ToList();
            return firstParent;
        }

        public async Task<Tuple<PagedList<Product>,List<Prod_Category>, Prod_Category, double, double>>GetProducts(ServiceProductParams productParams){
            productParams.ServiceNameId = productParams.ServiceNameId.ToLower();
            
            var Service   = await GetService(productParams.ServiceNameId);
            Prod_Category Category = new Prod_Category();
            double MaxPrice = 0;
            double MinPrice = 0;
             productParams.ServiceNameId = productParams.ServiceNameId.ToLower().Trim();



            if(productParams.Category > 0)
            {
                
                var  Iproducts = _context.Products
                .AsQueryable().Where(c => !c.Deactivated  && ( c.Service.NameId
                .CompareTo(productParams.ServiceNameId) == 0 ||  Service.Generic));

                    var  PriceList = await Iproducts.OrderBy(p => p.Price).Select(p => p.Price ).ToListAsync();
                            if(PriceList.Count > 0) {
                                MaxPrice = PriceList.Last();
                                MinPrice = PriceList.First();
                            }

               Iproducts =  Iproducts.AsQueryable().Where(c => productParams.Category == c.Prod_CategoryId || 
                (null != c.Prod_Category.Parent && c.Prod_Category.ParentId == productParams.Category ) ||
                (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && productParams.Category == c.Prod_Category.Parent.ParentId));
                    
                    if(!String.IsNullOrEmpty(productParams.SearchTerm))
                    {
                        productParams.SearchTerm = productParams.SearchTerm.Trim().ToUpper();
                        Iproducts = Iproducts.AsQueryable().Where(c => c.SearchParam.Contains(productParams.SearchTerm)
                                                    || c.Prod_Category.SearchParam.Contains(productParams.SearchTerm)
                                                    || (null != c.Prod_Category.Parent && c.Prod_Category.Parent.SearchParam.Contains(productParams.SearchTerm))
                                                    || (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && c.Prod_Category.Parent.Parent.SearchParam.Contains(productParams.SearchTerm) )
                                                    );
                    }

                
                if(productParams.MinPrice > 0 || productParams.MaxPrice > 0)
                {
                    if(productParams.MinPrice == 0) productParams.MinPrice = MinPrice;
                    if(productParams.MaxPrice == 0) productParams.MaxPrice = MaxPrice;
                    Iproducts = Iproducts.AsQueryable().Where(c => c.Price >= productParams.MinPrice && c.Price <= productParams.MaxPrice );
                }

                switch(productParams.Sort)
                {
                    case "pricedesc":
                    Iproducts = Iproducts.OrderByDescending(p=> p.Price);
                    break;
                    case "priceasc":
                    Iproducts = Iproducts.OrderBy(p=> p.Price);
                    break;
                    default:
                    Iproducts = Iproducts.OrderBy(p => p.Name);
                     break;
                }
                Iproducts = Iproducts.Include(p => p.Photos).Include(p => p.Prod_Category).ThenInclude(p => p.Parent)
                .Include(p => p.Vendor); 
                
                var ChildrenCategories = await _context.Products
                                        .AsQueryable().Where(c => 
                                            !c.Deactivated && c.Service.NameId
                                            .CompareTo(productParams.ServiceNameId)== 0 || Service.Generic
                                           ).Select(p => p.Prod_Category)
                                           .AsQueryable().Where(pc => pc.ParentId == productParams.Category)
                                           .ToListAsync(); 

                var secondChild = await _context.Products
                                        .AsQueryable().Where(c => 
                                            !c.Deactivated && c.Service.NameId
                                            .CompareTo(productParams.ServiceNameId)== 0 || Service.Generic
                                           ).Select(p => p.Prod_Category.Parent)
                                           //.Include(p => p.Parent)
                                           .AsQueryable().Where(pc => pc.ParentId == productParams.Category)
                                           .ToListAsync();  

                 ChildrenCategories.AddRange(secondChild);
                 ChildrenCategories = ChildrenCategories.GroupBy(p => p.Id ).Select(o => o.First()).ToList();

                Category = await _context.Prod_Categories.AsQueryable().Where(p => p.Id == productParams.Category)
                         .Include(p => p.Parent).ThenInclude( p => p.Parent).FirstOrDefaultAsync();
                var PagedListproducts = await PagedList<Product>.CreateAsync(Iproducts, productParams.PageNumber, productParams.PageSize);
                
                
                return  new Tuple<PagedList<Product>,List<Prod_Category>, Prod_Category, double, double>
                (PagedListproducts, ChildrenCategories, Category, MinPrice, MaxPrice);
            }
            
             

            var products = _context.Products.AsQueryable().Where(c => !c.Deactivated && c.Service.NameId
            .CompareTo(productParams.ServiceNameId)== 0 ||  Service.Generic);

             var  PriceList2 = await products.OrderBy(p => p.Price).Select(p => p.Price ).ToListAsync();
            if(PriceList2.Count > 0) {
                MaxPrice = PriceList2.Last();
                MinPrice = PriceList2.First();
            } 

            //Search Products
            if(!String.IsNullOrEmpty(productParams.SearchTerm))
                    {
                        productParams.SearchTerm = productParams.SearchTerm.Trim().ToUpper();
                        products = products.AsQueryable().Where(c => c.SearchParam.Contains(productParams.SearchTerm)
                                                    || c.Prod_Category.SearchParam.Contains(productParams.SearchTerm)
                                                    || (null != c.Prod_Category.Parent && c.Prod_Category.Parent.SearchParam.Contains(productParams.SearchTerm))
                                                    || (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && c.Prod_Category.Parent.Parent.SearchParam.Contains(productParams.SearchTerm) )
                                                    );
                    }
            //get maximum and minimum prices
           
            ///Sort List by minmum to maximum price 
            if(productParams.MinPrice > 0 || productParams.MaxPrice > 0)
            {
                if(productParams.MinPrice == 0) productParams.MinPrice = MinPrice;
                if(productParams.MaxPrice == 0) productParams.MaxPrice = MaxPrice;
                products = products.AsQueryable().Where(c => c.Price >= productParams.MinPrice && c.Price <= productParams.MaxPrice );
            }

            switch(productParams.Sort)
                {
                    case "pricedesc":
                    products = products.OrderByDescending(p=> p.Price);
                    break;
                    case "priceasc":
                    products = products.OrderBy(p=> p.Price);
                    break;
                    default:
                    products = products.OrderBy(p => p.Name);
                     break;
                }

                products = products.Include(p => p.Photos).Include(p => p.Prod_Category).ThenInclude(p => p.Parent)
            .Include(p => p.Vendor);

             var OutPagedListproducts = await PagedList<Product>.CreateAsync(products, productParams.PageNumber, productParams.PageSize);

             return  new Tuple<PagedList<Product>,List<Prod_Category>, Prod_Category,double, double>
                (OutPagedListproducts, null, Category, MinPrice, MaxPrice);
        }

        public async Task<Tuple<PagedList<Product>,List<Prod_Category>, Prod_Category, double, double>>GetProducts(ServiceVendorProductParams productParams){
            productParams.ServiceNameId = productParams.ServiceNameId.ToLower();
            
            var Service   = await GetService(productParams.ServiceNameId);
            Prod_Category Category = new Prod_Category();
            double MaxPrice = 0;
            double MinPrice = 0;
             productParams.ServiceNameId = productParams.ServiceNameId.ToLower().Trim();



            if(productParams.Category > 0)
            {
                
                var  Iproducts = _context.Products
                .AsQueryable().Where(c => !c.Deactivated  && ( c.Service.NameId
                .CompareTo(productParams.ServiceNameId) == 0 ||  Service.Generic)
                && c.VendorId == productParams.VendorId
                );

                    var  PriceList = await Iproducts.OrderBy(p => p.Price).Select(p => p.Price ).ToListAsync();
                            if(PriceList.Count > 0) {
                                MaxPrice = PriceList.Last();
                                MinPrice = PriceList.First();
                            }

               Iproducts =  Iproducts.AsQueryable().Where(c => productParams.Category == c.Prod_CategoryId || 
                (null != c.Prod_Category.Parent && c.Prod_Category.ParentId == productParams.Category ) ||
                (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && productParams.Category == c.Prod_Category.Parent.ParentId));
                    
                    if(!String.IsNullOrEmpty(productParams.SearchTerm))
                    {
                        productParams.SearchTerm = productParams.SearchTerm.Trim().ToUpper();
                        Iproducts = Iproducts.AsQueryable().Where(c => c.SearchParam.Contains(productParams.SearchTerm)
                                                    || c.Prod_Category.SearchParam.Contains(productParams.SearchTerm)
                                                    || (null != c.Prod_Category.Parent && c.Prod_Category.Parent.SearchParam.Contains(productParams.SearchTerm))
                                                    || (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && c.Prod_Category.Parent.Parent.SearchParam.Contains(productParams.SearchTerm) )
                                                    );
                    }

                
                if(productParams.MinPrice > 0 || productParams.MaxPrice > 0)
                {
                    if(productParams.MinPrice == 0) productParams.MinPrice = MinPrice;
                    if(productParams.MaxPrice == 0) productParams.MaxPrice = MaxPrice;
                    Iproducts = Iproducts.AsQueryable().Where(c => c.Price >= productParams.MinPrice && c.Price <= productParams.MaxPrice );
                }

                switch(productParams.Sort)
                {
                    case "pricedesc":
                    Iproducts = Iproducts.OrderByDescending(p=> p.Price);
                    break;
                    case "priceasc":
                    Iproducts = Iproducts.OrderBy(p=> p.Price);
                    break;
                    default:
                    Iproducts = Iproducts.OrderBy(p => p.Name);
                     break;
                }
                Iproducts = Iproducts.Include(p => p.Photos).Include(p => p.Prod_Category).ThenInclude(p => p.Parent)
                .Include(p => p.Vendor); 
                
                var ChildrenCategories = await _context.Products
                                        .AsQueryable().Where(c => 
                                            !c.Deactivated && c.Service.NameId
                                            .CompareTo(productParams.ServiceNameId)== 0 || Service.Generic
                                           ).Select(p => p.Prod_Category)
                                           .AsQueryable().Where(pc => pc.ParentId == productParams.Category)
                                           .ToListAsync(); 

                var secondChild = await _context.Products
                                        .AsQueryable().Where(c => 
                                            !c.Deactivated && c.Service.NameId
                                            .CompareTo(productParams.ServiceNameId)== 0 || Service.Generic
                                           ).Select(p => p.Prod_Category.Parent)
                                           //.Include(p => p.Parent)
                                           .AsQueryable().Where(pc => pc.ParentId == productParams.Category)
                                           .ToListAsync();  

                 ChildrenCategories.AddRange(secondChild);
                 ChildrenCategories = ChildrenCategories.GroupBy(p => p.Id ).Select(o => o.First()).ToList();

                Category = await _context.Prod_Categories.AsQueryable().Where(p => p.Id == productParams.Category)
                         .Include(p => p.Parent).ThenInclude( p => p.Parent).FirstOrDefaultAsync();
                var PagedListproducts = await PagedList<Product>.CreateAsync(Iproducts, productParams.PageNumber, productParams.PageSize);
                
                
                return  new Tuple<PagedList<Product>,List<Prod_Category>, Prod_Category, double, double>
                (PagedListproducts, ChildrenCategories, Category, MinPrice, MaxPrice);
            }
            
             

            var products = _context.Products.AsQueryable().Where(c => !c.Deactivated && (c.Service.NameId
            .CompareTo(productParams.ServiceNameId)== 0 ||  Service.Generic) 
                 && c.VendorId == productParams.VendorId);

             var  PriceList2 = await products.OrderBy(p => p.Price).Select(p => p.Price ).ToListAsync();
            if(PriceList2.Count > 0) {
                MaxPrice = PriceList2.Last();
                MinPrice = PriceList2.First();
            } 

            //Search Products
            if(!String.IsNullOrEmpty(productParams.SearchTerm))
                    {
                        productParams.SearchTerm = productParams.SearchTerm.Trim().ToUpper();
                        products = products.AsQueryable().Where(c => c.SearchParam.Contains(productParams.SearchTerm)
                                                    || c.Prod_Category.SearchParam.Contains(productParams.SearchTerm)
                                                    || (null != c.Prod_Category.Parent && c.Prod_Category.Parent.SearchParam.Contains(productParams.SearchTerm))
                                                    || (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && c.Prod_Category.Parent.Parent.SearchParam.Contains(productParams.SearchTerm) )
                                                    );
                    }
            //get maximum and minimum prices
           
            ///Sort List by minmum to maximum price 
            if(productParams.MinPrice > 0 || productParams.MaxPrice > 0)
            {
                if(productParams.MinPrice == 0) productParams.MinPrice = MinPrice;
                if(productParams.MaxPrice == 0) productParams.MaxPrice = MaxPrice;
                products = products.AsQueryable().Where(c => c.Price >= productParams.MinPrice && c.Price <= productParams.MaxPrice );
            }

            switch(productParams.Sort)
                {
                    case "pricedesc":
                    products = products.OrderByDescending(p=> p.Price);
                    break;
                    case "priceasc":
                    products = products.OrderBy(p=> p.Price);
                    break;
                    default:
                    products = products.OrderBy(p => p.Name);
                     break;
                }

                products = products.Include(p => p.Photos).Include(p => p.Prod_Category).ThenInclude(p => p.Parent)
            .Include(p => p.Vendor);

             var OutPagedListproducts = await PagedList<Product>.CreateAsync(products, productParams.PageNumber, productParams.PageSize);

             return  new Tuple<PagedList<Product>,List<Prod_Category>, Prod_Category,double, double>
                (OutPagedListproducts, null, Category, MinPrice, MaxPrice);
        }

        public async Task<Tuple<PagedList<Product>,List<Prod_Category>, Prod_Category, double, double>>GetProducts(VendorProductParams productParams){
           
            Prod_Category Category = new Prod_Category();
            double MaxPrice = 0;
            double MinPrice = 0;
           
            if(productParams.Category > 0)
            {
                
                var  Iproducts = _context.Products
                .AsQueryable().Where(c => !c.Deactivated  && c.VendorId == productParams.VendorId);
                var  PriceList = await Iproducts.OrderBy(p => p.Price).Select(p => p.Price ).ToListAsync();
                if (PriceList.Count > 0) {
                    MaxPrice = PriceList.Last();
                    MinPrice = PriceList.First();
                }
                Iproducts = Iproducts.AsQueryable().Where(c => 
                (productParams.Category == c.Prod_CategoryId || 
                (null != c.Prod_Category.Parent && c.Prod_Category.ParentId == productParams.Category ) ||
                (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && productParams.Category == c.Prod_Category.Parent.ParentId)));
                

                if(!String.IsNullOrEmpty(productParams.SearchTerm))
                    {
                        productParams.SearchTerm = productParams.SearchTerm.Trim().ToUpper();
                        Iproducts = Iproducts.AsQueryable().Where(c => c.SearchParam.Contains(productParams.SearchTerm)
                                                    || c.Prod_Category.SearchParam.Contains(productParams.SearchTerm)
                                                    || (null != c.Prod_Category.Parent && c.Prod_Category.Parent.SearchParam.Contains(productParams.SearchTerm))
                                                    || (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && c.Prod_Category.Parent.Parent.SearchParam.Contains(productParams.SearchTerm) )
                                                    );
                    }

                
                if(productParams.MinPrice > 0 || productParams.MaxPrice > 0)
                {
                    if(productParams.MinPrice == 0) productParams.MinPrice = MinPrice;
                    if(productParams.MaxPrice == 0) productParams.MaxPrice = MaxPrice;          
                    Iproducts = Iproducts.AsQueryable().Where(c => c.Price >= productParams.MinPrice && c.Price <= productParams.MaxPrice );
                }

                switch(productParams.Sort)
                {
                    case "pricedesc":
                    Iproducts = Iproducts.OrderByDescending(p=> p.Price);
                    break;
                    case "priceasc":
                    Iproducts = Iproducts.OrderBy(p=> p.Price);
                    break;
                    default:
                    Iproducts = Iproducts.OrderBy(p => p.Name);
                     break;
                }
                Iproducts = Iproducts.Include(p => p.Photos).Include(p => p.Prod_Category).ThenInclude(p => p.Parent)
                .Include(p => p.Vendor).Include(p => p.Service); 
                
                var ChildrenCategories = await _context.Products
                                        .AsQueryable().Where(c => 
                                            !c.Deactivated && c.VendorId == productParams.VendorId
                                           ).Select(p => p.Prod_Category)
                                           .AsQueryable().Where(pc => pc.ParentId == productParams.Category)
                                           .ToListAsync(); 

                var secondChild = await _context.Products
                                        .AsQueryable().Where(c => 
                                            !c.Deactivated && c.VendorId == productParams.VendorId 
                                           ).Select(p => p.Prod_Category.Parent)
                                           //.Include(p => p.Parent)
                                           .AsQueryable().Where(pc => pc.ParentId == productParams.Category)
                                           .ToListAsync();  

                 ChildrenCategories.AddRange(secondChild);
                 ChildrenCategories = ChildrenCategories.GroupBy(p => p.Id ).Select(o => o.First()).ToList();

                Category = await _context.Prod_Categories.AsQueryable().Where(p => p.Id == productParams.Category)
                         .Include(p => p.Parent).ThenInclude( p => p.Parent).FirstOrDefaultAsync();
                var PagedListproducts = await PagedList<Product>.CreateAsync(Iproducts, productParams.PageNumber, productParams.PageSize);
                
                
                return  new Tuple<PagedList<Product>,List<Prod_Category>, Prod_Category, double, double>
                (PagedListproducts, ChildrenCategories, Category, MinPrice, MaxPrice);
            }
            
             

            var products = _context.Products.AsQueryable().Where(c => !c.Deactivated && c.VendorId == productParams.VendorId );
              //get maximum and minimum prices
            var  PriceList2 = await products.OrderBy(p => p.Price).Select(p => p.Price ).ToListAsync();
            if (PriceList2.Count > 0) {
                MaxPrice = PriceList2.Last();
                MinPrice = PriceList2.First();
            }
            ///Sort List by minmum to maximum price 
            if(!String.IsNullOrEmpty(productParams.SearchTerm))
                    {
                        productParams.SearchTerm = productParams.SearchTerm.Trim().ToUpper();
                        products = products.AsQueryable().Where(c => c.SearchParam.Contains(productParams.SearchTerm)
                                                    || c.Prod_Category.SearchParam.Contains(productParams.SearchTerm)
                                                    || (null != c.Prod_Category.Parent && c.Prod_Category.Parent.SearchParam.Contains(productParams.SearchTerm))
                                                    || (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && c.Prod_Category.Parent.Parent.SearchParam.Contains(productParams.SearchTerm) )
                                                    );
                    }
          
            if(productParams.MinPrice > 0 || productParams.MaxPrice > 0)
            {
                if(productParams.MinPrice == 0) productParams.MinPrice = MinPrice;
                if(productParams.MaxPrice == 0) productParams.MaxPrice = MaxPrice;
                products = products.AsQueryable().Where(c => c.Price >= productParams.MinPrice && c.Price <= productParams.MaxPrice );
            }

            switch(productParams.Sort)
                {
                    case "pricedesc":
                    products = products.OrderByDescending(p=> p.Price);
                    break;
                    case "priceasc":
                    products = products.OrderBy(p=> p.Price);
                    break;
                    default:
                    products = products.OrderBy(p => p.Name);
                     break;
                }

                products = products.Include(p => p.Photos).Include(p => p.Prod_Category).ThenInclude(p => p.Parent)
            .Include(p => p.Vendor).Include(p => p.Service);

             var OutPagedListproducts = await PagedList<Product>.CreateAsync(products, productParams.PageNumber, productParams.PageSize);

             return  new Tuple<PagedList<Product>,List<Prod_Category>, Prod_Category,double, double>
                (OutPagedListproducts, null, Category, MinPrice, MaxPrice);
        }

         public async Task<Tuple<PagedList<Product>,List<Prod_Category>, Prod_Category, double, double>>GetProducts(ClientProductParams productParams){
           
            Prod_Category Category = new Prod_Category();
            double MaxPrice = 0;
            double MinPrice = 0;
           
            if(productParams.Category > 0)
            {
                
                var  Iproducts = _context.Products
                .AsQueryable().Where(c => !c.Deactivated );
                 var  PriceList = await Iproducts.OrderBy(p => p.Price).Select(p => p.Price ).ToListAsync();
                if (PriceList.Count > 0) {
                    MaxPrice = PriceList.Last();
                    MinPrice = PriceList.First();
                }
                Iproducts = Iproducts.AsQueryable().Where(c=> 
                productParams.Category == c.Prod_CategoryId || 
                (null != c.Prod_Category.Parent && c.Prod_Category.ParentId == productParams.Category ) ||
                (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && productParams.Category == c.Prod_Category.Parent.ParentId));
                
                if(!String.IsNullOrEmpty(productParams.SearchTerm))
                    {
                        productParams.SearchTerm = productParams.SearchTerm.Trim().ToUpper();
                        Iproducts = Iproducts.AsQueryable().Where(c => c.SearchParam.Contains(productParams.SearchTerm)
                                                    || c.Prod_Category.SearchParam.Contains(productParams.SearchTerm)
                                                    || (null != c.Prod_Category.Parent && c.Prod_Category.Parent.SearchParam.Contains(productParams.SearchTerm))
                                                    || (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && c.Prod_Category.Parent.Parent.SearchParam.Contains(productParams.SearchTerm) )
                                                    );
                    }

               
                if(productParams.MinPrice > 0 || productParams.MaxPrice > 0)
                {
                   if(productParams.MinPrice == 0) productParams.MinPrice = MinPrice;
                    if(productParams.MaxPrice == 0) productParams.MaxPrice = MaxPrice;          
                    Iproducts = Iproducts.AsQueryable().Where(c => c.Price >= productParams.MinPrice && c.Price <= productParams.MaxPrice );
                }

                switch(productParams.Sort)
                {
                    case "pricedesc":
                    Iproducts = Iproducts.OrderByDescending(p=> p.Price);
                    break;
                    case "priceasc":
                    Iproducts = Iproducts.OrderBy(p=> p.Price);
                    break;
                    default:
                    Iproducts = Iproducts.OrderBy(p => p.Name);
                     break;
                }
                Iproducts = Iproducts.Include(p => p.Photos).Include(p => p.Prod_Category).ThenInclude(p => p.Parent)
                .Include(p => p.Vendor).Include(p => p.Service); 
                
                var ChildrenCategories = await _context.Products
                                        .AsQueryable().Where(c => 
                                            !c.Deactivated
                                           ).Select(p => p.Prod_Category)
                                           .AsQueryable().Where(pc => pc.ParentId == productParams.Category)
                                           .ToListAsync(); 

                var secondChild = await _context.Products
                                        .AsQueryable().Where(c => 
                                            !c.Deactivated 
                                           ).Select(p => p.Prod_Category.Parent)
                                           //.Include(p => p.Parent)
                                           .AsQueryable().Where(pc => pc.ParentId == productParams.Category)
                                           .ToListAsync();  

                 ChildrenCategories.AddRange(secondChild);
                 ChildrenCategories = ChildrenCategories.GroupBy(p => p.Id ).Select(o => o.First()).ToList();

                Category = await _context.Prod_Categories.AsQueryable().Where(p => p.Id == productParams.Category)
                         .Include(p => p.Parent).ThenInclude( p => p.Parent).FirstOrDefaultAsync();
                var PagedListproducts = await PagedList<Product>.CreateAsync(Iproducts, productParams.PageNumber, productParams.PageSize);
                
                
                return  new Tuple<PagedList<Product>,List<Prod_Category>, Prod_Category, double, double>
                (PagedListproducts, ChildrenCategories, Category, MinPrice, MaxPrice);
            }
            
             

            var products = _context.Products.AsQueryable().Where(c => !c.Deactivated  );
            //get maximum and minimum prices
            var  PriceList2 = await products.OrderBy(p => p.Price).Select(p => p.Price ).ToListAsync();
            if (PriceList2.Count > 0) {
                MaxPrice = PriceList2.Last();
                MinPrice = PriceList2.First();
            }
            //Sort List by minmum to maximum price 

            if(!String.IsNullOrEmpty(productParams.SearchTerm))
                    {
                        productParams.SearchTerm = productParams.SearchTerm.Trim().ToUpper();
                        products = products.AsQueryable().Where(c => c.SearchParam.Contains(productParams.SearchTerm)
                                                    || c.Prod_Category.SearchParam.Contains(productParams.SearchTerm)
                                                    || (null != c.Prod_Category.Parent && c.Prod_Category.Parent.SearchParam.Contains(productParams.SearchTerm))
                                                    || (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && c.Prod_Category.Parent.Parent.SearchParam.Contains(productParams.SearchTerm) )
                                                    );
                    }
            
            if(productParams.MinPrice > 0 || productParams.MaxPrice > 0)
            {
                if(productParams.MinPrice == 0) productParams.MinPrice = MinPrice;
                if(productParams.MaxPrice == 0) productParams.MaxPrice = MaxPrice;
                products = products.AsQueryable().Where(c => c.Price >= productParams.MinPrice && c.Price <= productParams.MaxPrice );
            }

            switch(productParams.Sort)
                {
                    case "pricedesc":
                    products = products.OrderByDescending(p=> p.Price);
                    break;
                    case "priceasc":
                    products = products.OrderBy(p=> p.Price);
                    break;
                    default:
                    products = products.OrderBy(p => p.Name);
                     break;
                }

                products = products.Include(p => p.Photos).Include(p => p.Prod_Category).ThenInclude(p => p.Parent)
            .Include(p => p.Vendor).Include(p => p.Service);

             var OutPagedListproducts = await PagedList<Product>.CreateAsync(products, productParams.PageNumber, productParams.PageSize);

             return  new Tuple<PagedList<Product>,List<Prod_Category>, Prod_Category,double, double>
                (OutPagedListproducts, null, Category, MinPrice, MaxPrice);
        }



        public async Task<Tuple<double, double>> GetMinMaxPriceForSearch(string SearchTerm){
            var  PriceList2 = await _context.Products.AsQueryable().Where(c =>
                c.SearchParam.Contains(SearchTerm)
                 || c.Prod_Category.SearchParam.Contains(SearchTerm)
                 || c.Service.SearchParam.Contains(SearchTerm)
                 || (null != c.Prod_Category.Parent && c.Prod_Category.SearchParam.Contains(SearchTerm))
                 || (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && c.Prod_Category.Parent.Parent.SearchParam.Contains(SearchTerm) ))
                                .OrderBy(p => p.Price).Select(p => p.Price ).ToListAsync();
            if (PriceList2.Count > 0) {
               var  MaxPrice = PriceList2.Last();
               var  MinPrice = PriceList2.First();
               return new Tuple<double, double>(MinPrice,MaxPrice);
            }

            return new Tuple<double, double>(0,0);
        }
         public async Task<Tuple<double, double>> GetMinMaxPriceForAllProductsByVendor(int Id){
            var  PriceList2 = await _context.Products.AsQueryable().Where(c =>
            c.VendorId == Id)
                                .OrderBy(p => p.Price).Select(p => p.Price ).ToListAsync();
            if (PriceList2.Count > 0) {
               var  MaxPrice = PriceList2.Last();
               var  MinPrice = PriceList2.First();
               return new Tuple<double, double>(MinPrice,MaxPrice);
            }

            return new Tuple<double, double>(0,0);
        }

        public async Task<Tuple<double, double>> GetMinMaxPriceForAllProductsByService(string nameId){
            nameId = nameId.ToLower();
            var  PriceList2 = await _context.Products.AsQueryable().Where(c =>
            c.Service.NameId.CompareTo(nameId)== 0)
                                .OrderBy(p => p.Price).Select(p => p.Price ).ToListAsync();
            if (PriceList2.Count > 0) {
               var  MaxPrice = PriceList2.Last();
               var  MinPrice = PriceList2.First();
               return new Tuple<double, double>(MinPrice,MaxPrice);
            }

            return new Tuple<double, double>(0,0);
        }

        
        public async Task<Tuple<double, double>> GetMinMaxPriceForAllProducts(){
            var  PriceList2 = await _context.Products.AsQueryable().OrderBy(p => p.Price).Select(p => p.Price ).ToListAsync();
            if (PriceList2.Count > 0) {
               var  MaxPrice = PriceList2.Last();
               var  MinPrice = PriceList2.First();
               return new Tuple<double, double>(MinPrice,MaxPrice);
            }

            return new Tuple<double, double>(0,0);
        }
        public async Task<PagedList<Product>> GetAllProducts(ProductParams productParams)
        {
             if(!string.IsNullOrEmpty(productParams.SearchTerm))
            {
                 productParams.SearchTerm = productParams.SearchTerm.ToUpper().Trim();
                var  Iproducts = _context.Products
                .AsQueryable().Where(c =>
                c.SearchParam.Contains(productParams.SearchTerm)
                 || c.Prod_Category.SearchParam.Contains(productParams.SearchTerm)
                 || c.Service.SearchParam.Contains(productParams.SearchTerm)
                 || (null != c.Prod_Category.Parent && c.Prod_Category.SearchParam.Contains(productParams.SearchTerm))
                 || (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && c.Prod_Category.Parent.Parent.SearchParam.Contains(productParams.SearchTerm) )
                )
                .Include(p => p.Photos).Include(p => p.Prod_Category).ThenInclude(p => p.Parent)
                .Include(p => p.Vendor).OrderBy(c => c.Name);

                return await PagedList<Product>.CreateAsync(Iproducts, productParams.PageNumber, productParams.PageSize);
            }
            var products = _context.Products
            .Include(p => p.Photos).Include(p => p.Prod_Category).ThenInclude(p => p.Parent)
            .Include(p => p.Vendor).OrderBy(c => c.Name);
            return await PagedList<Product>.CreateAsync(products, productParams.PageNumber, productParams.PageSize);
        }


        public Task<Product> GetProduct(int Id)
        {
            return _context.Products.AsQueryable().Where(p => p.Id == Id)
            .Include(p => p.Photos).Include(p => p.Vendor)
            .Include(p => p.ProductProperties).ThenInclude( p => p.Property.Prod_MeasurementType)
            .Include(p => p.Prod_Category)
            .Include(p => p.ProductProperties).ThenInclude( p => p.Property.Prod_PropertyType)
            .Include(p=> p.ProductOrders)
            .FirstOrDefaultAsync();
        }

        public async  Task<Service> GetServiceWithPhotos(int Id){

            return await _context.Services.AsQueryable().Where(p => p.Id == Id)
                    .Include(p => p.Photos).FirstOrDefaultAsync();
        }

        public async Task<Service> GetDefaultGenericService(){
             return await  _context.Services.AsQueryable().Where(p => p.IsDefaultGeneric).FirstOrDefaultAsync();
        }
         public Task<Product> GetProductForUpdate(int Id)
        {
            return _context.Products.AsQueryable().Where(p => p.Id == Id)
            
            .FirstOrDefaultAsync();
        }
 
        public async Task<ICollection<ProductPropertyDto>> GetProductProperties(int Id)
        {
           var properties = await _context.ProductProperties.AsQueryable().Where(pp => pp.ProductId == Id)
           .Include(pp => pp.Property.Prod_MeasurementType)
           .Include(pp => pp.Property.Prod_PropertyType)
           .Select(pp => new  ProductPropertyDto{
               ProductId = Id,
               PropertyId = pp.PropertyId,
               PropertyTypeName = pp.Property.Prod_PropertyType.Name,
               MeasurementTypeName =  pp.Property.Prod_MeasurementType.Name,
               MeasurementTypeSymbol = pp.Property.Prod_MeasurementType.Symbol,
                Value = pp.NValue == null ? pp.Value: pp.NValue.ToString()
           }).ToListAsync();
          
            
           return  properties;
        }

        public async Task<bool> ProdcutHasRecords(int Id)
        {
           return await _context.ProductOrders.AsQueryable().Where(o => o.ProductId == Id).AsQueryable().AnyAsync();
        }

        public async  Task<ICollection<Product>> GetInActiveProducts()
        {
            return await _context.Products.AsQueryable().Where(p => p.Deactivated == true).ToListAsync();
        }

        public async Task<ICollection<Product>> GetActiveProducts()
        {
            var products = await _context.Products.AsQueryable().Where(p => p.Deactivated == false)
            .ToListAsync();
            return products;
        }

        public async Task<bool> ProductNameExistsInCategory(string name, Prod_Category category)
        {
            name = name.RemoveSpecialCharatcters().ToUpper();
            var exits = await _context.Products
                            .AsQueryable().Where(p => p.Prod_CategoryId == category.Id 
                             && p.UniqueParam.CompareTo(name) == 0
                             && p.VendorId == null)
                            .AsQueryable().AnyAsync();
            return exits;
        }

        public async Task<bool> ProductNameExistsInVendor(string name, Vendor vendor, Prod_Category category)
        {
            name = name.RemoveSpecialCharatcters().ToUpper();
            var exists = await _context.Products
                            .AsQueryable().Where(p => p.VendorId == vendor.Id &&  
                                p.Prod_CategoryId == category.Id &&
                                p.UniqueParam.CompareTo(name) == 0)
                            .AsQueryable().AnyAsync();
            return exists;
        }

        public async Task<bool> ProductNameExistsInCategory(string name, Prod_Category category, int productId)
        {
            name = name.RemoveSpecialCharatcters().ToUpper();
            var exits = await _context.Products
                            .AsQueryable().Where(p => p.Prod_CategoryId == category.Id 
                             && p.UniqueParam.CompareTo(name) == 0
                             && p.VendorId == null 
                             && p.Id != productId)
                            .AsQueryable().AnyAsync();
            return exits;
        }

        public async Task<bool> ProductNameExistsInVendor(string name, Vendor vendor, Prod_Category category, int productId)
        {
            name = name.RemoveSpecialCharatcters().ToUpper();
            var exists = await _context.Products
                            .AsQueryable().Where(p => p.VendorId == vendor.Id &&  
                                p.Prod_CategoryId == category.Id &&
                                p.UniqueParam.CompareTo(name) == 0
                                && p.Id != productId)
                            .AsQueryable().AnyAsync();
            return exists;
        }

        public async Task<ICollection<ProductProperty>> GetProductProperties(IEnumerable<int> list, int Id)
        {
           return await _context.ProductProperties.AsQueryable().Where(p => list.Contains(p.PropertyId) && p.ProductId == Id )
                        .ToListAsync(); 
        }

        public async Task<Service> GetService(int Id)
        {   
            return await _context.Services.AsQueryable().Where(s => s.Id == Id).Include(p => p.Photos).Include(s =>s.Products)
            .Select(
                s => new Service{
                    Id = s.Id,
                    NameId = s.NameId,
                    Name = s.Name,
                    CanDelete = !  s.Products.Any(),
                    Description = s.Description,
                    Photos = s.Photos,
                    Products = s.Products,
                    Generic = s.Generic,
                    PickupAllowed = s.PickupAllowed,
                    IsDefaultGeneric = s.IsDefaultGeneric,
                    PurchaseAllowed = s.PurchaseAllowed,
                    ServiceCharge = s.ServiceCharge
                }
            ).FirstOrDefaultAsync();

         
        }

        public async Task<Service>GetNoLazyService(int id){
            return await _context.Services.AsQueryable().Where(s => s.Id == id).FirstOrDefaultAsync();
        }

        public async Task<Service> GetService(string ServiceNameId)
        {   
            ServiceNameId = ServiceNameId.ToLower();
            return await _context.Services.AsQueryable().Where(s => s.NameId.CompareTo(ServiceNameId) == 0).Include(p => p.Photos).Include(s =>s.Products)
            .Select(
                s => new Service{
                    Id = s.Id,
                    NameId = s.NameId,
                    Name = s.Name,
                    CanDelete = !  s.Products.Any(),
                    Description = s.Description,
                    Photos = s.Photos,
                    Products = s.Products,
                    Generic = s.Generic,
                    PickupAllowed = s.PickupAllowed,
                    IsDefaultGeneric = s.IsDefaultGeneric,
                    PurchaseAllowed = s.PurchaseAllowed,
                    ServiceCharge = s.ServiceCharge
                }
            ).FirstOrDefaultAsync();

         
        }

        public async Task<Service> GetServiceWithoutPhotos(int Id)
        {   
            return await _context.Services.AsQueryable().Where(s => s.Id == Id)
        
            // l.Include(s => s.)
            .FirstOrDefaultAsync();
        }

        public async Task<bool> ServiceNameExists(string Name)
        {
            Name = Name.ToLower();
            return await _context.Services.AsQueryable().AnyAsync(s => s.Name.ToLower().CompareTo(Name) == 0);
        }

        public async Task<bool> ServiceNameExists(string Name, int Id)
        {
            Name = Name.ToLower();
            return await _context.Services.AsQueryable().AnyAsync(s => s.Name.ToLower().CompareTo(Name) == 0
             && s.Id != Id);
        }

        public async Task<bool> ServiceCanBeDeleted(int id)
        {
            return ! await _context.Products.AsQueryable().Where(p => p.ServiceId == id).AsQueryable().AnyAsync();
        } 
         public async Task<PagedList<Service>> GetServices(ServiceParams serviceParams)
        {
           // var categories = _context.Prod_Categories.Include(p => p.Parent);
          
            if(!string.IsNullOrEmpty(serviceParams.SearchTerm))
            {
                 serviceParams.SearchTerm = serviceParams.SearchTerm.ToUpper().Trim();
                 //var nameIdSearch  =  serviceParams.SearchTerm.GenerateValidNameId();
                var  IServices = _context.Services//.Include(p => p.Parent)
                .AsQueryable().Where(c => c.SearchParam.Contains(serviceParams.SearchTerm))
                .OrderBy(c => c.Name);

                return await PagedList<Service>.CreateAsync(IServices, serviceParams.PageNumber, serviceParams.PageSize);
            }
            var services = _context.Services.AsQueryable().OrderBy(c => c.Name);
            return await PagedList<Service>.CreateAsync(services, serviceParams.PageNumber, serviceParams.PageSize);
        }

        public async Task<ICollection<Service>> GetAllServices(){
           return await  _context.Services.AsQueryable().ToListAsync();
        }

        public async Task<ICollection<ServiceDto2>> GetTop6ServicesWithMostOrders()
        {
            // var servicese = await _context.Orders.Include(o => o.Service).AsQueryable().Where(t => null != t.Service ).ToListAsync();
            var services =  _context.Orders.Include(o => o.Service).AsQueryable().Where(t => null != t.Service ).AsEnumerable()
            .GroupBy(po => po.Service).OrderByDescending(r => r.Count())
            .Select(g => new ServiceDto2{
                    Name = g.Key.Name,
                    Id = g.Key.Id,
                    Description = g.Key.Description,
                    PictureUrl =  _context.Photos.FirstOrDefault(f => f.ServiceId == g.Key.Id)?.Url ?? "",
                    NameId = g.Key.NameId, 
                    PickupAllowed = g.Key.PickupAllowed,
                    PurchaseAllowed = g.Key.PurchaseAllowed,
                    IsDefaultGeneric = g.Key.IsDefaultGeneric
                    
                    
            }).Take(6).ToList();

            if(services.Count() >=  6 )
            return services;

            else {
                return await _context.Services.AsQueryable().AsAsyncEnumerable().Select(g => new ServiceDto2 { 
                    Name = g.Name,
                    Description = g.Description,
                    PictureUrl = _context.Photos.FirstOrDefault(f => f.ServiceId == g.Id)?.Url ?? "",
                    Id = g.Id,
                    NameId = g.NameId,
                     PickupAllowed = g.PickupAllowed,
                    PurchaseAllowed = g.PurchaseAllowed,
                    IsDefaultGeneric = g.IsDefaultGeneric
                    
            }).Take(6).ToListAsync();
            }
        }

         public async Task<ICollection<ServiceDto2>> GetServicesWithPictures()
        {
       
                return await _context.Services.AsQueryable().AsAsyncEnumerable().Select(g => new ServiceDto2 { 
                    Name = g.Name,
                    Description = g.Description,
                    PictureUrl = _context.Photos.FirstOrDefault(f => f.ServiceId == g.Id)?.Url ?? "",
                    Id = g.Id,
                    NameId = g.NameId,
                    PickupAllowed = g.PickupAllowed,
                    PurchaseAllowed = g.PurchaseAllowed,
                    IsDefaultGeneric = g.IsDefaultGeneric

                    
            }).ToListAsync();
            
        }

        public async Task<ICollection<Product>> GetProducts(string Search)
        {
            Search = Search.ToUpper().Trim();
            var products = await _context.Products
            .AsQueryable().Where( p => (p.SearchParam.Contains(Search) || p.Prod_Category.SearchParam.Contains(Search)
                 || p.Service.SearchParam.Contains(Search)
                 || (null != p.Prod_Category.Parent && p.Prod_Category.Parent.SearchParam.Contains(Search))
                 || (null != p.Prod_Category.Parent && null != p.Prod_Category.Parent.Parent && p.Prod_Category.Parent.Parent.SearchParam.Contains(Search) )) 
                 && p.Deactivated == false
            )
            .Include(p => p.Vendor).Include(p => p.Photos).Include(s => s.Service)
            .Include(p => p.Prod_Category).ThenInclude(p => p.Parent).ToListAsync();

            return products;
        }

        public async Task<Product> GetProductWithVendor(int Id)
        {
           return await _context.Products.AsQueryable().Where(p => p.Id == Id).Include(p => p.Vendor)
                .ThenInclude(o => o.Address).FirstOrDefaultAsync();
        }

        public async Task<List<Product>> GetProductListWithVendorAndAddress(List<int> Ids)
        {
           return await _context.Products.AsQueryable().Where(p => Ids.Contains(p.Id))
                                        .Include(p => p.Vendor).ThenInclude(v => v.Address).ToListAsync();
        }

        // public async Task<PagedList<Product>> GetProducts(ClientProductParams productParams)
        // {
        //     var Iproducts = _context.Products.Where(p => p.Deactivated ==false);
        //     if(!string.IsNullOrEmpty(productParams.SearchTerm))
        //     {
        //         productParams.SearchTerm = productParams.SearchTerm.ToUpper().Trim();
        //          Iproducts = Iproducts.Where(c =>
        //         c.SearchParam.Contains(productParams.SearchTerm) 
        //          || c.Service.SearchParam.Contains(productParams.SearchTerm)
        //          || (c.Prod_Category.SearchParam.Contains(productParams.SearchTerm))
        //          || (null != c.Prod_Category.Parent && c.Prod_Category.Parent.SearchParam.Contains(productParams.SearchTerm))
        //          || (null != c.Prod_Category.Parent && null != c.Prod_Category.Parent.Parent && c.Prod_Category.Parent.Parent.SearchParam.Contains(productParams.SearchTerm) )
        //         );

        //     }
        //         if(null != productParams.Categories && productParams.Categories.Count() > 0)
        //          Iproducts = Iproducts.Where(c =>
        //         productParams.Categories.Contains(c.Prod_Category.Id) || 
        //         productParams.Categories.Contains(c.Prod_Category.Parent.Id));

        //         if(productParams.MinPrice != 0 || productParams.MaxPrice != 0)
        //         {
        //             Iproducts = Iproducts
        //             .Where(p => p.Price >= productParams.MinPrice && p.Price <= productParams.MaxPrice);
        //         }
        //     var products =  Iproducts.Include(p => p.Photos).Include(p => p.Prod_Category)
        //                     .ThenInclude(p => p.Parent)
        //                     .Include(p => p.Vendor).Include(p => p.Service).OrderBy(c => c.Name);

        //     return await PagedList<Product>.CreateAsync(products, productParams.PageNumber, productParams.PageSize);
        // }

    }
}