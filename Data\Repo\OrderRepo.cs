using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using QuickMessenger.API.Data.DTO.Client;
using QuickMessenger.API.Data.DTO.Shared;
using QuickMessenger.API.Data.DTO.Staff;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Helpers;
using QuickMessenger.API.Helpers.Params;

namespace QuickMessenger.API.Data.Repo
{
    public class OrderRepo : IOrderRepo
    {
        private readonly DataContext _datacontext;
        public OrderRepo(DataContext datacontext)
        {
            this._datacontext = datacontext;

        }

        public async Task<Order> GetNoLazyOrder(int Id)
        {
            return await _datacontext.Orders.AsQueryable().Where(o => o.Id == Id).FirstOrDefaultAsync();
        }

        public async Task<Order> GetOrder(int Id)
        {
           return await _datacontext.Orders.Include(o => o.Client)
             .Include(o => o.PickupItems).ThenInclude(p => p.Photos)
             .Include(o => o.Client)
             .Include(o => o.PickupItems).ThenInclude(p => p.Address)
             .Include(o => o.ProductOrders)
             .ThenInclude(p => p.Product).ThenInclude(p => p.Photos)
             .Include(o => o.ProductOrders)
             .ThenInclude(p => p.Product).ThenInclude(v => v.Vendor)
             .Include(o => o.ProductOrders)
             .ThenInclude(p => p.Product).ThenInclude(v => v.Photos)
             .Include(o => o.Address)
             .Include(o => o.Rider)
             .Include(s => s.Service)
             .Include(c => c.Cart)
             .AsQueryable().Where(o => o.Id == Id).FirstOrDefaultAsync();
        }


        public void Delete<T>(T entity) where T : class
        {
            _datacontext.Remove(entity);
        }

        public void Update <T>(T entity) where T : class
        {
            _datacontext.Update(entity);
        }
        
        public void DeleteAll<T>(IEnumerable<T> entities) where T : class
        {
            _datacontext.RemoveRange(entities);
        }

        public async Task<PagedList<Order>> GetAllPaidOrders(OrderParams orderParams)
        {
            
            var orders = _datacontext.Orders.Include(o => o.Client)
             .Include(o => o.PickupItems)
             .Include(o => o.ProductOrders)
             .Include(s => s.Service)
             .Include(o => o.Address)
             .Include(o => o.Rider)
             .AsQueryable().Where(o=> null != o.State || o.State.CompareTo("Pending") == 0);
            
            if(null !=orderParams.States && orderParams.States.Count() > 0)
            {
                 //orderParams.State = orderParams.State.();
                orders = orders
                .AsQueryable().Where(o => orderParams.States.Contains(o.State) );
            }
             if (!string.IsNullOrEmpty(orderParams.SearchTerm))
             {
                 orderParams.SearchTerm = orderParams.SearchTerm.ToUpper();
                 orders = orders.AsQueryable().Where(u => u.SearchParam.Contains(orderParams.SearchTerm) ||
                                            u.Rider.SearchParam.Contains(orderParams.SearchTerm));
             }
            if(!string.IsNullOrEmpty(orderParams.Type))
            {
                orders = orders.AsQueryable().Where(o => o.Type.CompareTo(orderParams.Type) == 0);
            }
             if(orderParams.FromDate != null)
             {
                orders = orders.AsQueryable().Where(u => u.Date > orderParams.FromDate);
             }

             if(orderParams.ToDate != null)
             {
                orders = orders.AsQueryable().Where(u => u.Date < orderParams.ToDate);
             }  
             return await PagedList<Order>.CreateAsync(orders.OrderByDescending(o => o.Date), orderParams.PageNumber, orderParams.PageSize);

        }

         public async Task<bool> SaveAll()
        {
            return await _datacontext.SaveChangesAsync() > 0;
        }

        public async Task<ICollection<Order>> GetOrdersByClient(int ClientId)
        {
            return await _datacontext.Orders.AsQueryable().Where(o => o.ClientId == ClientId && null != o.State)
            .Include(o => o.ProductOrders)
            .ThenInclude( p=> p.Product)
            .ThenInclude(p => p.Photos)
            .Include(o => o.PickupItems)
            .ThenInclude(o => o.Photos)
            //.Include(o => o.)
            .ToListAsync();
        }
        public async Task<ICollection<Order>> GetOrdersByDate(DateTime Date)
        {
            return await _datacontext.Orders.AsQueryable().Where(o => o.Date.Date == Date.Date)
            .Include(o => o.ProductOrders)
            .ThenInclude( p=> p.Product)
            .Include(o => o.PickupItems)
            //.Include(o => o.)
            .ToListAsync();
        }

        public async Task<ICollection<Order>> GetOrdersByDateRange(DateTime StartDate, DateTime EndDate)
        {
            return await _datacontext.Orders.AsQueryable().Where(o => o.Date.Date >= StartDate.Date && o.Date.Date <= EndDate.Date) 
            .Include(o => o.ProductOrders)
            .ThenInclude( p=> p.Product)
            .Include(o => o.PickupItems)
            //.Include(o => o.)
            .ToListAsync();
        }

        public async Task<ICollection<Order>> GetOrdersWithMonth(int Year, int month)
        {
             return await _datacontext.Orders.AsQueryable().Where(o => o.Date.Year == Year && o.Date.Month == month) 
            .Include(o => o.ProductOrders)
            .ThenInclude( p=> p.Product)
            .Include(o => o.PickupItems)
            //.Include(o => o.)
            .ToListAsync();
        }

        public async Task<ICollection<OrdersByLocationList>> GetOrdersGroupedByCities(int Year, int month)
        {
           var list =   _datacontext.Orders.AsQueryable().Where(o => o.Date.Year == Year && o.Date.Month == month )   
            .Include(a => a.Address).AsEnumerable()
             .GroupBy( a => new {a.Address.Country, a.Address.State, a.Address.City})
            .Select(c => new OrdersByLocationList{
                City = c.Key.City,
                Country = c.Key.Country,
                State = c.Key.State,
                Orders = c.Count()
            }).OrderByDescending(o => o.Orders).ToList();

            return list;
        }

        public async Task<ICollection<OrdersByLocationList>> GetOrdersGroupedByCities(DateTime Date)
        {
            var list =  await  _datacontext.Orders.AsQueryable().Where(o => o.Date.Date == Date.Date )
           
            .Include(a => a.Address).AsAsyncEnumerable()
             .GroupBy( a => new {a.Address.Country, a.Address.State, a.Address.City})
            .SelectAwait ( async c  =>  new OrdersByLocationList{
                City = c.Key.City,
                Country = c.Key.Country,
                State = c.Key.State,
                Orders = await  c.CountAsync()
            }).OrderByDescending(  o =>  o.Orders).ToListAsync();

            return list;
        }

        public async Task<ICollection<OrdersByLocationList>> GetOrdersGroupedByCities(DateTime StartDate, DateTime EndDate)
        {
            var list = await  _datacontext.Orders
            .Include(o => o.Address)
            .AsQueryable().Where(o => o.Date.Date >= StartDate.Date && o.Date.Date <= EndDate )
            .Include(a => a.Address).AsAsyncEnumerable()
             .GroupBy( a => new {a.Address.Country, a.Address.State, a.Address.City})
            .SelectAwait(async c => new OrdersByLocationList{
                City = c.Key.City,
                Country = c.Key.Country,
                State = c.Key.State,
                Orders = await  c.CountAsync()
            }).OrderByDescending(o => o.Orders).ToListAsync();

            return list;
        }

        public async Task<ICollection<OrdersByVendorList>> GetOrdersGroupedByVendor(int Year, int month)
        {
             var list =  await  _datacontext.ProductOrders   
             .Include(o => o.Order)       
             .Include(o => o.Product)
             .ThenInclude(o => o.Vendor)
             .AsQueryable().Where(o => o.Order.Date.Year == Year && o.Order.Date.Month == month && o.Product.Vendor != null)
             .AsAsyncEnumerable().GroupBy( a => a.Product.Vendor)
            .SelectAwait(async c =>
              new OrdersByVendorList{
                City = _datacontext.Addresses.AsQueryable().Where(v  => v.VendorId == c.Key.Id).FirstOrDefault().City,
                Name = c.Key.Name,
                Id = c.Key.Id,
                Orders = await  c.CountAsync(),
                photoUrl = _datacontext.Photos.AsQueryable().Where(v  => v.VendorId == c.Key.Id).FirstOrDefault().Url
            
            }).OrderByDescending(o => o.Orders).ToListAsync();

            return list;
        }

        public async Task<ICollection<OrdersByVendorList>> GetOrdersGroupedByVendor(DateTime Date)
        {
            var list = await   _datacontext.ProductOrders
            .Include(o => o.Product)
            .ThenInclude(p => p.Vendor)
            .Include(o => o.Order)
            .AsQueryable().Where(o => o.Order.Date.Date == Date.Date && o.Product.Vendor != null)
           .AsAsyncEnumerable().GroupBy( a => a.Product.Vendor)
            .SelectAwait( async c =>
              new OrdersByVendorList{
                City = _datacontext.Addresses.AsQueryable().Where(v  => v.VendorId == c.Key.Id).FirstOrDefault().City,
                Name = c.Key.Name,
                Id = c.Key.Id,
                Orders = await c.CountAsync(),
                photoUrl = _datacontext.Photos.AsQueryable().Where(v  => v.VendorId == c.Key.Id).FirstOrDefault().Url
            
            }).OrderByDescending(o => o.Orders).ToListAsync();

            return list;
        }

        public async Task<ICollection<OrdersByVendorList>> GetOrdersGroupedByVendor(DateTime StartDate, DateTime EndDate)
        {
            var list =   await  _datacontext.ProductOrders
            .Include(O => O.Order)
            .Include(O => O.Product)
            .ThenInclude(P => P.Vendor)
            .AsQueryable().Where(o => o.Order.Date.Date >= StartDate.Date
             && o.Order.Date.Date <= EndDate && o.Product.Vendor != null ).AsAsyncEnumerable()
            .GroupBy( a => a.Product.Vendor)
            .SelectAwait( async c =>
              new OrdersByVendorList{
                City = _datacontext.Addresses.AsQueryable().Where(v  => v.VendorId == c.Key.Id).FirstOrDefault().City,
                Name = c.Key.Name,
                Id = c.Key.Id,
                Orders = await c.CountAsync(),
                photoUrl = _datacontext.Photos.AsQueryable().Where(v  => v.VendorId == c.Key.Id).FirstOrDefault().Url
            
            }).OrderByDescending(o => o.Orders).ToListAsync();

            return list;
        }

        public async Task<ICollection<OrdersByProductList>> GetOrdersGroupedByProduct(int Year, int month)
        {
            var list =  await  _datacontext.ProductOrders
                .Include(o => o.Product)
             .AsQueryable().Where(o => o.Order.Date.Year == Year && o.Order.Date.Month == month && o.Product != null) 
            .AsAsyncEnumerable().GroupBy( a =>  a.Product)
            .SelectAwait( async c =>
              new OrdersByProductList{
                Name = c.Key.Name,
                Id = c.Key.Id,
                Orders = await c.CountAsync(),
                Category = _datacontext.Prod_Categories.AsQueryable().Where(p => p.Id == c.Key.Prod_CategoryId).FirstOrDefault().Name,
                photoUrl = _datacontext.Photos.AsQueryable().Where(v  => v.ProductId == c.Key.Id).FirstOrDefault().Url
            
            }).OrderByDescending(o => o.Orders).ToListAsync();

            return list;
        }

        public async Task<ICollection<OrdersByProductList>> GetOrdersGroupedByProduct(DateTime Date)
        {
            var list =  await  _datacontext.ProductOrders.Include(p => p.Product)
            .Include(o => o.Product)
            .AsQueryable().Where(o => o.Order.Date.Date == Date.Date && o.Product != null)  
            .AsAsyncEnumerable().GroupBy( a => a.Product)
            .SelectAwait( async c =>
              new OrdersByProductList{
                Name = c.Key.Name,
                Id = c.Key.Id,
                Orders = await c.CountAsync(),
                Category = _datacontext.Prod_Categories.AsQueryable().Where(r => r.Id == c.Key.Prod_CategoryId).Select(a => a.Name).FirstOrDefault(),
                photoUrl = _datacontext.Photos.AsQueryable().Where(r => r.ProductId == c.Key.Id).FirstOrDefault().Url
            
            }).OrderByDescending(o => o.Orders).ToListAsync();

            return list;
        }

        public async Task<ICollection<OrdersByProductList>> GetOrdersGroupedByProduct(DateTime StartDate, DateTime EndDate)
        {
             var list = await   _datacontext.ProductOrders
             .Include(o => o.Product)
             .AsQueryable().Where(o => o.Order.Date.Date >= StartDate.Date && o.Order.Date.Date <= EndDate.Date && o.Product != null)
            .AsAsyncEnumerable().GroupBy( a => a.Product)
            .SelectAwait(async c =>
              new OrdersByProductList{
                Name = c.Key.Name,
                Id = c.Key.Id,
                Orders = await  c.CountAsync(),
                Category = _datacontext.Prod_Categories.AsQueryable().Where(r => r.Id == c.Key.Prod_CategoryId).Select(a => a.Name).FirstOrDefault(),
                photoUrl = _datacontext.Photos.AsQueryable().Where(r => r.ProductId == c.Key.Id).FirstOrDefault().Url
            }).OrderByDescending(o => o.Orders).ToListAsync();

            return list;
        }

        public async Task<ICollection<OrdersByRiderList>> GetOrdersGroupedByRider(int Year, int month)
        {
            var list =  await  _datacontext.Orders
            .Include(o => o.Rider )
             .AsQueryable().Where(o => o.Date.Year == Year && o.Date.Month == month && o.Rider != null)
            .AsAsyncEnumerable().GroupBy( a => a.Rider)
            .SelectAwait( async c =>
              new OrdersByRiderList{
                FirstName = c.Key.FirstName,
                Id = c.Key.Id,
                Orders = await c.CountAsync(),
                LastName = c.Key.LastName,
                photoUrl = c.Key.PhotoUrl
            
            }).OrderByDescending(o => o.Orders).ToListAsync();

            return list;
        }

        public async Task<ICollection<OrdersByRiderList>> GetOrdersGroupedByRider(DateTime Date)
        {
            var list =  await   _datacontext.Orders
                .Include(o => o.Rider)
             .AsQueryable().Where(o => o.Date.Date == Date.Date && o.Rider != null).
            AsAsyncEnumerable().GroupBy( a => a.Rider)
            .SelectAwait( async c =>
              new OrdersByRiderList{
                FirstName = c.Key.FirstName,
                Id = c.Key.Id,
                Orders = await c.CountAsync(),
                LastName = c.Key.LastName,
                photoUrl = c.Key.PhotoUrl
            
            }).OrderByDescending(o => o.Orders).ToListAsync();

            return list;
        }

        public async Task<ICollection<OrdersByRiderList>> GetOrdersGroupedByRider(DateTime StartDate, DateTime EndDate)
        {
           var list =  await  _datacontext.Orders
             .Include(o => o.Rider)
             .AsQueryable().Where(o => o.Date.Date >= StartDate && o.Date.Date <= EndDate && o.Rider != null)
            .AsAsyncEnumerable().GroupBy( a => a.Rider)
            .SelectAwait( async c =>
              new OrdersByRiderList{
                FirstName = c.Key.FirstName,
                Id = c.Key.Id,
                Orders = await c.CountAsync(),
                LastName = c.Key.LastName,
                photoUrl = c.Key.PhotoUrl
            
            }).OrderByDescending(o => o.Orders).ToListAsync();

            return list;
        }

        public async Task<ICollection<Rider>> GetAllActiveRiders()
        {
            return await  _datacontext.Riders.AsQueryable().Where( p => !p.Deactivated).ToListAsync();
        }

        public async Task<Cart> GetLastCart()
        {
            return await _datacontext.Carts.AsAsyncEnumerable().LastOrDefaultAsync();
        }

        public void Add<T>(T entity) where T : class
        {     
            _datacontext.Add(entity);
        }

        public async Task<Cart> GetCart(int Id)
        {
            return await _datacontext.Carts.AsQueryable().Where(c => c.Id == Id).Include(c=> c.Client).FirstOrDefaultAsync();
        }

        public async  Task<Cart> GetCartWithOrdersint(int Id)
        {
            return await _datacontext.Carts.AsQueryable().Where(c => c.Id == Id).Include(c => c.Orders)
                                            .ThenInclude(o => o.Address).Include(c => c.Orders)
                                            .ThenInclude(o => o.Service).Include(c => c.Orders)
                                            .ThenInclude(o => o.PickupItems).ThenInclude(p => p.Photos)
                                            .Include(c => c.Orders).ThenInclude(o => o.ProductOrders)
                                            .ThenInclude(p => p.Product)
                                            .FirstOrDefaultAsync();
        }

        public async  Task<Cart> GetCartWithoutOrders(int Id)
        {
            

            return await _datacontext.Carts.AsQueryable().Where(c => c.Id == Id).FirstOrDefaultAsync();
        }
        
        public async Task<ICollection<ProductOrder>> GetProductOrdersByOrder(int OrderId)
        {
            return await _datacontext.ProductOrders.AsQueryable().Where(p => p.OrderId == OrderId).ToListAsync();
        }

        public async Task<ICollection<PickupItem>> GetPickupsByOrder(int OrderId)
        {
            return await _datacontext.PickupItems.AsQueryable().Where(p => p.OrderId == OrderId).ToListAsync();
        }
        
        
        public async Task<ICollection<int>> GetPickupsByOrderIds(int OrderId)
        {
           return await _datacontext.PickupItems.AsQueryable().Where(o => o.OrderId == OrderId).Select(o => o.Id).ToListAsync();
        }

        public void AddAll<T>(IEnumerable<T> entities) where T : class
        {
            _datacontext.AddRange(entities);
        }

        public async Task<PickupItem> GetPickup(int OrderId)
        {
           return await _datacontext.PickupItems.AsQueryable().Where(p => p.Id == OrderId).FirstOrDefaultAsync();
        }

        public async Task<PickupItem> GetPickupWithPhotos(int OrderId)
        {
           return await _datacontext.PickupItems.AsQueryable().Where(p => p.Id == OrderId).Include(o => o.Photos).FirstOrDefaultAsync();
        }

        public async  Task<ProductOrder> GetProductOrder(int ProductId, int OrderId)
        {
            return await _datacontext.ProductOrders
                        .Include(p => p.Product)
                        .AsQueryable().Where(p => p.ProductId == ProductId && p.OrderId == OrderId).FirstOrDefaultAsync();
        }

        public async  Task<ICollection<ProductOrder>> GetProductOrders(int[] ProductId, int OrderId)
        {
            if(null == ProductId || ProductId.Length == 0)
            return new List<ProductOrder>();
            return await _datacontext.ProductOrders
            .Include(p => p.Product)
                        .AsQueryable().Where(p => ProductId.Contains(p.ProductId) && p.OrderId == OrderId).ToListAsync();
        }
        public async  Task<ICollection<PickupItem>> GetPickupsByOrderId(int[] pickupIds, int OrderId)
        {
             if(null == pickupIds || pickupIds.Length == 0)
            return new List<PickupItem>();
            return await _datacontext.PickupItems
                        .AsQueryable().Where(p => pickupIds.Contains(p.Id) && p.OrderId == OrderId).ToListAsync();
        }

        public async Task<ICollection<int>> GetOrderIdsForCart(int CartId)
        {
            return await _datacontext.Orders.AsQueryable().Where(o => o.CartId == CartId ).Select(o => o.Id).ToListAsync();
        }

        public async Task<ICollection<string>> GetOrderTrackingIdsForCart(int CartId)
        {
            return await _datacontext.Orders.AsQueryable().Where(o => o.CartId == CartId ).Select(o => o.TrackingId).ToListAsync();
        }

        public void DetachAllEntities()
        {
            var changedEntriesCopy = _datacontext.ChangeTracker.Entries()
                .AsQueryable().Where(e => e.State == EntityState.Added ||
                            e.State == EntityState.Modified ||
                            e.State == EntityState.Deleted || 
                            e.State == EntityState.Unchanged)
                .ToList();

            foreach (var entry in changedEntriesCopy)
                entry.State = EntityState.Detached;
        }

        public void DetachEntity<T>(T entity) where T : class
        {

            _datacontext.Entry(entity).State = EntityState.Detached;
        }

        public void ReloadEntity<T>(T entity) where T : class
        {
            _datacontext.Entry(entity).Reload();
        }

        public void DetachEntities<T>(IEnumerable<T> entities) where T : class
        {
            foreach (var entity in entities)
            {
                _datacontext.Entry(entity).State = EntityState.Detached;
            }
        }

        public async Task<ICollection<Photo>> GetAllPhotosBelongingToCart(int CartId)
        {
            return 
            await _datacontext.Photos.AsQueryable().Where(p => p.PickupItem.Order.ClientId == CartId).ToListAsync();
        }

        public async  Task<ICollection<Order>> GetOrdersByCart(int CartId)
        {
           return await _datacontext.Orders.AsQueryable().Where(r => r.CartId == CartId).Include(p => p.PickupItems)
                                                        .Include(p => p.ProductOrders).ToListAsync();
        }

        public async Task<ICollection<Cart>> GetCartsByUser(int UserId)
        {
            return await _datacontext.Carts.AsQueryable().Where(c => c.ClientId == UserId)
                                            .Include(c => c.Orders)
                                            .ThenInclude(o => o.PickupItems).Include(c => c.Orders)
                                            .ThenInclude(o => o.ProductOrders).Include(c => c.Orders)
                                            .ThenInclude(o => o.Address).Include(c => c.Orders)
                                            .ThenInclude(o => o.Service).Include(c => c.Orders)
                                            .ThenInclude(o => o.PickupItems).ThenInclude(p => p.Photos)
                                            .ToListAsync();
        }
        //note a rider is usually assigned an order not a cart so this pulls a cart based on the order assigned to the rider
        public async Task<ICollection<CartListViewForRider2>> GetCartSummaryByRider(int UserId)
        {
            var pis = _datacontext.PickupItems.Include(a => a.Address)
                .Include(p => p.Order)
                .Include(p => p.Photos)
                .AsQueryable().Where(a => a.Order.RiderId == UserId && a.Order.State.CompareTo("Delivered") != 0);

            var pos =   _datacontext.ProductOrders
                               .Include(p => p.Order)
                               .Include(p => p.Product)
                                .ThenInclude(p => p.Vendor)
                                .ThenInclude(p => p.Address)
                               .AsQueryable()
                        .Where(po => po.Order.RiderId == UserId && po.Order.State.CompareTo("Delivered") != 0);
                        return await GetCartSumaryByRider(pos,pis, UserId);

        //     var result =  await _datacontext.Orders
        //                     .Include(p => p.Cart)
        //                     .ThenInclude(p=> p.Client)
        //                     .AsQueryable().Where(o => o.RiderId == UserId)
        //                                                         .AsAsyncEnumerable()
        //                                                         .GroupBy(g => g.Cart)
        //                                                         .Select(c => 
        //                                                         new CartListViewForRider{
        //                                                             ClientId = c.Key.ClientId,
        //                                                             Lastname = c.Key.Client.LastName,
        //                                                             FirstName = c.Key.Client.FirstName,
        //                                                             photoUrl = c.Key.Client.PhotoUrl,
        //                                                             OutOf = pos.Where(po => po.Order.CartId == c.Key.Id ).Count(),
        //                                                             Completed =  pos.Where(po => po.Order.CartId == c.Key.Id && 
        //                                                                                    "Ready".CompareTo(po.State) == 0).Count(),
        //                                                             Date =   c.Key.Date,
        //                                                             CartId = c.Key.Id
        //                                                         }).OrderBy(d => d.Date).ToListAsync();

        //     return result;
        //
         }



         public async Task<ICollection<CartListViewForRider2>> GetDeliveredCartSummaryByRider(int UserId)
        {
            var pis = _datacontext.PickupItems.Include(a => a.Address)
                .Include(p => p.Order)
                .Include(p => p.Photos)
                .AsQueryable().Where(a => a.Order.RiderId == UserId && a.Order.State.CompareTo("Delivered") == 0);
             var pos =   _datacontext.ProductOrders
                               .Include(p => p.Order)
                                .Include(p => p.Product)
                                .ThenInclude(p => p.Vendor)
                                .ThenInclude(p => p.Address)
                               .AsQueryable()
                        .Where(po => po.Order.RiderId == UserId && po.Order.State.CompareTo("Delivered") == 0);
                        return await GetCartSumaryByRider(pos, pis,  UserId);
            //   var result =  await _datacontext.Orders
            //                 .Include(p => p.Cart)
            //                 .ThenInclude(p=> p.Client)
            //                 .AsQueryable().Where(o => o.RiderId == UserId && o.State.CompareTo("Delivered") == 0)
            //                                                     .AsAsyncEnumerable()
            //                                                     .GroupBy(g => g.Cart)
            //                                                     .Select(c => 
            //                                                     new CartListViewForRider{
            //                                                         ClientId = c.Key.ClientId,
            //                                                         Lastname = c.Key.Client.LastName,
            //                                                         FirstName = c.Key.Client.FirstName,
            //                                                         photoUrl = c.Key.Client.PhotoUrl,
            //                                                         OutOf = pos.Where(po => po.Order.CartId == c.Key.Id ).Count(),
            //                                                         Completed =  pos.Where(po => po.Order.CartId == c.Key.Id && 
            //                                                                                "Ready".CompareTo(po.State) == 0).Count(),
            //                                                         Date =   c.Key.Date,
            //                                                         CartId = c.Key.Id
            //                                                     }).OrderBy(d => d.Date).ToListAsync();

            // return result;
           
        }
    
         public async Task<ICollection<CartListViewForRider2>> GetDeliveredCartSummaryByRider(int UserId, DateTime start, DateTime end, string SearchParam)
        {
           
             var pis = _datacontext.PickupItems.Include(a => a.Address)
                .Include(p => p.Order)
                .Include(p => p.Photos)
                .AsQueryable().Where(a => a.Order.RiderId == UserId && a.Order.Date <= end &&
                                        a.Order.Date >= start && a.Order.State.CompareTo("Delivered") == 0);
             var pos =   _datacontext.ProductOrders
                               .Include(p => p.Order)
                                .Include(p => p.Product)
                                .ThenInclude(p => p.Vendor)
                                .ThenInclude(p => p.Address)
                               .AsQueryable()
                        .Where(po => po.Order.RiderId == UserId  && po.Order.Date <= end &&
                                     po.Order.Date >= start && po.Order.State.CompareTo("Delivered") == 0);

                        if(!string.IsNullOrEmpty(SearchParam))
                        {
                             SearchParam = SearchParam.ToUpper();
                            pis = pis.AsQueryable().Where(a => a.Order.SearchParam.Contains(SearchParam));
                            pos = pos.AsQueryable().Where(a => a.Order.SearchParam.Contains(SearchParam));
                        }
                                     
                        return await GetCartSumaryByRider(pos, pis, UserId);

            
        }

         public async Task<ICollection<CartListViewForRider2>> GetOntheWayCartSummaryByRider(int UserId, string SearchParam){
            var pis = _datacontext.PickupItems.Include(a => a.Address)
                .Include(p => p.Order)
                .Include(p => p.Photos)
                .AsQueryable().Where(a => a.Order.RiderId == UserId && a.Order.State.CompareTo("On The Way") == 0);
             var pos =   _datacontext.ProductOrders
                               .Include(p => p.Order)
                                .Include(p => p.Product)
                                .ThenInclude(p => p.Vendor)
                                .ThenInclude(p => p.Address)
                               .AsQueryable()
                        .Where(po => po.Order.RiderId == UserId && po.Order.State.CompareTo("On The Way") == 0);
                        if(!string.IsNullOrEmpty(SearchParam))
                        {
                             SearchParam = SearchParam.ToUpper();
                            pis = pis.AsQueryable().Where(a => a.Order.SearchParam.Contains(SearchParam));
                            pos = pos.AsQueryable().Where(a => a.Order.SearchParam.Contains(SearchParam));
                        }
                        return await GetCartSumaryByRider(pos, pis, UserId);
         }
         
         public async Task<ICollection<CartListViewForRider2>> GetOntheWayCartSummaryByRider(int UserId, DateTime start, DateTime end,  string SearchParam)
        {

            var pis = _datacontext.PickupItems.Include(a => a.Address)
                .Include(p => p.Order)
                .Include(p => p.Photos)
                .AsQueryable().Where(a => a.Order.RiderId == UserId && a.Order.Date <= end &&
                                     a.Order.Date >= start && a.Order.State.CompareTo("On The Way") == 0);

             var pos =   _datacontext.ProductOrders
                               .Include(p => p.Order)
                                .Include(p => p.Product)
                                .ThenInclude(p => p.Vendor)
                                .ThenInclude(p => p.Address)
                               .AsQueryable()
                        .Where(po => po.Order.RiderId == UserId && po.Order.State.CompareTo("On The Way") == 0);

                        if(!string.IsNullOrEmpty(SearchParam))
                        {
                             SearchParam = SearchParam.ToUpper();
                            pis = pis.AsQueryable().Where(a => a.Order.SearchParam.Contains(SearchParam));
                            pos = pos.AsQueryable().Where(a => a.Order.SearchParam.Contains(SearchParam));
                        }
                        return await GetCartSumaryByRider(pos, pis, UserId);
        }

        private async Task<ICollection<CartListViewForRider2>> GetCartSumaryByRider(IQueryable<ProductOrder> pos, IQueryable<PickupItem> pis,  int UserId)
        {
            var sam = pos.AsEnumerable().Select(po => po.Order.State).ToList();
            var cartIds = pos.AsEnumerable().Select(po => po.Order.CartId).Distinct();
            var cartIds2 = pis.AsEnumerable().Select(po => po.Order.CartId).Distinct();
           var result =  await _datacontext.Orders
                            .Include(p => p.Cart)
                            .ThenInclude(p=> p.Client)
                            .AsQueryable().Where(o => o.RiderId == UserId && cartIds.Contains(o.CartId) || cartIds2.Contains(o.CartId))
                                                                .AsAsyncEnumerable()
                                                                .GroupBy(g => g.Cart)
                                                                .Select(c => 
                                                                new CartListViewForRider2{
                                                                    ClientId = c.Key.ClientId,
                                                                    Lastname = c.Key.Client.LastName,
                                                                    FirstName = c.Key.Client.FirstName,
                                                                    photoUrl = c.Key.Client.PhotoUrl,
                                                                    OutOf = pos.Where(po => po.Order.CartId == c.Key.Id ).Count(),
                                                                    Completed =  pos.Where(po => po.Order.CartId == c.Key.Id && 
                                                                                           "Ready".CompareTo(po.State) == 0).Count(),
                                                                    Date =   c.Key.Date,
                                                                    CartId = c.Key.Id,
                                                                      Vendors =  pos.AsQueryable().Where(po => po.Order.CartId == c.Key.Id)
                                                                      .AsEnumerable().GroupBy(g => g.Product.Vendor)
                                                                    .Select(  v => 
                                                                        new VendorOrderForRider{
                                                                            Name = v.Key.Name,
                                                                            Address = null == v.Key.Address ? null :new DTO.Shared.AddressDto{
                                                                                        Street = v.Key.Address.Street ?? "",
                                                                                        City = v.Key.Address.City ?? "",
                                                                                        State = v.Key.Address.State ?? ""
                                                                            }
                                                                        }
                                                                    ).ToList(),
                                                                   Pickups = pis.AsQueryable().Where(po => po.Order.CartId == c.Key.Id)
                                                                                .AsEnumerable().Select( p => 
                                                                   new PickupItemDto{
                                                                    Name = p.Name,
                                                                    OrderId = p.OrderId,
                                                                    Fragile = p.Fragile,
                                                                    Size    = p.Size,
                                                                    Photos = p.Photos.Select(ph => new DTO.Shared.PhotoDto{
                                                                        Id = ph.Id,
                                                                        Url = ph.Url
                                                                    }).ToList(),
                                                                    Address = null == p.Address ? null :new DTO.Shared.AddressDto2{
                                                                                        Street = p.Address.Street ?? "",
                                                                                        City = p.Address.City ?? "",
                                                                                        State = p.Address.State ?? "",
                                                                                        LastName = p.Address.LastName ?? "",
                                                                                        FirstName = p.Address.FirstName ?? "",
                                                                                        Phone = p.Address.Phone ?? ""
                                                                            },
                                                                    AddressId = p.AddressId?? 0,
                                                                   }).ToList(),
                                                                   
                                                                State = pos.AsQueryable().Where(po => po.Order.CartId == c.Key.Id).AsEnumerable().Select(p => p.Order.State).FirstOrDefault()
                                                                        ?? pis.AsQueryable().Where(pi => pi.Order.CartId == c.Key.Id).AsEnumerable().Select(p => p.Order.State).FirstOrDefault(),
                                                                }).OrderBy(d => d.Date).ToListAsync();

            return result;
        }

        public async Task<ICollection<CartListViewForRider2>> GetCartSummaryByRider(int UserId, DateTime date)
        {
            var pis = _datacontext.PickupItems.Include(p => p.Address)
                        .Include(p => p.Order)
                        .Include(p => p.Photos)
                        .AsQueryable().Where(po => po.Order.RiderId == UserId  && po.Order.Date.DayOfYear == date.DayOfYear 
                                                            && po.Order.Date.Year == date.Year
                                                            && po.Order.State.CompareTo("Delivered") != 0);
                                                            

             var pos =   _datacontext.ProductOrders
                               .Include(p => p.Order)
                               .Include(p => p.Product)
                                .ThenInclude(p => p.Vendor)
                                .ThenInclude(p => p.Address)
                               .AsQueryable()
                        .Where(po => po.Order.RiderId == UserId  && po.Order.Date.DayOfYear == date.DayOfYear 
                                                            && po.Order.Date.Year == date.Year
                                                            && po.Order.State.CompareTo("Delivered") != 0);
                                                            return await GetCartSumaryByRider(pos,pis,  UserId);

        //     var result =  await _datacontext.Orders
        //                     .Include(p => p.Cart)
        //                     .ThenInclude(p=> p.Client)
        //                     .AsQueryable().Where(o => o.RiderId == UserId && o.Date.DayOfYear == date.DayOfYear 
        //                                                                 && o.Date.Year == date.Year 
        //                                                                 && o.State.CompareTo("Delivered") != 0)
        //                                                         .AsAsyncEnumerable()
        //                                                         .GroupBy(g => g.Cart)
        //                                                         // .WhereAwait(async g => await g.CountAsync() > 0 )
        //                                                         .Select(c => 
        //                                                         new CartListViewForRider{
        //                                                             ClientId = c.Key.ClientId,
        //                                                             Lastname = c.Key.Client.LastName,
        //                                                             FirstName = c.Key.Client.FirstName,
        //                                                             photoUrl = c.Key.Client.PhotoUrl,
        //                                                             OutOf = pos.Where(po => po.Order.CartId == c.Key.Id ).Count(),
        //                                                             Completed =  pos.Where(po => po.Order.CartId == c.Key.Id && 
        //                                                                                    "Ready".CompareTo(po.State) == 0).Count(),
        //                                                             Date =   c.Key.Date,
        //                                                             CartId = c.Key.Id
        //                                                         }).OrderBy(d => d.Date).ToListAsync();
            
        //     return result;
        // 
        }
        public async Task<ICollection<CartListViewForRider2>> GetPendingCartSummaryByRider(int UserId, string SearchParam)
        {
            SearchParam = SearchParam.ToUpper().Trim();
            var pis = _datacontext.PickupItems.Include(o => o.Address)
                        .Include(p => p.Order)
                        .Include(p => p.Photos)
                        .AsQueryable().Where(po => po.Order.RiderId == UserId && po.Order.State.CompareTo("Pending") == 0);

            var pos =   _datacontext.ProductOrders
                               .Include(p => p.Order)
                               .Include(p => p.Product)
                                .ThenInclude(p => p.Vendor)
                                .ThenInclude(p => p.Address)
                               .AsQueryable()
                        .Where(po => po.Order.RiderId == UserId && po.Order.State.CompareTo("Pending") == 0);

            if(!String.IsNullOrEmpty(SearchParam))
            {
                 SearchParam = SearchParam.ToUpper().Trim();
                pos = pos.Where(po => po.Order.SearchParam.Contains(SearchParam));
                pis = pis.Where(po => po.Order.SearchParam.Contains(SearchParam));
            }
            return await GetCartSumaryByRider(pos, pis, UserId);
        }
        public async Task<ICollection<CartListViewForRider2>> GetPendingCartSummaryByRider(int UserId, DateTime start, DateTime end, string SearchParam)
        {
            
            var pis = _datacontext.PickupItems.Include(o => o.Address)
                        .Include(p => p.Order)
                        .Include(p => p.Photos)
                        .Where(po => po.Order.RiderId == UserId  && po.Order.Date <= end &&
                                     po.Order.Date >= start && po.Order.State.CompareTo("Pending") == 0);

            var pos =   _datacontext.ProductOrders
                               .Include(p => p.Order)
                               .Include(p => p.Product)
                                .ThenInclude(p => p.Vendor)
                                .ThenInclude(p => p.Address)
                               .AsQueryable()
                        .Where(po => po.Order.RiderId == UserId  && po.Order.Date <= end &&
                                     po.Order.Date >= start && po.Order.State.CompareTo("Pending") == 0);

             if(!String.IsNullOrEmpty(SearchParam))
            {
                 SearchParam = SearchParam.ToUpper();
                pos = pos.Where(po => po.Order.SearchParam.Contains(SearchParam));
                pis = pis.Where(po => po.Order.SearchParam.Contains(SearchParam));
            }
            return await GetCartSumaryByRider(pos,pis,  UserId);
        }
        public async Task<ICollection<CartListViewForRider2>> GetCartSummaryByRider(int UserId, DateTime start, DateTime end, string SearchParam )
        {
         
             var pis = _datacontext.PickupItems.Include(o => o.Address)
                        .Include(p => p.Order)
                        .Include(p => p.Photos)
                        .Where(po => po.Order.RiderId == UserId  && po.Order.Date <= end &&
                                     po.Order.Date >= start && po.Order.State.CompareTo("Delivered") != 0 );
                        
            var pos =   _datacontext.ProductOrders
                               .Include(p => p.Order)
                               .Include(p => p.Product)
                                .ThenInclude(p => p.Vendor)
                                .ThenInclude(p => p.Address)
                               .AsQueryable()
                        .Where(po => po.Order.RiderId == UserId  && po.Order.Date <= end &&
                                     po.Order.Date >= start && po.Order.State.CompareTo("Delivered") != 0);
            
            if(!String.IsNullOrEmpty(SearchParam))
            {
                 SearchParam = SearchParam.ToUpper().Trim();
                pos = pos.Where(po => po.Order.SearchParam.Contains(SearchParam));
                pis = pis.Where(po => po.Order.SearchParam.Contains(SearchParam));
            }
            return await GetCartSumaryByRider(pos, pis, UserId);
            

            // var result =  await _datacontext.Orders
            //                 .Include(p => p.Cart)
            //                 .ThenInclude(p=> p.Client)
            //                 .AsQueryable().Where(o => o.RiderId == UserId
            //                                         && o.Date <= end && o.Date >= start 
            //                                         && o.State.CompareTo("Delivered") != 0)
            //                                                     .AsAsyncEnumerable()
            //                                                     .GroupBy(g => g.Cart)
            //                                                     .Select(c => 
            //                                                     new CartListViewForRider{
            //                                                         ClientId = c.Key.ClientId,
            //                                                         Lastname = c.Key.Client.LastName,
            //                                                         FirstName = c.Key.Client.FirstName,
            //                                                         photoUrl = c.Key.Client.PhotoUrl,
            //                                                         OutOf = pos.Where(po => po.Order.ClientId == c.Key.ClientId ).Count(),
            //                                                         Completed =  pos.Where(po => po.Order.CartId == c.Key.Id && 
            //                                                                                "Ready".CompareTo(po.State) == 0).Count(),
            //                                                         Date =   c.Key.Date,
            //                                                         CartId = c.Key.Id
            //                                                     }).ToListAsync();
                                                                 

            // return result;
        }

        

        public async Task<ICollection<Cart>> GetNonPendingCartsByUser(int UserId)
        {
            return await _datacontext.Carts.AsQueryable().Where(c => c.ClientId == UserId && c.State != "Pending")
                                            .Include(c => c.Orders)
                                            .ThenInclude(o => o.PickupItems).Include(c => c.Orders)
                                            .ThenInclude(o => o.ProductOrders).ThenInclude(p => p.Product)
                                            .ThenInclude(p => p.Photos)
                                            .Include(c => c.Orders)
                                            .ThenInclude(o => o.Address).Include(c => c.Orders)
                                            .ThenInclude(o => o.Service).Include(c => c.Orders)
                                            .ThenInclude(o => o.PickupItems).ThenInclude(p => p.Photos).
                                            OrderByDescending(t => t.Date)
                                            .ToListAsync();
        }

        public async Task<Cart> GetCartByTrackingId(string trackingId)
        {
            trackingId = trackingId.ToLower();
             return await _datacontext.Carts.AsQueryable().Where(c => c.TrackingId.CompareTo(trackingId) == 0).Include(c => c.Orders)
                                            .ThenInclude(o => o.PickupItems).ThenInclude(a => a.Address)
                                             .Include(c => c.Orders).ThenInclude(o => o.Address)
                                            .Include(c => c.Orders)
                                            .ThenInclude(o => o.ProductOrders)
                                            .ThenInclude(p=> p.Product).ThenInclude(p => p.Photos)
                                            .Include(c => c.Orders)
                                            .ThenInclude(o => o.Service).Include(c => c.Orders)
                                            .ThenInclude(o => o.PickupItems).ThenInclude(p => p.Photos)
                                            .FirstOrDefaultAsync();
        }

        public async Task<Cart> GetCurrentPendingCart(int ClientId) 
        {
            return await _datacontext.Carts.AsQueryable().Where(c => c.State.CompareTo("Pending") == 0 && c.ClientId == ClientId)
                                            .Include(c => c.Orders)
                                                .ThenInclude(a => a.Address)
                                            .Include(c => c.Orders)
                                                .ThenInclude(o => o.PickupItems).ThenInclude(a => a.Address)
                                            .Include(c => c.Orders)
                                                .ThenInclude(o => o.ProductOrders).ThenInclude(p=> p.Product).ThenInclude(p => p.Photos)
                                            .Include(c => c.Orders)
                                                .ThenInclude(o => o.ProductOrders).ThenInclude(p=> p.Product).ThenInclude(p => p.Vendor)
                                            .Include(c => c.Orders)
                                                .ThenInclude(o => o.Service)
                                            .Include(c => c.Orders)
                                                .ThenInclude(o => o.PickupItems).ThenInclude(p => p.Photos)
                                            .OrderByDescending(o => o.Date).FirstOrDefaultAsync();
        }

        public async Task<Order> GetOrderByTrackingId(string trackingId)
        {
            trackingId = trackingId.ToLower();
             return await _datacontext.Orders.AsQueryable().Where(c => c.TrackingId.CompareTo(trackingId) == 0)
                                            .Include(o => o.PickupItems).ThenInclude(p => p.Address)
                                            .Include(o => o.Cart)
                                            .Include(o => o.ProductOrders).ThenInclude(o => o.Product)
                                            .ThenInclude(o => o.Photos)
                                            .Include(o => o.Address)
                                            .Include(o => o.Service)
                                            .Include(o => o.PickupItems).ThenInclude(p => p.Photos)
                                            .FirstOrDefaultAsync();
        }

        public async Task<ICollection<ProductOrder>> GetProductOrdersByVendor(int VendorId)
        {
           var orders = await  _datacontext.ProductOrders.AsQueryable().Where(p => p.Product.VendorId == VendorId)
                        .Include(p => p .Product).ToListAsync();
            return orders;
        }

        public async Task<ICollection<VendorOrder>> GetUnpreparedOrdersByVendorGroupedByClient(int VendorId)
        {
            var POrders =  _datacontext.ProductOrders.AsQueryable().Where(p => p.Product.VendorId == VendorId
                                                                               && "Pending".CompareTo(p.State)== 0)
                                                        .Include(p => p.Product)
                                                        .Include(p => p.Order)
                                                        .ThenInclude(o => o.Client);
                                                        //.Include(p => p.Order)
                                                       // .the;
            var orders = await POrders.AsAsyncEnumerable().GroupBy(po => po.Order)
                                                            .Select(g => new VendorOrder{
                                                            FirstName = g.Key.Client.FirstName,
                                                            LastName = g.Key.Client.LastName,
                                                            Date = g.Key.Date,
                                                            Total = POrders.Where(po => po.OrderId == g.Key.Id)
                                                                    .Sum(p => p.ProductPrice * p.Quantity),
                                                            Products =  POrders.Where(po => po.OrderId == g.Key.Id)
                                                                            .Select(pd => new ProductDto3{
                                                                                Name = pd.Product.Name,
                                                                                Price = pd.Product.Price,
                                                                                State = pd.State,
                                                                                quantity = pd.Quantity,
                                                                                Id = pd.ProductId,
                                                                                OrderId = pd.OrderId
                                                                            }).ToList()
                                                            }).ToListAsync(); 

            return orders;
            
        }

        public async Task<ICollection<VendorOrder>> GetTodayPendingOrdersByVendorGroupedByClient(int VendorId)
        {
            var now = DateTime.Now;
            var POrders =  _datacontext.ProductOrders.AsQueryable().Where(p => p.Product.VendorId == VendorId &&
                                                                                now.DayOfYear == p.Order.Date.DayOfYear
                                                                                && now.Year == p.Order.Date.Year && 
                                                                                "Pending".CompareTo(p.State)== 0)
                                                        .Include(p => p.Product)
                                                        .Include(p => p.Order);

             var orders = await POrders.AsAsyncEnumerable().GroupBy(po => po.Order)
                                                            .Select(g => new VendorOrder{
                                                            FirstName = g.Key.Client.FirstName,
                                                            LastName = g.Key.Client.LastName,
                                                            Date = g.Key.Date,
                                                            Total = POrders.Where(po => po.OrderId == g.Key.Id)
                                                                    .Sum(p => p.ProductPrice * p.Quantity),
                                                            Products =  POrders.Where(po => po.OrderId == g.Key.Id)
                                                                            .Select(pd => new ProductDto3{
                                                                                Name = pd.Product.Name,
                                                                                Price = pd.Product.Price,
                                                                                State = pd.State,
                                                                                quantity = pd.Quantity,
                                                                                Id = pd.ProductId,
                                                                                OrderId = pd.OrderId
                                                                            }).ToList()
                                                            }).ToListAsync(); 

            return orders ;
            
        }

        public async Task<Cart> GetCartDetailsForRider(int cartId, int userId)
        {
           var result =  await _datacontext.Orders.Include(c =>c.Cart)
                                                    .ThenInclude(c => c.Client)
                                        .AsQueryable().Where(o => o.RiderId == userId  
                                        &&  o.CartId == cartId).Select(c => c.Cart).FirstOrDefaultAsync();
            return result;

        }

        public async Task<ICollection<PickupItem>> GetPickupItemsByRiderAndCart(int userId, int cartId)
        {
            var res = await _datacontext.PickupItems.Include(o => o.Order)
                                                    .Include(a => a.Address)
                                                    .Include(p =>p.Photos)
                                                    .AsQueryable()
                                                    .Where(p => p.Order.RiderId == userId && p.Order.CartId == cartId)
                                                    .ToListAsync();
            return res;
        }

        public async Task<ICollection<VendorOrderForRider>> GetRiderProductOrdersGroupedByVendors(int cuserId, int cartId){
             var pOrder = _datacontext.ProductOrders.Include(po => po.Product)
                                                      .ThenInclude(p => p.Vendor)
                                                      .ThenInclude(v => v.Address)
                                                      .Include(o => o.Order)
                                                      .Include(p => p.Product)
                                                      .ThenInclude( p => p.Photos)
                                                      .AsQueryable()
                                                      .Where(po => po.Order.CartId == cartId 
                                                      && po.Order.RiderId == cuserId);

            var vendors = await pOrder.AsAsyncEnumerable().GroupBy(g => g.Product.Vendor)
                                                          .Select(  v => 
                                                            new VendorOrderForRider{
                                                                Name = v.Key.Name,
                                                                Phone = v.Key.Phone,
                                                                Address = new DTO.Shared.AddressDto{
                                                                            Street = v.Key.Address.Street,
                                                                            City = v.Key.Address.City,
                                                                            State = v.Key.Address.State
                                                                },
                                                                Products =  pOrder.Where(po => po.Product.VendorId == v.Key.Id)
                                                                            .Select(pd => new ProductDto3{
                                                                                Name = pd.Product.Name,
                                                                                Price = pd.Product.Price,
                                                                                State = pd.State,
                                                                                quantity = pd.Quantity,
                                                                                Id = pd.ProductId,
                                                                                OrderId = pd.OrderId,
                                                                                PhotoUrl = pd.Product.Photos.FirstOrDefault().Url
                                                                            }).ToList(),
                                                                
                                                                Total = pOrder.Where(po => po.Product.VendorId == v.Key.Id).Sum(
                                                                    w => w.ProductPrice * w.Quantity
                                                                )
                                                                            ,
                                                            }
                                                          ).ToListAsync();

            return vendors;
        }
 
        public async Task<ICollection<VendorOrder>> GetOrdersByVendorAndDateRangeGroupedByClient(int VendorId, DateTime start, DateTime end)
        {
            var POrders =  _datacontext.ProductOrders.AsQueryable().Where(p => p.Product.VendorId == VendorId
                                                                               && "Pending".CompareTo(p.State)== 0 &&
                                                                                p.Order.Date >= start.Date && p.Order.Date <= end.Date )
                                                        .Include(p => p.Product)
                                                        .Include(p => p.Order);

                var orders = await POrders.AsAsyncEnumerable().GroupBy(po => po.Order)
                                                            .Select(g => new VendorOrder{
                                                            FirstName = g.Key.Client.FirstName,
                                                            LastName = g.Key.Client.LastName,
                                                            Date = g.Key.Date,
                                                            Total = POrders.Where(po => po.OrderId == g.Key.Id)
                                                                    .Sum(p => p.ProductPrice * p.Quantity),
                                                            Products =  POrders.Where(po => po.OrderId == g.Key.Id)
                                                                            .Select(pd => new ProductDto3{
                                                                                Name = pd.Product.Name,
                                                                                Price = pd.Product.Price,
                                                                                State = pd.State,
                                                                                quantity = pd.Quantity,
                                                                                Id = pd.ProductId,
                                                                                OrderId = pd.OrderId
                                                                            }).ToList()
                                                            }).ToListAsync(); 

            return orders;
        }

        public Task<ICollection<ProductOrder>> GetOrdersByVendor(int VendorId)
        {
            throw new NotImplementedException();
        }

        public async  Task<Cart> GetCartByTransactionReference(string Tx_ref)
        {
             return await _datacontext.Carts.AsQueryable().Where(c => Tx_ref.CompareTo(c.Tx_ref) == 0 )
                                            .Include(c=> c.Client).FirstOrDefaultAsync();
        }

        public async Task<ICollection<ProductOrder>> GetProductOrdersByClient(int clinentID)
        {
            var  productOrders =  _datacontext.ProductOrders
                                                        .AsQueryable()
                                                        .Where(po => po.Order.ClientId == clinentID);
            return await  productOrders.ToListAsync();
        }

        public async Task<ICollection<Order>> GetOrderByCartAndRider(int cartId, int riderId){
            return await _datacontext.Orders.AsQueryable().Where(o => o.CartId == cartId && o.RiderId == riderId).ToListAsync();
        }

        public async Task<ICollection<RiderEarningDto>> GetRiderEarningsForDateRange(DateTime startTime, DateTime endTime, int riderID)
        {
           
           var earnings = await _datacontext.Orders.AsQueryable()
           .Where(o => o.RiderId == riderID && o.Date >= startTime && o.Date <= endTime && o.State.CompareTo("Delivered") == 0)
           .Include(p => p.Client)
           .Include(p => p.Address)
           .Select(o => new RiderEarningDto{
               Date = o.Date,
               Earninig = o.RiderEarning,
               ClientName = o.Client.FirstName + " " + o.Client.LastName,
               Address = new AddressDto2{
                     City = o.Address.City,
                     State = o.Address.State,
                     Street = o.Address.Street
               }
           }).ToListAsync();

           return earnings;
        
        }
    
        public async Task<ICollection<RiderEarningGroup>> GetRiderEarningsGroupedByWeek(int userId)
        {
            var orders = await _datacontext.Orders.AsQueryable()
           .Where(o => o.RiderId == userId ).ToListAsync();
            int count = 1;
           var earnings = orders
            .GroupBy(order => CultureInfo.InvariantCulture.Calendar.GetWeekOfYear(
                order.Date,
                CalendarWeekRule.FirstDay,
                DayOfWeek.Monday
            )).Select(group => new RiderEarningGroup
            {
                Name = "Week " + count++,
                Amount = group.Sum(ord => ord.RiderEarning),
                OrdersCount = group.Count(), 
                StartDate = group.OrderByDescending( o => o.Date).Last().Date,
                EndDate = group.OrderByDescending( o => o.Date).FirstOrDefault().Date
            })
            .ToList();

            return earnings;
        }

        public async Task<ICollection<RiderEarningGroup>> GetRiderEarningsGroupedByMonth(int userId)
        {
            var orders = await _datacontext.Orders.AsQueryable()
           .Where(o => o.RiderId == userId ).ToListAsync();
           var earnings = orders
            .GroupBy(order => order.Date.Month).Select(group => new RiderEarningGroup
            {
                Name = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(group.Key),
                Amount = group.Sum(ord => ord.RiderEarning),
                OrdersCount = group.Count(), 
                StartDate = group.OrderByDescending( o => o.Date).Last().Date,
                EndDate = group.OrderByDescending( o => o.Date).FirstOrDefault().Date
            })
            .ToList();

            return earnings;
        }
    
    }   
}