using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace QuickMessenger.API.Data.DTO.Client
{
    public class ClientRegisterDto
    {
        public int Id { get; set; }
        [Required]
        [EmailAddress]
        public string Email { get; set; }
        [Required]
        public string FirstName { get; set; }
        [Required]
        public string LastName { get; set; }
        [Required]
        [DataType(DataType.Password)]
        [StringLength(225, MinimumLength = 6, ErrorMessage = "Password should be at least 6 characters")]
        public string Password { get; set; }

        // [DataType(DataType.Password)]
        // [StringLength(225, MinimumLength = 6, ErrorMessage = "Password should be at least 6 characters")]
        // [Compare("Password")]
        // public string ConfirmPassword { get; set; }
        [Required]
        [Phone]
        public string Phone { get; set; }
        public string Gender { get; set; }




        [NotMapped]
        public DateTime DateRegistered { get; set; }
        [NotMapped]
        public DateTime LastActive { get; set; }

        public ClientRegisterDto()
        {
            this.DateRegistered = DateTime.Now;
            this.LastActive = DateTime.Now;
        }

        
    }
}