using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using QuickMessenger.API.Data.DTO.Client;
using QuickMessenger.API.Data.DTO.Staff;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.IService;
using QuickMessenger.API.Data.Model;
using Clant = QuickMessenger.API.Data.Model.Client;
using QuickMessenger.API.Helpers.Params;

namespace QuickMessenger.API.Controllers.Staff
{
    [ApiController]
    [Authorize(Policy = "ProdMgt")]
    [Route("api/qm_475/staff/[controller]")]
    public class ClientController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly IUserRepo _userRepo;
        private readonly IMapper _mapper;
        private readonly UserManager<User> _userManager;

        public ClientController(IUserRepo userRepo, IMapper mapper,
        IUserService userService, UserManager<User> userManager)
        {
            this._userManager = userManager;
            this._mapper = mapper;
            this._userRepo = userRepo;
            this._userService = userService;
        }

    [HttpGet]
    public async Task<IActionResult> GetClients([FromQuery]ClientListParams UserParam)
    {
        var client = await _userRepo.GetClientLazyAddress(UserParam);
        var userToReturn = _mapper.Map<IEnumerable<ClientListDto>>(client);
        var objToreturn = new
        {
            Clients = userToReturn,
            Pagination = new
            {
                CurrentPage = client.CurrentPage,
                PageSize = client.PageSize,
                TotalCount = client.TotalCount,
                TotalPages = client.TotalPages
            }
        };
        return Ok(objToreturn);
    }

    [HttpGet("/all")]
    public async Task<IActionResult> GetAllClients([FromQuery]ClientListParams UserParam)
    {
        var client = await _userRepo.GetAllClientLazyAddress(UserParam);
        var userToReturn = _mapper.Map<IEnumerable<ClientListDto>>(client);
        var objToreturn = new
        {
            Client = userToReturn,
            Pagination = new
            {
          
                CurrentPage = client.CurrentPage,
                PageSize = client.PageSize,
                TotalCount = client.TotalCount,
                TotalPages = client.TotalPages
            }
        };
        return Ok(objToreturn);
    }

    [HttpGet("{Id}")]
    public async Task<IActionResult> GetClient(int Id)
    {
        var client = await _userRepo.GetClientLazy(Id);
        var clientToReturn = _mapper.Map<ClientDetailDto>(client);
        if(client != null)
        return Ok(clientToReturn);
        return BadRequest();

    }

    [HttpPut("{Id}/activate")]
    public async Task<IActionResult> ActivateClient(int Id)
    {
        User  client = new Clant();
        client = await _userRepo.GetClient(Id);
        client.Deactivated = false;
        if(await _userRepo.SaveAll())
        return NoContent();

        return BadRequest();
    }

    [HttpPut("{Id}/deactivate")]
    public async Task<IActionResult> DeactivateClient(int Id)
    {
        User  client = new Clant();
        client = await _userRepo.GetClient(Id);
        client.Deactivated = true;
        if(await _userRepo.SaveAll())
        return NoContent();

        return BadRequest();
    }

    }
}