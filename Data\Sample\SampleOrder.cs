using System;
using System.Collections.Generic;
using QuickMessenger.API.Data.Model;

namespace QuickMessenger.API.Data.Sample
{
    public class SampleOrder
    {
         public int Id { get; set; }
        public Client Client { get; set; }
        public int ClientId { get; set; }
        public Address Address { get; set; }
        public int AddressId {get; set;}
        public DateTime Time { get; set; }
        public int RiderId { get; set; }
        public Rider Rider { get; set; }
        public ICollection<PickupItem> PickupItems { get; set; }
        public string Type { get; set; }
        public ICollection<ProductOrder> ProductOders { get; set; }
        public string State { get; set; }
        public double Cost { get; set; }
        public string SearchParam { get; set; }
        public int ServiceId { get; set; }
        public int CartId { get; set; }
    }
}