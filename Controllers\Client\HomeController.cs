using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using QuickMessenger.API.Data.DTO.Client;
using QuickMessenger.API.Data.DTO.Product;
using QuickMessenger.API.Data.DTO.Vendor;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.IService;
using Microsoft.Extensions.Configuration;

namespace QuickMessenger.API.Controllers.Client
{
    [ApiController]
    [AllowAnonymous]
    [Route("api/client/[controller]")]
    public class HomeController : ControllerBase
    {
        private readonly IVendorRepo _vendorRepo;
        private readonly IProductRepo _productRepo;
        private readonly IMapper _mapper;
        private readonly IUserService _userService;
        private readonly IUserRepo _userRepo;
        private readonly IConfiguration _config;
        public HomeController(IVendorRepo vendorRepo, IProductRepo productRepo, 
        IMapper mapper, IUserService userService, IUserRepo userRep, IConfiguration config)
        {
            _vendorRepo = vendorRepo;
            _productRepo = productRepo;
            _mapper = mapper;
            _userService = userService;
            _userRepo = userRep;
            _config = config;
    }

        [HttpGet]
        public async Task<IActionResult> GetHomePageItems()
        {
            // this method is supposed to get list of;
            //vendors and services based on their prominence
            var vendors = await _vendorRepo.GetTop6endorsWithMostOrders();
            var services = await _productRepo.GetTop6ServicesWithMostOrders();


            return Ok(new HomeItemsDto
            {
                Vendors = _mapper.Map<ICollection<VendorListDtoLite>>(vendors),
                Services = services

            });

        }

        [HttpPost("search")]
        public async Task<IActionResult> Search(string searchTerm)
        {
            if (null == searchTerm)
                searchTerm = "";
            var products = await _productRepo.GetProducts(searchTerm);
            var vendors = await _vendorRepo.GetVendors(searchTerm);

            return Ok(
                new SearchResultDto
                {
                    Products = _mapper.Map<ICollection<ProductListForClientDto>>(products),
                    Vendors = _mapper.Map<ICollection<VendorListDtoLite>>(vendors)
                });
        }

        [HttpPost("contact")]
        public async Task<IActionResult> Contact(Contact Contact)
        {
            await  _userService.ContactUS(Contact);
            return Ok();
        }

        [HttpGet("ads")]
        public async Task<IActionResult> Ads()
        {
            var ads = await _userRepo.GetAllActiveAppBannerLinks();
            return Ok(ads);
        }
    
        //get contact address and phone number
        [HttpGet("contact")]
        public async Task<IActionResult> GetContact()
        {
            //get contact from appsettings
            //new contact object
            var contact = new {
                Whatsapp = _config.GetSection("Contact:Whatsapp").Value,
                Email = _config.GetSection("Contact:Email").Value,
                Phone = _config.GetSection("Contact:Phone").Value,
            };
          
            return Ok(contact);
        }

    }

}