{"AppSettings": {"Token": "giqygidqg8cowhefo276fjhjsdf0239424024hsdnsdf3813et19gss9shqw9ed8yw0e]d8wf9ef0w8e"}, "Redirects": {"email_confirmation": "http://quickwa:4200/account/email/confirmation", "password_reset": "http://localhost:4200/account/password/reset"}, "SendGridKey": "*********************************************************************", "SendGridKey2": "*********************************************************************", "SpaStaffUrl": "http://localhost:4200/staff/", "Logging": {"LogLevel": {"Default": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=tcp:s26.winhost.com;Database=DB_140011_quickwaka;user id=DB_140011_quickwaka_user;password=********;MultipleActiveResultSets=true;"}, "SendGrid": {"ApiKey": "*********************************************************************"}, "Flutter": {"TestKey": "FLWSECK_TEST-60c4b51355ce211392c3d4e7bb0bd0a7-X"}, "GoogleMapApi": {"ApiKey": "AIzaSyA8PHgUXNFqM6FVDswrm91xy3TP8MUK6OY"}, "CloudinarySettings": {"CloudName": "victorago<PERSON>", "ApiKey": "***************", "ApiSecrete": "Xi6z14ThTJuq21bVxqoYMZnsHXU"}, "DeliveryCharge": {"baseCharge": 750, "baseDistance": 6000, "chargePerStop": 400, "subsequentBaseCharge": "120", "subsequentBaseDistance": "1000"}}