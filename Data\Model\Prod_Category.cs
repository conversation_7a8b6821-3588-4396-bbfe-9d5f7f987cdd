using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace QuickMessenger.API.Data.Model
{
    public class Prod_Category
    {
        public int Id { get; set; }
        [Required]
        public string  Name { get; set; }
        [Required]
        public string NameId { get; set; }
        public string Description { get; set; }
        public Prod_Category Parent { get; set; }
        public int? ParentId { get; set; }
        public string ImageUrl { get; set; }
        public ICollection<Prod_Category> SubCategories { get; set; }
        public ICollection<Property> Properties { get; set; }
        public ICollection<Product> Products { get; set; }
        [NotMapped]
        public bool CanDelete { get; set; }
        public string SearchParam { get; set; }

    }
}