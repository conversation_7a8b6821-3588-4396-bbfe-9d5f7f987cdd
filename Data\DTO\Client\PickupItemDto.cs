using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using QuickMessenger.API.Data.DTO.Shared;

namespace QuickMessenger.API.Data.DTO.Client
{
    public class PickupItemDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Size { get; set; }
        public double Value { get; set; }
        public bool Fragile { get; set; }
        public int OrderId { get; set; }
        public ICollection<PhotoDto>  Photos { get; set; }
        public int Quantity { get; set; }
        public int AddressId { get; set; }
        public AddressDto2 Address { get; set; }
    }

    public class PickupItemDto2
    {
        public int Id { get; set; }
        [Required]
        public string Name { get; set; }
        public string Description { get; set; }
        [Required]
        public string Size { get; set; }
        public double Value { get; set; }
        public bool Fragile { get; set; }

        public ICollection<PhotoDto>  Photos { get; set; }
        [Required]
        public int Quantity { get; set; }
        public int AddressId { get; set; }

    }

    public class PickupItemViewDto
    {
        public int Id { get; set; }
        [Required]
        public string Name { get; set; }
        public string Description { get; set; }
        [Required]
        public string Size { get; set; }
        public bool Fragile { get; set; }
        public double Value { get; set; }
        public int OrderId { get; set; }
        public ICollection<PhotoDto>  Photos { get; set; }
        [Required]
        public int Quantity { get; set; }
        public int AddressId { get; set; }
        public AddressDto2 Address { get; set; }

    }
}