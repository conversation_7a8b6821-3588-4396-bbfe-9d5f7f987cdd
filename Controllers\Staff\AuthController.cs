using System;
using System.Security.Claims;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using QuickMessenger.API.Data.DTO.Shared;
using QuickMessenger.API.Data.DTO.Staff;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.IService;
using QuickMessenger.API.Data.Model;

namespace QuickMessenger.API.Controllers.staff
{
    [ApiController]
    [AllowAnonymous]
    [Route("api/qm_475/staff/[controller]")]

    public class AuthController : ControllerBase
    {
        private readonly SignInManager<User> _signinManager;
        private readonly UserManager<User> _userManager;
        private readonly IAuthService _authService;
        private readonly IMapper _mapper;
        private readonly IUserRepo _userRepo;
        public AuthController(UserManager<User> _userManager,
        SignInManager<User> _signinManager, IAuthService _authService, IMapper _mapper, IUserRepo userRepo)
        {
            _userRepo = userRepo;
            this._mapper = _mapper;
            this._authService = _authService;
            this._userManager = _userManager;
            this._signinManager = _signinManager;

        }

    [HttpPost("login")]
    public async Task<IActionResult> Login(LoginDto userForLoginDto)
    {

        var user = await _userManager.FindByNameAsync(userForLoginDto.Username);

        if (user != null && !user.Deactivated)
        {
            var result = await _signinManager
            .CheckPasswordSignInAsync(user, userForLoginDto.Password, false);

            if (result.Succeeded)
            {
                var userToreturn = _mapper.Map<UserDetailDto>(user);
                return Ok(new
                {
                    token = _authService.GenerateTokenHandler(user, false),
                    user = userToreturn
                });
            }

        }

        return BadRequest();
    }

    [Authorize]
    [HttpPost("renewToken")]
    public async Task<IActionResult> RenewToken()
    {
        var loggedInId = Convert.ToInt32(User.FindFirst(ClaimTypes.NameIdentifier).Value);
        var user = await _userRepo.GetStaff(loggedInId);
       if(null != user) {
            var userToreturn = _mapper.Map<UserDetailDto>(user);
            return Ok(new
            {
                token = _authService.GenerateTokenHandler(user, false),
                user = userToreturn
            });
    }
        return BadRequest();
    }
}
}