using System.IO;
using System.Threading.Tasks;
using CloudinaryDotNet;
using CloudinaryDotNet.Actions;
using Microsoft.AspNetCore.Http;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.IService;
using Microsoft.Extensions.Options;
using QuickMessenger.API.Helpers;
using QuickMessenger.API.Data.Repo;
using System.Collections.Generic;
using QuickMessenger.API.Data.Model;

namespace QuickMessenger.API.Data.Services
{
    public class PhotoService : IPhotoService
    {
        private readonly Cloudinary _cloudinary;
        private readonly IQuickMessengerRepo _qmRepo;
        public PhotoService(IQuickMessengerRepo qmRepo, IOptions<CloudinarySettings> _cloudinaryConfig)
        {
            _qmRepo = qmRepo;
            Account acc = new Account(_cloudinaryConfig.Value.CloudName, _cloudinaryConfig.Value.ApiKey, _cloudinaryConfig.Value.ApiSecrete);
            _cloudinary = new Cloudinary(acc);
        }

        
        public  async Task<string> AddPhoto(IFormFile file, string path)
        {
           return await Task.Run(() => {
                var uploadResult = new ImageUploadResult();
                    if (file.Length > 0)
                    {
                        using (var stream = file.OpenReadStream())
                        {
                            var uploadParams = new ImageUploadParams()
                            {
                                File = new FileDescription(file.Name, stream),
                                // Transformation = new Transformation()
                                // .Width(300).Height(300).Crop("fill").Gravity("face"),
                                PublicId = path,
                                Overwrite = true
                                
                            };  
                            
                            uploadResult = _cloudinary.Upload(uploadParams);
                        }
                }
            return  uploadResult.Uri.ToString();

            });
            
        }

        public async Task<bool> DeletePhoto(string PublicId)
        {
                  return 
                   await Task.Run(()=> {
                    var deleteParams = new DeletionParams(PublicId);
                    var result = _cloudinary.Destroy(deleteParams);
                    if(result.Result == "ok") {
                    return true;
                     }
                     return false;
                    });
        }
    
        public int  GetNextIndex(int max, ICollection<Photo> existingPhotos){

            for(int i = 1; i<=max; i++){
                bool found  = false;
                foreach(Photo photo in existingPhotos){
                    if(null != photo.PublicId && photo.PublicId.EndsWith($"_{i}"))
                    {
                        found = true;
                        break;
                    }
                    if(!found)
                    return i;
                }
                
            }
            return 0;
        }
    }
}