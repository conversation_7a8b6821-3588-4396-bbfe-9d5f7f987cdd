﻿IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;

GO

CREATE TABLE [AspNetRoles] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(256) NULL,
    [NormalizedName] nvarchar(256) NULL,
    [ConcurrencyStamp] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetRoles] PRIMARY KEY ([Id])
);

GO

CREATE TABLE [Messages] (
    [Id] int NOT NULL IDENTITY,
    CONSTRAINT [PK_Messages] PRIMARY KEY ([Id])
);

GO

CREATE TABLE [OrderRatings] (
    [Id] int NOT NULL IDENTITY,
    CONSTRAINT [PK_OrderRatings] PRIMARY KEY ([Id])
);

GO

CREATE TABLE [OrderSchedules] (
    [Id] int NOT NULL IDENTITY,
    CONSTRAINT [PK_OrderSchedules] PRIMARY KEY ([Id])
);

GO

CREATE TABLE [Prod_Categories] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [NameId] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NULL,
    [ParentId] int NULL,
    [ImageUrl] nvarchar(max) NULL,
    [SearchParam] nvarchar(450) NULL,
    CONSTRAINT [PK_Prod_Categories] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Prod_Categories_Prod_Categories_ParentId] FOREIGN KEY ([ParentId]) REFERENCES [Prod_Categories] ([Id]) ON DELETE NO ACTION
);

GO

CREATE TABLE [Prod_MeasurementTypes] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [Symbol] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NULL,
    [SearchParam] nvarchar(max) NULL,
    CONSTRAINT [PK_Prod_MeasurementTypes] PRIMARY KEY ([Id])
);

GO

CREATE TABLE [Prod_PropertyTypes] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NULL,
    [SearchParam] nvarchar(max) NULL,
    CONSTRAINT [PK_Prod_PropertyTypes] PRIMARY KEY ([Id])
);

GO

CREATE TABLE [Reviews] (
    [Id] int NOT NULL IDENTITY,
    CONSTRAINT [PK_Reviews] PRIMARY KEY ([Id])
);

GO

CREATE TABLE [Services] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NULL,
    [Description] nvarchar(max) NULL,
    [NameId] nvarchar(max) NULL,
    [SearchParam] nvarchar(450) NULL,
    [Generic] bit NOT NULL,
    [PickupAllowed] bit NOT NULL,
    [PurchaseAllowed] bit NOT NULL,
    [IsDefaultGeneric] bit NOT NULL,
    CONSTRAINT [PK_Services] PRIMARY KEY ([Id])
);

GO

CREATE TABLE [Transactions] (
    [Id] int NOT NULL IDENTITY,
    CONSTRAINT [PK_Transactions] PRIMARY KEY ([Id])
);

GO

CREATE TABLE [UnresolvedSearches] (
    [Id] int NOT NULL IDENTITY,
    CONSTRAINT [PK_UnresolvedSearches] PRIMARY KEY ([Id])
);

GO

CREATE TABLE [Users] (
    [Id] int NOT NULL IDENTITY,
    [UserName] nvarchar(256) NULL,
    [NormalizedUserName] nvarchar(256) NULL,
    [Email] nvarchar(256) NULL,
    [NormalizedEmail] nvarchar(256) NULL,
    [EmailConfirmed] bit NOT NULL,
    [PasswordHash] nvarchar(max) NULL,
    [SecurityStamp] nvarchar(max) NULL,
    [ConcurrencyStamp] nvarchar(max) NULL,
    [PhoneNumber] nvarchar(max) NULL,
    [PhoneNumberConfirmed] bit NOT NULL,
    [TwoFactorEnabled] bit NOT NULL,
    [LockoutEnd] datetimeoffset NULL,
    [LockoutEnabled] bit NOT NULL,
    [AccessFailedCount] int NOT NULL,
    [LastName] nvarchar(max) NOT NULL,
    [FirstName] nvarchar(max) NOT NULL,
    [Gender] nvarchar(max) NULL,
    [DefaultAddressId] int NOT NULL,
    [SearchParam] nvarchar(450) NOT NULL,
    [PhotoUrl] nvarchar(max) NULL,
    [Deactivated] bit NOT NULL,
    [DateRegistered] datetime2 NOT NULL,
    [LastActive] datetime2 NOT NULL,
    [Discriminator] nvarchar(max) NOT NULL,
    CONSTRAINT [PK_Users] PRIMARY KEY ([Id])
);

GO

CREATE TABLE [Vendors] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NULL,
    [SearchParam] nvarchar(450) NULL,
    [AddressId] int NOT NULL,
    [UniqueParam] nvarchar(max) NULL,
    [Email] nvarchar(max) NOT NULL,
    [Phone] nvarchar(max) NOT NULL,
    [Phone2] nvarchar(max) NULL,
    [Deactivated] bit NOT NULL,
    CONSTRAINT [PK_Vendors] PRIMARY KEY ([Id])
);

GO

CREATE TABLE [AspNetRoleClaims] (
    [Id] int NOT NULL IDENTITY,
    [RoleId] int NOT NULL,
    [ClaimType] nvarchar(max) NULL,
    [ClaimValue] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetRoleClaims] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_AspNetRoleClaims_AspNetRoles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [AspNetRoles] ([Id]) ON DELETE CASCADE
);

GO

CREATE TABLE [Properties] (
    [Id] int NOT NULL IDENTITY,
    [Prod_CategoryId] int NOT NULL,
    [Prod_PropertyTypeId] int NOT NULL,
    [Prod_MeasurementTypeId] int NULL,
    CONSTRAINT [PK_Properties] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Properties_Prod_Categories_Prod_CategoryId] FOREIGN KEY ([Prod_CategoryId]) REFERENCES [Prod_Categories] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Properties_Prod_MeasurementTypes_Prod_MeasurementTypeId] FOREIGN KEY ([Prod_MeasurementTypeId]) REFERENCES [Prod_MeasurementTypes] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Properties_Prod_PropertyTypes_Prod_PropertyTypeId] FOREIGN KEY ([Prod_PropertyTypeId]) REFERENCES [Prod_PropertyTypes] ([Id]) ON DELETE NO ACTION
);

GO

CREATE TABLE [AspNetUserClaims] (
    [Id] int NOT NULL IDENTITY,
    [UserId] int NOT NULL,
    [ClaimType] nvarchar(max) NULL,
    [ClaimValue] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetUserClaims] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_AspNetUserClaims_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);

GO

CREATE TABLE [AspNetUserLogins] (
    [LoginProvider] nvarchar(450) NOT NULL,
    [ProviderKey] nvarchar(450) NOT NULL,
    [ProviderDisplayName] nvarchar(max) NULL,
    [UserId] int NOT NULL,
    CONSTRAINT [PK_AspNetUserLogins] PRIMARY KEY ([LoginProvider], [ProviderKey]),
    CONSTRAINT [FK_AspNetUserLogins_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);

GO

CREATE TABLE [AspNetUserRoles] (
    [UserId] int NOT NULL,
    [RoleId] int NOT NULL,
    [UserId1] int NULL,
    [RoleId1] int NULL,
    CONSTRAINT [PK_AspNetUserRoles] PRIMARY KEY ([UserId], [RoleId]),
    CONSTRAINT [FK_AspNetUserRoles_AspNetRoles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [AspNetRoles] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_AspNetUserRoles_AspNetRoles_RoleId1] FOREIGN KEY ([RoleId1]) REFERENCES [AspNetRoles] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_AspNetUserRoles_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_AspNetUserRoles_Users_UserId1] FOREIGN KEY ([UserId1]) REFERENCES [Users] ([Id]) ON DELETE NO ACTION
);

GO

CREATE TABLE [AspNetUserTokens] (
    [UserId] int NOT NULL,
    [LoginProvider] nvarchar(450) NOT NULL,
    [Name] nvarchar(450) NOT NULL,
    [Value] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetUserTokens] PRIMARY KEY ([UserId], [LoginProvider], [Name]),
    CONSTRAINT [FK_AspNetUserTokens_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);

GO

CREATE TABLE [Carts] (
    [Id] int NOT NULL IDENTITY,
    [TrackingId] nvarchar(450) NULL,
    [Date] datetime2 NOT NULL,
    [ClientId] int NOT NULL,
    [State] nvarchar(max) NULL,
    [Cost] float NOT NULL,
    CONSTRAINT [PK_Carts] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Carts_Users_ClientId] FOREIGN KEY ([ClientId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);

GO

CREATE TABLE [Addresses] (
    [Id] int NOT NULL IDENTITY,
    [FirstName] nvarchar(max) NULL,
    [LastName] nvarchar(max) NULL,
    [Phone] nvarchar(max) NULL,
    [Street] nvarchar(max) NULL,
    [Country] nvarchar(max) NULL,
    [State] nvarchar(max) NULL,
    [City] nvarchar(max) NULL,
    [UserId] int NULL,
    [VendorId] int NULL,
    CONSTRAINT [PK_Addresses] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Addresses_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Addresses_Vendors_VendorId] FOREIGN KEY ([VendorId]) REFERENCES [Vendors] ([Id]) ON DELETE NO ACTION
);

GO

CREATE TABLE [Products] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [Price] float NOT NULL,
    [Prod_CategoryId] int NOT NULL,
    [Description] nvarchar(max) NULL,
    [VendorId] int NULL,
    [Deactivated] bit NOT NULL,
    [SearchParam] nvarchar(450) NULL,
    [UniqueParam] nvarchar(450) NULL,
    [ServiceId] int NOT NULL,
    CONSTRAINT [PK_Products] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Products_Prod_Categories_Prod_CategoryId] FOREIGN KEY ([Prod_CategoryId]) REFERENCES [Prod_Categories] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Products_Services_ServiceId] FOREIGN KEY ([ServiceId]) REFERENCES [Services] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_Products_Vendors_VendorId] FOREIGN KEY ([VendorId]) REFERENCES [Vendors] ([Id]) ON DELETE NO ACTION
);

GO

CREATE TABLE [Orders] (
    [Id] int NOT NULL IDENTITY,
    [ClientId] int NOT NULL,
    [AddressId] int NULL,
    [Date] datetime2 NOT NULL,
    [PickupTime] datetime2 NOT NULL,
    [RiderId] int NULL,
    [Type] nvarchar(max) NULL,
    [State] nvarchar(max) NULL,
    [SearchParam] nvarchar(450) NULL,
    [Cost] float NOT NULL,
    [DeliveryCharge] float NOT NULL,
    [TimeDelivered] datetime2 NOT NULL,
    [CartId] int NOT NULL,
    [ServiceId] int NOT NULL,
    [TrackingId] nvarchar(450) NULL,
    CONSTRAINT [PK_Orders] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Orders_Addresses_AddressId] FOREIGN KEY ([AddressId]) REFERENCES [Addresses] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Orders_Carts_CartId] FOREIGN KEY ([CartId]) REFERENCES [Carts] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_Orders_Users_ClientId] FOREIGN KEY ([ClientId]) REFERENCES [Users] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Orders_Users_RiderId] FOREIGN KEY ([RiderId]) REFERENCES [Users] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Orders_Services_ServiceId] FOREIGN KEY ([ServiceId]) REFERENCES [Services] ([Id]) ON DELETE CASCADE
);

GO

CREATE TABLE [ProductProperties] (
    [ProductId] int NOT NULL,
    [PropertyId] int NOT NULL,
    [NValue] int NULL,
    [Value] nvarchar(max) NULL,
    CONSTRAINT [PK_ProductProperties] PRIMARY KEY ([ProductId], [PropertyId]),
    CONSTRAINT [FK_ProductProperties_Products_ProductId] FOREIGN KEY ([ProductId]) REFERENCES [Products] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_ProductProperties_Properties_PropertyId] FOREIGN KEY ([PropertyId]) REFERENCES [Properties] ([Id]) ON DELETE NO ACTION
);

GO

CREATE TABLE [PickupItems] (
    [Id] int NOT NULL IDENTITY,
    [AddressId] int NULL,
    [Name] nvarchar(max) NULL,
    [Description] nvarchar(max) NULL,
    [Size] nvarchar(max) NULL,
    [Fragile] bit NOT NULL,
    [OrderId] int NOT NULL,
    [Quantity] int NOT NULL,
    [Value] float NOT NULL,
    CONSTRAINT [PK_PickupItems] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_PickupItems_Addresses_AddressId] FOREIGN KEY ([AddressId]) REFERENCES [Addresses] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_PickupItems_Orders_OrderId] FOREIGN KEY ([OrderId]) REFERENCES [Orders] ([Id]) ON DELETE CASCADE
);

GO

CREATE TABLE [ProductOrders] (
    [ProductId] int NOT NULL,
    [OrderId] int NOT NULL,
    [Quantity] int NOT NULL,
    [ProductPrice] float NOT NULL,
    CONSTRAINT [PK_ProductOrders] PRIMARY KEY ([ProductId], [OrderId]),
    CONSTRAINT [FK_ProductOrders_Orders_OrderId] FOREIGN KEY ([OrderId]) REFERENCES [Orders] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_ProductOrders_Products_ProductId] FOREIGN KEY ([ProductId]) REFERENCES [Products] ([Id]) ON DELETE CASCADE
);

GO

CREATE TABLE [Photos] (
    [Id] int NOT NULL IDENTITY,
    [Url] nvarchar(max) NOT NULL,
    [PublicId] nvarchar(max) NULL,
    [DateAdded] datetime2 NOT NULL,
    [VendorId] int NULL,
    [ProductId] int NULL,
    [ServiceId] int NULL,
    [PickupItemId] int NULL,
    CONSTRAINT [PK_Photos] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Photos_PickupItems_PickupItemId] FOREIGN KEY ([PickupItemId]) REFERENCES [PickupItems] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_Photos_Products_ProductId] FOREIGN KEY ([ProductId]) REFERENCES [Products] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Photos_Services_ServiceId] FOREIGN KEY ([ServiceId]) REFERENCES [Services] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Photos_Vendors_VendorId] FOREIGN KEY ([VendorId]) REFERENCES [Vendors] ([Id]) ON DELETE NO ACTION
);

GO

CREATE INDEX [IX_Addresses_UserId] ON [Addresses] ([UserId]);

GO

CREATE UNIQUE INDEX [IX_Addresses_VendorId] ON [Addresses] ([VendorId]) WHERE [VendorId] IS NOT NULL;

GO

CREATE INDEX [IX_AspNetRoleClaims_RoleId] ON [AspNetRoleClaims] ([RoleId]);

GO

CREATE UNIQUE INDEX [RoleNameIndex] ON [AspNetRoles] ([NormalizedName]) WHERE [NormalizedName] IS NOT NULL;

GO

CREATE INDEX [IX_AspNetUserClaims_UserId] ON [AspNetUserClaims] ([UserId]);

GO

CREATE INDEX [IX_AspNetUserLogins_UserId] ON [AspNetUserLogins] ([UserId]);

GO

CREATE INDEX [IX_AspNetUserRoles_RoleId] ON [AspNetUserRoles] ([RoleId]);

GO

CREATE INDEX [IX_AspNetUserRoles_RoleId1] ON [AspNetUserRoles] ([RoleId1]);

GO

CREATE INDEX [IX_AspNetUserRoles_UserId1] ON [AspNetUserRoles] ([UserId1]);

GO

CREATE INDEX [IX_Carts_ClientId] ON [Carts] ([ClientId]);

GO

CREATE INDEX [IX_Carts_TrackingId] ON [Carts] ([TrackingId]);

GO

CREATE INDEX [IX_Orders_AddressId] ON [Orders] ([AddressId]);

GO

CREATE INDEX [IX_Orders_CartId] ON [Orders] ([CartId]);

GO

CREATE INDEX [IX_Orders_ClientId] ON [Orders] ([ClientId]);

GO

CREATE INDEX [IX_Orders_RiderId] ON [Orders] ([RiderId]);

GO

CREATE INDEX [IX_Orders_SearchParam] ON [Orders] ([SearchParam]);

GO

CREATE INDEX [IX_Orders_ServiceId] ON [Orders] ([ServiceId]);

GO

CREATE INDEX [IX_Orders_TrackingId] ON [Orders] ([TrackingId]);

GO

CREATE INDEX [IX_Photos_PickupItemId] ON [Photos] ([PickupItemId]);

GO

CREATE INDEX [IX_Photos_ProductId] ON [Photos] ([ProductId]);

GO

CREATE INDEX [IX_Photos_ServiceId] ON [Photos] ([ServiceId]);

GO

CREATE UNIQUE INDEX [IX_Photos_VendorId] ON [Photos] ([VendorId]) WHERE [VendorId] IS NOT NULL;

GO

CREATE INDEX [IX_PickupItems_AddressId] ON [PickupItems] ([AddressId]);

GO

CREATE INDEX [IX_PickupItems_OrderId] ON [PickupItems] ([OrderId]);

GO

CREATE INDEX [IX_Prod_Categories_ParentId] ON [Prod_Categories] ([ParentId]);

GO

CREATE INDEX [IX_Prod_Categories_SearchParam] ON [Prod_Categories] ([SearchParam]);

GO

CREATE INDEX [IX_ProductOrders_OrderId] ON [ProductOrders] ([OrderId]);

GO

CREATE INDEX [IX_ProductProperties_PropertyId] ON [ProductProperties] ([PropertyId]);

GO

CREATE INDEX [IX_Products_Prod_CategoryId] ON [Products] ([Prod_CategoryId]);

GO

CREATE INDEX [IX_Products_SearchParam] ON [Products] ([SearchParam]);

GO

CREATE INDEX [IX_Products_ServiceId] ON [Products] ([ServiceId]);

GO

CREATE INDEX [IX_Products_UniqueParam] ON [Products] ([UniqueParam]);

GO

CREATE INDEX [IX_Products_VendorId] ON [Products] ([VendorId]);

GO

CREATE INDEX [IX_Properties_Prod_MeasurementTypeId] ON [Properties] ([Prod_MeasurementTypeId]);

GO

CREATE INDEX [IX_Properties_Prod_PropertyTypeId] ON [Properties] ([Prod_PropertyTypeId]);

GO

CREATE UNIQUE INDEX [IX_Properties_Prod_CategoryId_Prod_PropertyTypeId_Prod_MeasurementTypeId] ON [Properties] ([Prod_CategoryId], [Prod_PropertyTypeId], [Prod_MeasurementTypeId]) WHERE [Prod_MeasurementTypeId] IS NOT NULL;

GO

CREATE INDEX [IX_Services_SearchParam] ON [Services] ([SearchParam]);

GO

CREATE INDEX [EmailIndex] ON [Users] ([NormalizedEmail]);

GO

CREATE UNIQUE INDEX [UserNameIndex] ON [Users] ([NormalizedUserName]) WHERE [NormalizedUserName] IS NOT NULL;

GO

CREATE INDEX [IX_Users_SearchParam] ON [Users] ([SearchParam]);

GO

CREATE INDEX [IX_Vendors_SearchParam] ON [Vendors] ([SearchParam]);

GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20201026150540_Initial', N'2.2.4-servicing-10062');

GO

