using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using QuickMessenger.API.Data.DTO.Shared;

namespace QuickMessenger.API.Data.DTO.Client
{
    public class ClientDetailDto
    {
        public int Id { get; set; }
        [Required]
        [EmailAddress]
        public string Email { get; set; }
        [Required]
        public string FirstName { get; set; }
        [Required]
        public string LastName { get; set; }
        [Required]
        [Phone]
        public string PhoneNumber { get; set; }
        public string Gender { get; set; }
        [Required]
        public string Role { get; set; }
        public bool Deactivated { get; set; }
        public bool RemovePhoto{get;set;}
        public string Password { get; set; }
        public string PhotoUrl { get; set; }
        public ICollection<AddressDto> Addresses { get; set; }
        
    }

    public class ClientDetailViewDto
    {
        [Required]
        public int Id { get; set; }
        [Required]
        [EmailAddress]
        public string Email { get; set; }
        [Required]
        public string FirstName { get; set; }
        [Required]
        public string LastName { get; set; }
        [Required]
        [Phone]
        public string PhoneNumber { get; set; }
        public string Gender { get; set; }
        public string PhotoUrl { get; set; }
        public string FCMToken { get; set; }
        
    }

    class RiderDetailViewDto: ClientDetailViewDto{
        public string AccountName { get; set; }
        public string AccountNumber { get; set; }
        public string BankName { get; set; }
    }

    public class FCmTokenDto
    {
        public string token { get; set; }
    }

}