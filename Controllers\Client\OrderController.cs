using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Security.Claims;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using QuickMessenger.API.Data.DTO.Client;
using Microsoft.AspNetCore.Http;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Data.IService;
using QuickMessenger.API.Data.IRepo;
using System.Net.Http;
using QuickMessenger.API.Data.DTO.ThirdParty;
using System.Linq;
using Microsoft.Extensions.Configuration;
using QuickMessenger.API.Data.DTO.Vendor;
using System.Text.Json;
using System.Text;

namespace QuickMessenger.API.Controllers.Client
{
    [ApiController]
    [Authorize(Policy = "QuickWaka")]
    [Route("api/client/[controller]")]
    public class OrderController : ControllerBase
    {
        private static string _photoPath = "qm/pickup/parcel";
        private readonly IGeoService _geoService;
        private readonly IMapper _mapper;
        private readonly IOrderRepo _orderRepo;
        private readonly IUserRepo _userepo;
        private readonly IProductRepo _productRepo;
        private readonly IOrderService _orderService;
        private readonly IPhotoService _photoService;
        private readonly IConfiguration _config;
        private readonly IVendorRepo _vendorRepo;
        private readonly    IUserService _userService;
        public OrderController(IGeoService geoService, IMapper mapper, IPhotoService photoService, IOrderRepo orderRepo, IUserRepo _userepo, IOrderService orderService, 
        IProductRepo productRepo, IConfiguration config, IUserService userService, IVendorRepo vendorRepo)
        {
            this._photoService = photoService;
            this._orderService = orderService;
            this._productRepo = productRepo;
            this._userepo = _userepo;
            this._orderRepo = orderRepo;
            this._mapper = mapper;
            this._geoService = geoService;
            this._config = config;
            this._userService = userService;
            this._vendorRepo = vendorRepo;
        }

        [HttpPost("serviceCharge")]
        public async Task<IActionResult> GetServiceCharge(List<OrderAddress> AddressList)
        {
            double total = 0;
            foreach (OrderAddress orderAdd in AddressList)
            {
                List<Address> addresses = new List<Address>();
                var destinationAdd = await _userepo.GetAddress(orderAdd.AddressId);
                addresses.Add(destinationAdd);
                if (orderAdd.PickupAddressId > 0)
                {
                    var pickup = await _userepo.GetAddress(orderAdd.PickupAddressId);
                    addresses.Add(pickup);
                }

                if (null != orderAdd.ProductIds && orderAdd.ProductIds.Count > 0)
                {
                    var productList = await _productRepo.GetProductListWithVendorAndAddress(orderAdd.ProductIds);
                    foreach (Product product in productList)
                    {
                        if (null != product.Vendor)
                            addresses.Add(product.Vendor.Address);
                        else
                        {
                            // do something if the product doesn't have a vendor 
                            //do something 
                        }
                    }

                }

                double orderCharge = await _geoService.GetErrandPricing(addresses);
                total += orderCharge;
            }

            return Ok(total);
        }

        [HttpPost("createCart")]
        public async Task<IActionResult> CreateCart(CartForCreateDto CartTobeCreated)
        {

            if (null == CartTobeCreated.Orders || CartTobeCreated.Orders.Count < 0)
                return BadRequest();

            var Cart = _mapper.Map<Cart>(CartTobeCreated);
            Cart.Date = DateTime.Now;
            var lastCart = await _orderRepo.GetLastCart();
            //Generate tracking id
            if (null == lastCart)
            {
                Cart.TrackingId = $"qw-{0001}-{Cart.ClientId}";
            }
            else
            {
                var temp = lastCart.TrackingId.Split("-")[1];
                int serial = Int32.Parse(temp);
                serial++;
                string nserial = "";
                if (serial < 1000)
                {
                    nserial = string.Format("{0:0000}", serial);
                }
                Cart.TrackingId = $"qw-{nserial}-{Cart.ClientId}";
            }

            
            var orderCount = 0;
            foreach (Order order in Cart.Orders)
            {
                //check if any order doesn't contain both pickups and products
                if ((null == order.PickupItems || order.PickupItems.Count == 0)
               && (null == order.ProductOrders || order.ProductOrders.Count == 0))
                    return BadRequest("One of the orders contain neither pickup items nor products");

                orderCount++;
                //var rider = await _userepo.GetRider(order.Id);
                //order.Rider = rider;
                order.Date = DateTime.Now;
                var address = await _userepo.GetAddress(order.AddressId ?? 0);
                order.TrackingId = $"{Cart.TrackingId}-{orderCount}";
                var client = await _userepo.GetClient(order.ClientId);
                var service = await _productRepo.GetService(order.ServiceId);
                order.SearchParam = $"{client.Email} {client.FirstName} {client.LastName} {client.PhoneNumber} {order.TrackingId} {service.Name}";
                if(null != address)
                {
                    
                    order.SearchParam += $" {address.City} {address.Street} {address.State} {address.LastName} {address.FirstName} {address.Phone}";
                   // Address add = _mapper.Map<Address>(address);
                   // order.AddressId = 0;
                   // order.Address = add;
                }

                 if(null != order.PickupItems)
                    foreach(PickupItem pu in order.PickupItems){  
                        order.SearchParam = order.SearchParam + $" {pu.Name}";
                    }

                    //od.PickupItems = null;
                    if(null != order.ProductOrders)
                    foreach(ProductOrder pu in order.ProductOrders){
                        var product = await _productRepo.GetProduct(pu.ProductId);
                        order.SearchParam = order.SearchParam + $" {product.Name}";
                        if(null != product.Vendor)
                        order.SearchParam = order.SearchParam + $" {product.Vendor.Name}";
                        pu.ProductPrice = product.Price * pu.Quantity;
                    }

                order.SearchParam = order.SearchParam.ToUpper();
                
                
            }
            Cart.State = "Pending";
            await _orderService.GetTotalCartCost(Cart);
            _orderRepo.Add(Cart);
            if (await _orderRepo.SaveAll())
            {
                var cartToReturn = _mapper.Map<CartForViewDto>(Cart);
                return Ok(cartToReturn);
            }
            return BadRequest("Cannot create Cart");
        }

        [HttpPost("checkout/{CartId}")]
        public async Task<IActionResult> Checkout(int CartId, VerifyPaymentDto verify)
        {
            var path = "https://api.flutterwave.com/v3/transactions/" + verify.PaymentReference + "/verify";
            var client = new HttpClient();
            client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _config.GetSection("Flutter:Secrete_Key").Value);
            var uri = new Uri(path);
            var response = await client.GetAsync(uri);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var flutter = Newtonsoft.Json.JsonConvert.DeserializeObject<FlutterWave>(content);
                 var Cart = await _orderRepo.GetCart(CartId);

            
            if(await _orderService.Checkout(flutter, CartId))
                                 return Ok(
                                        new { TrackingId = Cart.TrackingId
                                });
            }

            return BadRequest(new { status = false });

        }

        [AllowAnonymous]
        [HttpGet("checkout/callback")]
        public async Task<IActionResult> VerifyCartTransaction([FromQuery] String Status, [FromQuery] String Tx_ref, String Transaction_id )
        {
            var path = "https://api.flutterwave.com/v3/transactions/" + Transaction_id + "/verify";
            var client = new HttpClient();
            client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _config.GetSection("Flutter:Secrete_Key").Value);
           
            var uri = new Uri(path); 
            var response = await client.GetAsync(uri);
            Console.WriteLine("callback  called");
            if (!String.IsNullOrEmpty(Status) && Status.CompareTo("successful") == 0 && response.IsSuccessStatusCode)
            {
                // Console.WriteLine("WEbhook success");
                var content = await response.Content.ReadAsStringAsync();
                var flutter = Newtonsoft.Json.JsonConvert.DeserializeObject<FlutterWave>(content);
                 var Cart = await _orderRepo.GetCartByTransactionReference(Tx_ref);

            if(Cart.State.CompareTo("Paid") == 0 || await _orderService.Checkout(flutter, Cart.Id))
                                 return Redirect("https://quickwaka.com/order/txcn_successful");
            }
            return Redirect("https://quickwaka.com/order/txcn_failed");

        }

        [AllowAnonymous]
        [HttpPost("checkout/webhook")]
        public async Task<IActionResult> Webhook( FlutterWave2 flutter){
            Console.WriteLine("WEbhook called");
            if(flutter.Event.CompareTo("charge.completed") == 0
             && flutter.data.status.CompareTo("successful") == 0){
                 var Cart = await _orderRepo.GetCartByTransactionReference(flutter.data.tx_ref);
                 if(Cart.State.CompareTo("Paid") == 0)
                 return Ok();
                 Console.WriteLine("WEbhook success");

            var path = "https://api.flutterwave.com/v3/transactions/" + flutter.data.id + "/verify";
            var client = new HttpClient();
            client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _config.GetSection("Flutter:Secrete_Key").Value);
            var uri = new Uri(path);
            var response = await client.GetAsync(uri);
            if(!response.IsSuccessStatusCode)
            {   
                return Ok();
            }
                 if(Cart.Cost == flutter.data.amount && Cart.State.CompareTo("Pending") == 0)
                 {
                     if(await _orderService.Checkout(flutter, Cart.Id))
                                 return Ok( );
                 }
             }

                 return BadRequest();
            
        }

        //generate payment link for cart
        [HttpPost("checkout/paymentLink/{CartId}")]
        public async Task<IActionResult>GetPaymentLink(int CartId){

            var Cart = await _orderRepo.GetCart(CartId);
            DateTime now = DateTime.Now;
            var milTime = now.Millisecond;
            Cart.Tx_ref = $"{Cart.TrackingId}-{milTime}";
            if(await _orderRepo.SaveAll()){
                     var apiUrl  = "https://api.flutterwave.com/v3/payments";
             using (var httpClient = new HttpClient())
             {
            // Create an object to be serialized as JSON
            var data = new
            {
                tx_ref = Cart.Tx_ref,
                amount = Cart.Cost,
                
                currency = "NGN",
                redirect_url = "https://quickwaka.com/api/api/client/order/checkout/callback",
                customer =  new  {
                email =  Cart.Client.Email,
                phonenumber = Cart.Client.PhoneNumber,
                name = $"{Cart.Client.FirstName} {Cart.Client.LastName}"
            },
            };

            // Serialize the object to JSON
            var jsonData = JsonSerializer.Serialize(data);

            // Create the HTTP request with JSON body
            var request = new HttpRequestMessage(HttpMethod.Post, apiUrl);
            request.Content = new StringContent(jsonData, Encoding.UTF8, "application/json");
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _config.GetSection("Flutter:Secrete_Key").Value);
            // Send the HTTP request and receive the response
            var response = await httpClient.SendAsync(request);

            // Check if the response is successful
            if (response.IsSuccessStatusCode)
            {
                var responseJson = await response.Content.ReadAsStringAsync();
                 var flutter = Newtonsoft.Json.JsonConvert.DeserializeObject<PaymentLinkResponse>(responseJson);
                return Ok(flutter);
                
            }
            else
            {
                return BadRequest();
            }
            }
        
        }
            return BadRequest();
        }
       
        [HttpPut("update/{CartId}")]
          public async Task<IActionResult> UpdateCart(int CartId, CartForCreateDto cartTobeEdited)
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var TempCart = await _orderRepo.GetCartWithoutOrders(CartId);
            if (userId != TempCart.ClientId)
            {
                return BadRequest("Unauthorised request");
            }
            bool ordersAdded = false;
            bool ordersRemoved = false;
            bool ordersModified = false;
            bool recalculateCharge = false;
            //var Cart  = await _orderRepo.GetCartWithoutOrders(CartId);
            // _mapper.Map(cartTobeEdited, Cart );

            // NOTE: No change can be made to a cart object only the orders belonging to that cart

            // CREATE
            //check if there are new Orders in the cart
            var newOrdersToBeAddedDto = cartTobeEdited.Orders.Where(o => o.Id == 0).ToList();
            var newOrdersToBeAdded = _mapper.Map<List<Order>>(newOrdersToBeAddedDto);
            if (newOrdersToBeAdded.Count > 0) {
                // get highest tracking id
                int highestOrderTrackingIdComponent = 0;
                var dbOrderTrackingIds = await _orderRepo.GetOrderTrackingIdsForCart(CartId);
                foreach (String orderTrackingId in dbOrderTrackingIds) {
                    var arr = orderTrackingId.Split("-");
                    if (Convert.ToInt32(arr[3]) > highestOrderTrackingIdComponent)
                        highestOrderTrackingIdComponent = Convert.ToInt32(arr[3]);
                }


                foreach (Order o in newOrdersToBeAdded) {
                    highestOrderTrackingIdComponent++;
                    o.CartId = TempCart.Id;

                    // set tracking id
                    o.TrackingId = $"{TempCart.TrackingId}-{highestOrderTrackingIdComponent}";

                    // set search params
                    var address = await _userepo.GetAddress(o.AddressId ?? 0);
                    var client = await _userepo.GetClient(userId);
                    var service = await _productRepo.GetService(o.ServiceId);
                    o.SearchParam = $"{client.Email} {client.FirstName} {client.LastName} {client.PhoneNumber} {o.TrackingId} {service.Name}";
                    o.ClientId = client.Id;
                    if(null != address)
                    {
                        o.SearchParam += $" {address.City} {address.Street} {address.State} {address.LastName} {address.FirstName} {address.Phone}";
                    }
                    if (null != o.PickupItems) {
                        foreach(PickupItem pu in o.PickupItems){  
                            o.SearchParam = o.SearchParam + $" {pu.Name}";
                        }
                    }
                    if (null != o.ProductOrders) {
                        foreach(ProductOrder pu in o.ProductOrders){
                            var product = await _productRepo.GetProduct(pu.ProductId);
                            o.SearchParam = o.SearchParam + $" {product.Name}";
                            if(null != product.Vendor)
                            o.SearchParam = o.SearchParam + $" {product.Vendor.Name}";
                            
                        }
                    }
                    o.SearchParam = o.SearchParam.ToUpper();
                }
                _orderRepo.AddAll(newOrdersToBeAdded);
            }
            if (await _orderRepo.SaveAll())
            {
                ordersAdded = true;  
            }
             //_orderRepo.DetachEntities(newOrdersToBeAdded);

            // DELETE
            //check if orders were deleted
            var dbOrderIds = await _orderRepo.GetOrderIdsForCart(CartId);
            var ordersToBeEditedIds = cartTobeEdited.Orders.Select(o => o.Id).ToList();
            if (newOrdersToBeAdded.Count > 0)
                ordersToBeEditedIds.AddRange(newOrdersToBeAdded.Select(o => o.Id).ToList());
            foreach (int id in dbOrderIds)
            {
                if (!ordersToBeEditedIds.Contains(id))
                {
                    var Order = await _orderRepo.GetNoLazyOrder(id);
                    var productOrders = await _orderRepo.GetProductOrdersByOrder(id);
                    var pickupItems = await _orderRepo.GetPickupsByOrder(id);

                    _orderRepo.DeleteAll(productOrders);
                    _orderRepo.DeleteAll(pickupItems);
                    _orderRepo.Delete(Order);
                }
            }
            // try {
            if (await _orderRepo.SaveAll())
            {
                ordersRemoved = true;
            }
            // } catch(Exception ex) {Console.WriteLine(ex.InnerException);}

            // EDIT
            //Attempt to modify orders 
            foreach (ServiceOrdersForCreateDto orderDto in cartTobeEdited.Orders)
            {
                string searchParam = "";

                //for each order check if pickup items have been deleted or added
                var order = await _orderRepo.GetNoLazyOrder(orderDto.Id);
                //_orderRepo.DetachAllEntities();
                //do this only if that order is an existing order
                if (null != order)
                {
                    var pickupIdsFromDb = await _orderRepo.GetPickupsByOrderIds(order.Id);
                    var productOrders = await _orderRepo.GetProductOrdersByOrder(order.Id);
                   
                    if(null != orderDto.PickupItems)
                    {
                        // PICKUPS
                        //check if pickups where added
                        var pickupsDtoToBeAdded = orderDto.PickupItems.Where(p => p.Id == 0).ToList();
                        var pickupsToBeAdded = _mapper.Map<List<PickupItem>>(pickupsDtoToBeAdded);
                        if (pickupsToBeAdded.Count > 0) {
                            foreach (PickupItem picupDto in pickupsToBeAdded)
                            {
                                picupDto.OrderId = order.Id;
                                searchParam += $"{picupDto.Name} ";
                            }
                            _orderRepo.AddAll(pickupsToBeAdded);
                        }

                        if (await _orderRepo.SaveAll())
                            ordersModified = true;
                           // _orderRepo.DetachAllEntities();

                        //Check if pickups were updated
                        var pickupsDtoToBeUpdate = orderDto.PickupItems.Where(p => p.Id != 0).ToList();
                        foreach (PickupItemDto2 picupDto in pickupsDtoToBeUpdate)
                        {
                            picupDto.Photos = null;
                            var pick = await _orderRepo.GetPickup(picupDto.Id);
                            searchParam += $"{pick.Name} ";
                            _mapper.Map(picupDto, pick);
                        }
                        if (await _orderRepo.SaveAll())
                            ordersModified = true;
                    }      

                    // check if pickups were deleted
                    List<int> pickupIdsFromDto = new List<int>();
                    if(null != orderDto.PickupItems)
                        pickupIdsFromDto = orderDto.PickupItems.Select(o => o.Id).ToList();
                    foreach (int id in pickupIdsFromDb)
                    {
                        if (!pickupIdsFromDto.Contains(id))
                        {
                            var pickup = await _orderRepo.GetPickup(id);
                            _orderRepo.Delete(pickup);
                        }
                    }
                    if (await _orderRepo.SaveAll())
                        {
                            ordersModified = true;     
                      //  _orderRepo.DetachAllEntities();
                        }

                    
                    // PURCHASES
                    var productOrderIdsFromDb = productOrders.Select(p => p.ProductId).ToList();
                    //check if productOrders were deleted
                    List<int> productOrdersIdsDto = new List<int>();
                    if(orderDto.ProductOrders != null)
                        productOrdersIdsDto = orderDto.ProductOrders.Select(p => p.ProductId).ToList();
                    foreach (int pId in productOrderIdsFromDb)
                    {
                        if (!productOrdersIdsDto.Contains(pId))
                        {
                            var productOrder = await _orderRepo.GetProductOrder(pId, orderDto.Id);
                            _orderRepo.Delete(productOrder);
                        }
                    }
                    if (await _orderRepo.SaveAll())
                    {
                         ordersModified = true;
                    //_orderRepo.DetachAllEntities();
                    }
                    
                    //chceck if productorders were updated
                    if(null != orderDto.ProductOrders)
                    {
                        var productOrderDtoTobeUpdate = orderDto.ProductOrders
                                                    .Where(po => productOrderIdsFromDb.Contains(po.ProductId)).ToList();
                        foreach (ProductOrderDto2 pdto in productOrderDtoTobeUpdate)
                        {
                            var productOrder = await _orderRepo.GetProductOrder(pdto.ProductId, orderDto.Id);
                            _mapper.Map(pdto, productOrder);
                            var product = await _productRepo.GetProduct(productOrder.ProductId);
                            searchParam += $"{product.Name} ";
                            if(null != product.Vendor)
                            searchParam += $"{product.Vendor.Name} ";
                             if (await _orderRepo.SaveAll())
                            {
                                ordersModified = true;
                                // _orderRepo.DetachEntity(productOrder);
                                // _orderRepo.DetachEntity(product);
                            }
                           
                        }
                       
                        //check if orderProducts were added
                        var productOrderDtoTobeAdded = orderDto.ProductOrders
                            .Where(po => !productOrderIdsFromDb.Contains(po.ProductId)).ToList();
                        var productOrderTobeAdded = _mapper.Map<List<ProductOrder>>(productOrderDtoTobeAdded);
                        if (productOrderTobeAdded.Count > 0)
                            _orderRepo.AddAll(productOrderTobeAdded);
                        if (await _orderRepo.SaveAll())
                        {
                            ordersModified = true;
                        }
                        //  _orderRepo.DetachEntities(productOrderTobeAdded);
                    }
                    
                   
                     var client = await _userepo.GetClientOnlyDetail(orderDto.ClientId);
                      var address = await _userepo.GetAddress(orderDto.AddressId);

                    var service = await _productRepo.GetNoLazyService(orderDto.ServiceId);
                    var ordertoUpdate = await _orderRepo.GetNoLazyOrder(orderDto.Id);
                    
                    var orderDtoLazy =  _mapper.Map<ServiceOrdersForCreateDtoLazy>(orderDto);
                    _mapper.Map(orderDtoLazy, ordertoUpdate);
                   
                    ordertoUpdate.SearchParam = $"{client.Email} {client.FirstName} {client.LastName} {client.PhoneNumber} {order.TrackingId} {service.Name}";
                    if(null != address)
                    {
                        ordertoUpdate.SearchParam += $" {address.City} {address.Street} {address.State} {address.LastName} {address.FirstName} {address.Phone}";
                    }

                    ordertoUpdate.SearchParam = $"{ordertoUpdate.SearchParam} {searchParam}".ToUpper();
        
                    // _orderRepo.DetachEntity(client);
                    // _orderRepo.DetachEntity(address);
                    // _orderRepo.DetachEntity(ordertoUpdate);

                }       
                    if (await _orderRepo.SaveAll())
                                {
                                    ordersModified = true;
                            //  _orderRepo.DetachAllEntities();
                                }
                    }

            if (ordersAdded || ordersRemoved || ordersModified)
            {
                var updatedCart = await _orderRepo.GetCartWithOrdersint(CartId);
               
                
                await _orderService.GetTotalCartCost(updatedCart);
                 
                if (await _productRepo.SaveAll())
                {
                     _orderRepo.ReloadEntity(updatedCart);

                     var CartotRetun = _mapper.Map<CartForViewDto>(updatedCart);
                       return Ok(CartotRetun);
                }
                  
            }

            return BadRequest("No changes were made to the cart");

        }

        [HttpGet("cart/{CartId}")]
        public async Task<IActionResult> GetCart(int CartId)
        {
            var Cart = await _orderRepo.GetCartWithOrdersint(CartId);
            var CartotRetun = _mapper.Map<CartForViewDto>(Cart);

            return Ok(CartotRetun);
        }

        [HttpDelete("cart/{CartId}")]
        public async Task<IActionResult> DeleteCart(int CartId)
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var Cart = await _orderRepo.GetCartWithOrdersint(CartId);
            if (Cart != null) {
                if (userId != Cart.ClientId)
                {
                    return BadRequest("Unauthorised request");
                }
                // var Cart = await _orderRepo.GetCartWithoutOrders(CartId);
                var photos = await _orderRepo.GetAllPhotosBelongingToCart(CartId);
                foreach(Photo pho in photos){
                    _photoService.DeletePhoto(pho.Url);
                }
                _orderRepo.DeleteAll(photos);
                foreach (var order in Cart.Orders)
                {
                    _orderRepo.DeleteAll(order.ProductOrders);
                }
                _orderRepo.Delete(Cart);
                if (await _orderRepo.SaveAll())
                {
                    return Ok();
                }
            }

            return BadRequest();

        }

        [HttpGet("{Id}")]
        public async Task<IActionResult> GetOrder(int Id)
        {
            var order = await _orderRepo.GetOrder(Id);
            if (null == order)
                return BadRequest();
              
            var orderToReturn = _mapper.Map<ServiceOrdersForViewDto2>(order);
            var otrackingSplit = order.TrackingId.Split("-");
            orderToReturn.CartTrackingId = otrackingSplit[0]+ "-" + otrackingSplit[1]+ "-" + otrackingSplit[2];
            
            return Ok(orderToReturn);

        }

        [HttpPost("pickupItem/addPhoto/{Id}")]
        public async Task<IActionResult> AddPickupPhoto([FromForm] IFormFile file, int Id)
        {
            if (await _orderService.AddPickupPhoto(file, Id, _photoPath, 3))
                return NoContent();
            return BadRequest(new { Error = "Could not upload Product photo" });
        }

        [HttpPost("cart/all")]
        public async Task<IActionResult> GetAllCarts(){
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var carts = await _orderRepo.GetCartsByUser(userId);
            var objToReturn = _mapper.Map<List<CartForViewDto>>(carts);
            return Ok(objToReturn);
            
        }

        [HttpPost("cart/notpending")]
        public async Task<IActionResult> GetAllNonPendingCarts(){
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var carts = await _orderRepo.GetNonPendingCartsByUser(userId);
            var objToReturn = _mapper.Map<List<CartForViewDto2>>(carts);
            return Ok(objToReturn);    
        }

        [AllowAnonymous]
        [HttpPost("track/{trackingId}")]
        public async Task<IActionResult> GetOrderOrCart(string trackingId){
            var Cart = await _orderRepo.GetCartByTrackingId(trackingId);
            Order order = new Order();
            if(null == Cart)
            {
                order  =  await _orderRepo.GetOrderByTrackingId(trackingId);
                if(null != order)
                {
                    var orderToReturn = _mapper.Map<ServiceOrdersForViewDto2>(order);
                    return Ok(orderToReturn);
                }
                return BadRequest();
            }

            var objToReturn = _mapper.Map<CartForViewDto2>(Cart);
            return Ok(objToReturn);

        }

        [HttpGet("pendingCart")]
        public async Task<IActionResult> GetPendingCart(){
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var Cart = await _orderRepo.GetCurrentPendingCart(userId);
            var objToReturn = _mapper.Map<CartForViewDto2>(Cart);
            return Ok(objToReturn);

        }

        [HttpGet]
        public async Task<IActionResult> GetAllOrders(){
             var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
             var orders = await _orderRepo.GetOrdersByClient(userId);
             var objToReturn = _mapper.Map<List<OrderListAppViewDto>>(orders);
             return Ok(objToReturn);
        }
   
        [HttpPut("update/ready")]
        public async Task <IActionResult> ModifyOrderstateReady(UpdateOrderItemDTO productOrder ){
                 var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
                var user = await _userepo.GetClient(userId);
                
                 var productOrders = await _orderRepo.GetProductOrders(productOrder.ProductIds, productOrder.OrderId);
                 foreach(ProductOrder po in productOrders)
                 {
                    if(po.Product.VendorId != user.VendorId)
                     return BadRequest(new { Error = "You are not authorised to update this order" });

                    po.State = "Ready";
                 }

                 if(await _orderRepo.SaveAll())
                    return NoContent();

                    return BadRequest();

        }

        [HttpPut("update/pending")]
        public async Task <IActionResult> ModifyOrderstatePending(UpdateOrderItemDTO productOrder ){
                 var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
                var user = await _userepo.GetClient(userId);
               var productOrders = await _orderRepo.GetProductOrders(productOrder.ProductIds, productOrder.OrderId);
                 foreach(ProductOrder po in productOrders)
                 {
                    if(po.Product.VendorId != user.VendorId)
                     return BadRequest(new { Error = "You are not authorised to update this order" });

                    po.State = "Pending";
                 }

                 if(await _orderRepo.SaveAll())
                    return NoContent();
                    return BadRequest();

             }

        }
}