using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.IService;

namespace QuickMessenger.API.Controllers.Staff
{
    [ApiController]
    [Authorize(Policy = "ProdMgt")]
    [Route("api/qm_475/staff/[controller]")]
    public class DashBoardController : ControllerBase
    {
        private readonly IOrderService _orderService;
        private readonly IOrderRepo _orderRepo;
        public DashBoardController(IOrderService orderService, IOrderRepo orderRepo)
        {
            _orderRepo = orderRepo;
            this._orderService = orderService;

        }

        // [HttpGet("order/today")]
        // public async Task<IActionResult> GetTodaysOrderReport()
        // {
        //     return Ok(await _orderService.GetOrderKpiReportWithDate(new DateTime()));
        // }

        [HttpGet("order/card/today")]
        public async Task<IActionResult> GetDailyOrderReport()
        {
            var date = DateTime.Now;
            return Ok(await _orderService.GetOrderKpiReportWithDate(date));
        }

        [HttpGet("order/card/thisweek")]
        public async Task<IActionResult> GetThisweeksOrderReport()
        {
            var today = DateTime.Now;
            var startDate = today.AddDays(-(int)today.DayOfWeek);

            return Ok(await _orderService.GetOrderKpiReportWithDateRange(startDate, today));
        }

        [HttpGet("order/card/thismonth")]
        public async Task<IActionResult> GetThisMonthsOrderReport()
        {
            var today = DateTime.Now;
            int month = today.Month;
            int year = today.Year;
            return Ok(await _orderService.GetOrderKpiReportWithMonth(year, month));
        }

        [HttpGet("order/list/city/today")]
        public async Task<IActionResult> GetOrderListByCityToday()
        {
            return Ok(await _orderRepo.GetOrdersGroupedByCities(DateTime.Now));
        }

        [HttpGet("order/list/city/thisweek")]
        public async Task<IActionResult> GetOrderListByCityThisWeek()
        {
             var today = DateTime.Now;
            var startDate = today.AddDays(-(int)today.DayOfWeek);
            return Ok(await _orderRepo.GetOrdersGroupedByCities(startDate, today));
        }

        [HttpGet("order/list/city/thismonth")]
        public async Task<IActionResult> GetOrderListByCityThisMonth()
        {
            var today = DateTime.Now;
            int month = today.Month;
            int year = today.Year;
            return Ok(await _orderRepo.GetOrdersGroupedByCities(year, month));
        }

        [HttpGet("order/list/vendor/today")]
        public async Task<IActionResult> GetOrderListByVendorToday()
        {
            return Ok(await _orderRepo.GetOrdersGroupedByVendor(DateTime.Now));
        }

        [HttpGet("order/list/vendor/thisweek")]
        public async Task<IActionResult> GetOrderListByVendorThisWeek()
        {
            var today = DateTime.Now;
            var startDate = today.AddDays(-(int)today.DayOfWeek);
            return Ok(await _orderRepo.GetOrdersGroupedByVendor(startDate, today));
        }

        [HttpGet("order/list/vendor/thismonth")]
        public async Task<IActionResult> GetOrderListByVendorThisMonth()
        {
            var today = DateTime.Now;
            int month = today.Month;
            int year = today.Year;
            return Ok(await _orderRepo.GetOrdersGroupedByVendor(year, month));
        }

        [HttpGet("order/list/product/today")]
        public async Task<IActionResult> GetOrderListByProductToday()
        {
            var date = DateTime.Now;
            return Ok(await _orderRepo.GetOrdersGroupedByProduct(date));
        }

        [HttpGet("order/list/product/thisweek")]
        public async Task<IActionResult> GetOrderListByProductThisWeek()
        {
            var today = DateTime.Now;
            var startDate = today.AddDays(-(int)today.DayOfWeek);
            
            return Ok(await _orderRepo.GetOrdersGroupedByProduct(startDate, today));
        }

        [HttpGet("order/list/product/thismonth")]
        public async Task<IActionResult> GetOrderListByProductThisMonth()
        {
            var today = DateTime.Now;;
            int month = today.Month;
            int year = today.Year;
            return Ok(await _orderRepo.GetOrdersGroupedByProduct(year, month));
        }

        [HttpGet("order/list/rider/today")]
        public async Task<IActionResult> GetOrderListByRiderToday()
        {
            return Ok(await _orderRepo.GetOrdersGroupedByRider(DateTime.Now));
        }

        [HttpGet("order/list/rider/thisweek")]
        public async Task<IActionResult> GetOrderListByRiderThisWeek()
        {
            var today = DateTime.Now;
            var startDate = today.AddDays(-(int)today.DayOfWeek);
            
            return Ok(await _orderRepo.GetOrdersGroupedByRider(startDate, today));
        }

        [HttpGet("order/list/rider/thismonth")]
        public async Task<IActionResult> GetOrderListByRiderThisMonth()
        {
            var today = DateTime.Now;;
            int month = today.Month;
            int year = today.Year;
            return Ok(await _orderRepo.GetOrdersGroupedByRider(year, month));
        }

        [HttpGet("order/chart/today")]
        public async Task<IActionResult> GetOrderChartToday()
        {
            var today =  DateTime.Now;
            
            return Ok(await _orderService.GetHourlyOrderKpiReportWithDate(today));
        }

        [HttpGet("order/chart/thisweek")]
        public async Task<IActionResult> GetOrderChartThisWeek()
        {
            var today =  DateTime.Now;
            var startDate = today.AddDays(-(int)today.DayOfWeek);
            
            return Ok(await _orderService.GetChartOrderReportWithDateRange(startDate, today));
        }

        [HttpGet("order/chart/thismonth")]
        public async Task<IActionResult> GetOrderChartThisMonth()
        {
            var today =  DateTime.Now;
            var startDate = today.AddDays(-(int)today.Day-1);
            
            return Ok(await _orderService.GetChartOrderReportWithDateRange(startDate, today));
        }
        
        //TODO:15. List of Riders with most Distance Covered

    }
}