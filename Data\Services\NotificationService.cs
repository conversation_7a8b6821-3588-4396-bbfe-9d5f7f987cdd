using System;
using System.Net.Http;
using System.Reflection.Metadata;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using FirebaseAdmin.Messaging;
using QuickMessenger.API.Data.DTO.FireBase;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.IService;
using QuickMessenger.API.Data.Model;

namespace QuickMessenger.API.Data.Services
{
    public enum NotificationTopic
    {
        CLIENTORDER,
        VENDORORDER

    }
    public class NotificationService : INotificationService
    {
          private readonly IUserRepo _userRepo;
          
        public NotificationService(IUserRepo userRepo)
        {
           this._userRepo = userRepo;
        }

        public async  Task SendOrderNotification(string Title, string Body, Order order, NotificationTopic topic)
        {
             
            var user = await _userRepo.GetUser(order.ClientId);
            FireBaseData data = new FireBaseData{
                Title = Title,
                Body = Body,
                Topic = topic switch
                {
                    NotificationTopic.CLIENTORDER => "ClientOrder",
                    NotificationTopic.VENDORORDER => "VendorOrder",
                    _ => ""
                },
                CartId = topic switch
                {
                    NotificationTopic.CLIENTORDER => order.CartId,
                    NotificationTopic.VENDORORDER => null,
                    _ => null
                },
            };

            if(!String.IsNullOrEmpty(user.FCMToken))
            {
                data.Token = user.FCMToken;
            }
        
            send(data);
          
         }

         public async  Task SendOrderNotification(string Title, string Body, Cart cart, NotificationTopic topic)
        {
             
            var user = await _userRepo.GetUser(cart.ClientId);
            FireBaseData data = new FireBaseData{
                Title = Title,
                Body = Body,
                Topic = topic switch
                {
                    NotificationTopic.CLIENTORDER => "ClientOrder",
                    NotificationTopic.VENDORORDER => "VendorOrder",
                    _ => ""
                },
                CartId = topic switch
                {
                    NotificationTopic.CLIENTORDER => cart.Id,
                    NotificationTopic.VENDORORDER => null,
                    _ => null
                },
            };

            if(!String.IsNullOrEmpty(user.FCMToken))
            {
                data.Token = user.FCMToken;
            }
        
            send(data);
          
         }

         private async void send(FireBaseData data){
           var path = "https://us-central1-quickwaka-af911.cloudfunctions.net/sendNotification";
           var client = new HttpClient();
           var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
           var jsonData = JsonSerializer.Serialize(data, options);
            var content = new StringContent(jsonData, Encoding.UTF8, "application/json");
             try
            {
            HttpResponseMessage response = await client.PostAsync(path, content);

            if (response.IsSuccessStatusCode)
            {
                string responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine("Response: " + responseContent);
            }
            else
            {
                Console.WriteLine("Error: " + response.ReasonPhrase);
            }
        }
        catch (Exception e)
        {
            Console.WriteLine("Exception: " + e.Message);
        }
     }
    }
 }