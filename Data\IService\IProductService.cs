using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using QuickMessenger.API.Data.DTO.Product;
using QuickMessenger.API.Data.DTO.Shared;
using QuickMessenger.API.Data.Model;

namespace QuickMessenger.API.Data.IService
{
    public interface IProductService
    {
         Task<bool> UpdateCategoryProperties(ICollection<Prod_PropertyDto> Prod_properties, Prod_Category Category);
         Task DeleteCategoryPhoto(Prod_Category Category);
         Task<bool> AddCategoryPhoto(IFormFile file, Prod_Category Category);
         Task<bool> AddServicePhoto(IFormFile file, Service Service, int maxPhotos);
         Task<bool> AddProductPhoto(IFormFile file, int Id, string path, int maxPhotos);
         Task<bool> CreateCategoryProperties(ICollection<Prod_PropertyDto> Prod_properties, Prod_Category Category); 
         Task<bool> DeleteProductPhotos(ICollection<PhotoDto> photos, string _photoPath);   
         Task<bool> UpdateProductProperties(ICollection<ProductPropertyDto> properties, int Id);  
         Task<bool> CreateProductProperties(ICollection<ProductPropertyDto> properties, int Id);  
    }

}