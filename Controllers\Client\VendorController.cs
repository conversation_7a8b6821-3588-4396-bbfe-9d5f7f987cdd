using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using QuickMessenger.API.Data.DTO.Client;
using QuickMessenger.API.Data.DTO.Product;
using QuickMessenger.API.Data.DTO.Vendor;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Helpers.Params;
using SQLitePCL;

namespace QuickMessenger.API.Controllers.Client
{
    [ApiController]
    [AllowAnonymous]
    [Route("api/client/[controller]")]
    public class VendorController: ControllerBase
    {
         private readonly IVendorRepo _vendorRepo;
        private readonly IProductRepo _productRepo;
        private readonly IMapper _mapper;
        private readonly IOrderRepo _orderRepo;
        private readonly IUserRepo _userRepo;
        public VendorController(IVendorRepo vendorRepo, IProductRepo productRepo, IMapper mapper, IOrderRepo iOrderRepo, IUserRepo userRepo)
        {
            this._mapper = mapper;
            this._productRepo = productRepo;
            this._vendorRepo = vendorRepo;
            this._orderRepo = iOrderRepo;
            this._userRepo = userRepo;
        }
        
        [HttpPost]
        public async Task<IActionResult> GetVendors([FromQuery] ClientVendorParams param){
             var vendors = await _vendorRepo.GetVendors(param);
              var objToreturn = new
                    {
                        Vendors = _mapper.Map<IEnumerable<VendorListDtoLite>>(vendors),
                        Cities  = await _vendorRepo.GetDistinctCitiesForParams(param),
                        Pagination = new
                        {
                            CurrentPage = vendors.CurrentPage,
                            PageSize = vendors.PageSize,
                            TotalCount = vendors.TotalCount,
                            TotalPages = vendors.TotalPages
                        }
                    };

            return Ok(objToreturn); 
        }

        [HttpGet("{Id}")]
        public async Task<IActionResult> GetVendor(int Id)
        {
            var vendorFromRepo = await _vendorRepo.GetVendor(Id);
            var vendorToReturn = _mapper.Map<VendorDtoLite>(vendorFromRepo);

            return Ok(vendorToReturn);
        }

        [HttpPost("products")]
        public async Task<IActionResult> getAllProductsByVendor([FromQuery]VendorProductParams Param)
        {
            var products = await _productRepo.GetProducts(Param);
               
                 var objToreturn = new
                    {
                        Products = _mapper.Map<IEnumerable<ProductListForClientDto>>(products.Item1),
                        Pagination = new
                        {
                            CurrentPage = products.Item1.CurrentPage,
                            PageSize = products.Item1.PageSize,
                            TotalCount = products.Item1.TotalCount,
                            TotalPages = products.Item1.TotalPages
                        },
                        ChildrenCategories = _mapper.Map<List<Prod_CategoryListDtoLite2>>(products.Item2),
                        MinPrice = products.Item4,
                        MaxPrice = products.Item5,
                        Category = _mapper.Map<Prod_CategoryListDtoLite2>(products.Item3)
                    };

            return Ok(objToreturn);
        }

        [HttpPost("top_prod_categories/{Id}")]
        public async Task<IActionResult> getAllTopProductCategories(int Id)
        {
            var categories = await _productRepo.GetCategoriesByVendor(Id);
               
                 var objToreturn = new
                    {
                        categories = _mapper.Map<ICollection<Prod_CategoryListDtoLite2>>(categories)
                    };

            return Ok(objToreturn);
        }

        [HttpPost("minmaxprice/{Id}")]
        public async Task<IActionResult> GetMinMaxPriceForAllProductsByVendor(int Id){

            var result = await _productRepo.GetMinMaxPriceForAllProductsByVendor(Id);
            return Ok(new {
                MinPrice = result.Item1,
                MaxPrice = result.Item2,
            });
        }
        
        [Authorize(Policy = "QuickWaka")]
        [HttpGet("pendingorders")]
        public async Task<IActionResult> GetVendorPendingOrders(){
                var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
                var user = await _userRepo.GetClient(userId);
                if(null == user.VendorId)
                return BadRequest(new { Error = "No Vendor attached to this user" });
                var orders = await _orderRepo.GetUnpreparedOrdersByVendorGroupedByClient(user.VendorId ?? 0);
                return Ok(orders);

        }

        [Authorize(Policy = "QuickWaka")]
        [HttpGet("pendingorders/today")]
        public async Task<IActionResult> GetVendorTodayOrders(){
                var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
                var user = await _userRepo.GetClient(userId);
                if(null == user.VendorId)
                return BadRequest(new { Error = "No Vendor attached to this user" });
                var orders = await _orderRepo.GetTodayPendingOrdersByVendorGroupedByClient(user.VendorId ?? 0);
                 return Ok(orders);
        }

        [Authorize(Policy = "QuickWaka")]
        [HttpGet("pendingorders/daterange")]
        public async Task<IActionResult> GetVendorDateRangeOrders([FromQuery] DateRangeParam dateRange){

            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var user = await _userRepo.GetClient(userId);
                if(null == user.VendorId)
                return BadRequest(new { Error = "No Vendor attached to this user" });
            // if(null != dateRange.Start && null != dateRange.End)
            {
                var orders = await _orderRepo.GetOrdersByVendorAndDateRangeGroupedByClient(userId, dateRange.Start ?? DateTime.Now, dateRange.End ?? DateTime.Now);
                 return Ok(orders);   
            }
        }

        [Authorize(Policy = "QuickWaka")]
        [HttpPut("{Id}")]
        public async Task<IActionResult> UpdateVendor(int Id, VendorUpdateForClientDto vendor){

            var vendorFromDb = await _vendorRepo.GetVendorWithoutRelation(Id);
            if(vendorFromDb.Id != vendor.Id)
            return BadRequest(new { Error = "Invalid vendor Id" });
             var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
             var user = await _userRepo.GetClient(userId);
             if(user.VendorId != Id)
              return Unauthorized(new { Error = "USer doesn't belong to this vendor!" });

            vendorFromDb.State = vendor.State;
            if(await _vendorRepo.SaveAll())
            return Ok();

            return BadRequest();
        } 
    }

}