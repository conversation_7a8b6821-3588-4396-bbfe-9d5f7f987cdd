
using System;
using System.Net;
using System.Text;
using AutoMapper;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using QuickMessenger.API.Data;
using QuickMessenger.API.Data.IService;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Data.Services;
using QuickMessenger.API.Helpers;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.Repo;
using QuickMessenger.API.Data.Sample;
using Microsoft.AspNetCore.Identity.UI.Services;
using static QuickMessenger.API.Helpers.Extensions;
using QuickMessenger.API.Providers;
using Google.Apis.Auth.OAuth2;
using FirebaseAdmin;
using Microsoft.OpenApi.Models;

namespace QuickMessenger.API
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddMvc(opt => {
                var policy = new AuthorizationPolicyBuilder().RequireAuthenticatedUser().Build();
                opt.Filters.Add(new AuthorizeFilter(policy));

                 //  opt.EnableEndpointRouting = true;
            }).SetCompatibilityVersion(CompatibilityVersion.Version_3_0);
            
            services.AddControllers().AddNewtonsoftJson(opt => {
                opt.SerializerSettings.ReferenceLoopHandling = 
                Newtonsoft.Json.ReferenceLoopHandling.Ignore;
                opt.SerializerSettings.Converters.Add(new TrimmingConverter());
            });
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(
            Options =>
            {
                Options.TokenValidationParameters   =  new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(
                       Encoding.ASCII.GetBytes(Configuration.GetSection("AppSettings:Token").Value)),
                       ValidateIssuer = false,
                       ValidateAudience = false
                    
                };
            })
            
            // .AddGoogle(googleOptions => 
            // {
            //     googleOptions.ClientId = "1045169575998-nmju0mp6trsk87ptc8lk74757gql1vsk.apps.googleusercontent.com";
            //     googleOptions.ClientSecret = "-TikW7FRjGq7Yp49XibdCyhR";
            // })
            ;

            services.AddAuthorization(opt => {
                opt.AddPolicy("StaffMgt", Policy => Policy.RequireRole("Admin", "SuperAdmin"));
                opt.AddPolicy("SuperStaffMgt", Policy => Policy.RequireRole("SuperAdmin"));
                opt.AddPolicy("ProdMgt", Policy => Policy.RequireRole("Admin", "SuperAdmin","FrontDesk"));
                opt.AddPolicy("OrderMgt", Policy => Policy.RequireRole("Admin", "SuperAdmin","FrontDesk", "Rider"));
                opt.AddPolicy("QuickWaka", Policy => Policy.RequireRole("Admin", "SuperAdmin","FrontDesk","Client","Rider"));
                });
           
            services.AddMvcCore(options =>
                {
                    options.Filters.Add(typeof(ValidateModelFilter));
                });
                
                services.AddSwaggerGen(options =>
                    {
                        options.SwaggerDoc("v1", new OpenApiInfo
                        {
                            Version = "v1",
                            Title = "Your API Title",
                            Description = "Your API Description"
                        });

                    });
                
            IdentityBuilder builder = services.AddIdentityCore<User>(opt => {
                    opt.Password.RequireDigit = false;
                    opt.Password.RequiredLength = 6;
                    opt.Password.RequireUppercase = false;
                    opt.Password.RequireNonAlphanumeric = false;
                    opt.User.RequireUniqueEmail = true;
                    opt.SignIn.RequireConfirmedEmail = true;
                    opt.Tokens.EmailConfirmationTokenProvider = "emailconfirmation";
                })
                .AddDefaultTokenProviders()
                .AddTokenProvider<EmailConfirmationTokenProvider<User>>("emailconfirmation");
                services.Configure<EmailConfirmationTokenProviderOptions>(opt =>
                opt.TokenLifespan = TimeSpan.FromDays(3));
                

                services.Configure<DataProtectionTokenProviderOptions>(opt =>
                opt.TokenLifespan = TimeSpan.FromHours(2));

            builder = new IdentityBuilder(builder.UserType, typeof(Role), builder.Services);
            builder.AddEntityFrameworkStores<DataContext>();
            builder.AddRoleValidator<RoleValidator<Role>>();
            builder.AddSignInManager<SignInManager<User>>();
            services.Configure<CloudinarySettings>(Configuration.GetSection("CloudinarySettings"));
           //services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());
            services.AddAutoMapper(typeof(Startup));
            services.AddDbContext<DataContext>(x => x.UseSqlServer(
            Configuration.GetConnectionString("DefaultConnection"))
            );
            InjectDependencies(services);
            services.AddCors(); 
            
            
            
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        [Obsolete]
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, Seed Seeder)
        {
            if (env.EnvironmentName == EnvironmentName.Development)
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseExceptionHandler(builder => {
                    builder.Run(async context => {
                        context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;

                        var error = context.Features.Get<IExceptionHandlerFeature>(); 
                        if(error != null)
                        {
                            context.Response.AddApplicationError(error.Error.Message);
                            await context.Response.WriteAsync(error.Error.Message);
                        } 
                    });
                });
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                //app.UseHsts();
            }

            Seeder.SeedUsers().Wait();
             app.UseDefaultFiles();
              app.UseSwagger();

            // Enable middleware to serve Swagger-ui (HTML, JS, CSS, etc.),
            // specifying the Swagger JSON endpoint.
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/api/swagger/v1/swagger.json", "My API V1");
            //    c.RoutePrefix = "swagger";// To serve the Swagger UI at the app's root
               
            });
            
            app.UseRouting();
            app.UseCors(x => x.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader());
            app.UseAuthentication();
            app.UseAuthorization();   
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });

            

        // Initialize Firebase Admin SDK with the configuration
                var credential = GoogleCredential.FromFile("google-services.json");
                FirebaseApp.Create(new AppOptions
                {
                    Credential = credential
                });

        }
        
        private void InjectDependencies(IServiceCollection services)
        {
            services.AddTransient<IEmailSender, EmailSender>();
            services.Configure<AuthMessageSenderOptions>(Configuration);
            services.AddScoped<IQuickMessengerRepo, QuickMessengerRepo>();

            services.AddScoped<IAuthService, AuthService>();
            services.AddScoped<IOrderService, OrderService>();
            services.AddScoped<IProductService, ProductService>();
            services.AddScoped<IProductRepo, ProductRepo>();
            services.AddScoped<IPhotoService, PhotoService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IUserRepo, UserRepo>();
            services.AddScoped<IVendorRepo, VendorRepo>();
            services.AddScoped<IPhotoRepo, PhotoRepo>();
            services.AddScoped<IOrderRepo, OrderRepo>();
            services.AddScoped<IGeoService, GeoService>();
            services.AddScoped<INotificationService, NotificationService>();
            services.AddTransient<Seed>();

        
        }
    }
}
