using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using QuickMessenger.API.Data.DTO.Staff;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.IService;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Data.Services;
using QuickMessenger.API.Helpers.Params;

namespace QuickMessenger.API.Controllers.Staff
{
    [ApiController]
    [Authorize(Policy = "StaffMgt")]
    [Route("api/qm_475/staff/[controller]")]
    public class StaffController : ControllerBase
    {
        private static string _photoPath = "qm/staff/staff";
        private readonly IUserRepo _userRepo;
        private readonly IMapper _mapper;
        private readonly IUserService _userService;
        private readonly IPhotoService _photoService;
        private readonly UserManager<User> _userManager;
        private readonly SignInManager<User> _signinManager;
        public StaffController(IUserRepo userRepo, IMapper mapper,
        IUserService userService, UserManager<User> userManager,
        IPhotoService photoService, SignInManager<User> signinManager )
        {
            _signinManager = signinManager;
            _userManager = userManager;
            _userService = userService;
            _mapper = mapper;
            _userRepo = userRepo;
            _photoService = photoService;
        }

    [HttpGet]
    public async Task<IActionResult> GetStaff([FromQuery]UserParam UserParam)
    {
        int UserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
        var staff = await _userRepo.GetStaffLazyAddress(UserParam, UserId);
        var userToReturn = _mapper.Map<IEnumerable<StaffDetailDto>>(staff);
        var objToreturn = new
        {
            Staff = userToReturn,
            Pagination = new
            {
                CurrentPage = staff.CurrentPage,
                PageSize = staff.PageSize,
                TotalCount = staff.TotalCount,
                TotalPages = staff.TotalPages
            }
        };
        return Ok(objToreturn);
    }

    [HttpGet("staff/{id}")]
    public async Task<IActionResult> GetStaffById(int Id)
    {
        var Staff = await _userRepo.LazyGetStaff(Id);
        var staffToReturn = _mapper.Map<StaffDetailDto>(Staff);
        return Ok(staffToReturn);
    }
    [HttpGet("riders/lite")]
    public async Task<IActionResult> GetRiders()
    {
        var riders = await _userRepo.GetAllActiveRiders();
        var RidersToReturn = _mapper.Map<ICollection<RiderListLiteDto>>(riders);
        return Ok(RidersToReturn);
    }

    [Authorize(Policy = "ProdMgt")]
    [HttpPut("update")]
    public async Task<IActionResult> UpdateStaff(StaffDetailDto staff)
    {
        var existingUSer = await _userRepo
                        .GetDiffUserWithEmailOrPhone(staff.Email, staff.Phone, staff.Id);
        if (existingUSer != null)
        {
            var errors = new List<object>();

            if (staff.Email.Equals(existingUSer.Email))
                errors.Add(new { Email = "Email address already exists" });

            if (staff.Phone.Equals(existingUSer.PhoneNumber))
                errors.Add(new { Phone = "Phone number already exists" });

            return BadRequest(new { Errors = errors });
        }


        string adminRole = User.FindFirst(ClaimTypes.Role).Value;
        int UserId = Convert.ToInt32(User.FindFirst(ClaimTypes.NameIdentifier).Value);
        var staffToUpdate = await _userRepo.GetStaff(staff.Id);
        if (staffToUpdate == null)
            return BadRequest();
        //Ensure that admin cannot edit another Admin
        if (!adminRole.Equals("SuperAdmin") && UserId != staffToUpdate.Id)
            return Unauthorized();
        if (await _userService.UpdateStaff(staff, staffToUpdate, _photoPath))
        {
            return NoContent();
        }

        return BadRequest();

    }

    [HttpPost("create")]
    public async Task<IActionResult> CreateStaff([FromBody]StaffDetailDto staffToBeCreated)
    {
        //Check if user exists in the repo
        var existingUSer = await _userRepo
                    .GetUserByEmailOrPhone(staffToBeCreated.Email, staffToBeCreated.Phone);
        if (existingUSer != null)
            return BadRequest(new {Errors = new {Error = "A staff has been created with this email or phone number"}});

        var result = await _userService.CreateStaff(staffToBeCreated);

        if (result.Succeeded)
        {
            _userService.SendStaffCreationEmail(staffToBeCreated);
            return Ok(new { Id = staffToBeCreated.Id });
        }

        return BadRequest(new { Errors = result.Errors });
    }

    [Authorize(Policy = "ProdMgt")]
    [HttpPut("{Id}/updatePassword")]
    public async Task<IActionResult> UpdateStaffPassword(int Id, StaffUpdatePassWord staff)
    {
        var loggedInId = Convert.ToUInt32(User.FindFirst(ClaimTypes.NameIdentifier).Value);
        if (staff.Id != Id || loggedInId != Id)
            return Unauthorized();
        var user = await _userManager.FindByNameAsync(staff.UserName);
        if (staff.NewPassword.CompareTo(staff.CurrentPassword) == 0) {
            return BadRequest(new {errors = new { NewPassword = new String[] {"Your new password should not be the same as your old password"}}});
        }
            
        var result = await _userManager.ChangePasswordAsync(user, staff.CurrentPassword, staff.NewPassword);

        if (result.Succeeded)
            return NoContent();

        return BadRequest(result.Errors);
    }
    [HttpDelete("{Id}/delete")]
    public async Task<IActionResult> DeleteStaff(int Id)
    {
        string adminRole = User.FindFirst(ClaimTypes.Role).Value;
        var staffToDelete = await _userRepo.GetStaff(Id);
        var address = await _userRepo.GetStaffAddress(Id);
        if (staffToDelete == null)
            return BadRequest();
        //Ensure that admin cannot edit another Admin
        if ((!adminRole.Equals("SuperAdmin")
        && staffToDelete.GetType().Name.Equals("Admin")))
            return Unauthorized();

        staffToDelete.Deactivated = true;
        if (!await _userRepo.UserHasRecords(Id))
        {
            _photoService.DeletePhoto($"{_photoPath}_{Id}");
            if(address != null)
            _userRepo.Delete(address);
            _userRepo.Delete(staffToDelete);
        }

        if (await _userRepo.SaveAll())
            return Ok();

        return BadRequest();
    }

    [HttpPost("{Id}/addPhoto")]
    public async Task<IActionResult> AddStaffPhoto([FromForm] IFormFile file, int Id)
    {

        string adminRole = User.FindFirst(ClaimTypes.Role).Value;
        var staffToUpdate = await _userRepo.GetStaff(Id);
        if (staffToUpdate == null)
            return BadRequest();
        //Ensure that admin cannot edit another Admin
        if (!adminRole.Equals("SuperAdmin") && staffToUpdate.Equals("Admin"))
            return Unauthorized();

        return await AddPhoto(file, staffToUpdate);

    }

    [HttpDelete("{staffId}/deletePhoto")]
    public async Task<IActionResult> DeleteStaffPhoto(int staffId)
    {
        string adminRole = User.FindFirst(ClaimTypes.Role).Value;
        var staffToUpdate = await _userRepo.GetStaff(staffId);
        //Ensure that admin cannot edit another Admin
        if (!adminRole.Equals("SuperAdmin") && staffToUpdate.Equals("Admin"))
            return Unauthorized();

        if (await _photoService.DeletePhoto($"{_photoPath}_{staffId}"))
        {
            var Staff = await _userRepo.GetStaff(staffId);
            Staff.PhotoUrl = "";
            if (await _userRepo.SaveAll())
                return Ok();
        }
        return BadRequest();
    }


    private async Task<IActionResult> AddPhoto(IFormFile file, User staff)
    {
        string url = await _photoService.AddPhoto(file, $"{_photoPath}_{staff.Id}");

        if (!url.Equals(staff.PhotoUrl))
        {
            staff.PhotoUrl = url;
            if (!await _userRepo.SaveAll())
                return BadRequest();
        }

        return Ok(new { url = url });

    }

}
}