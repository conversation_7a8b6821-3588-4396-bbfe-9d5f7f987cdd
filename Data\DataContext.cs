using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using QuickMessenger.API.Data.Model;

namespace QuickMessenger.API.Data
{
    public class DataContext:  IdentityDbContext<User, Role, int, 
    IdentityUserClaim<int>, UserRole, IdentityUserLogin<int>, IdentityRoleClaim<int>, 
    IdentityUserToken<int>>
    {
        public DataContext(DbContextOptions<DataContext> options): base(options)
        {
            
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            modelBuilder.Entity<User>().ToTable("Users");
            // modelBuilder.Entity<Admin >().ToTable("Admins");
            // modelBuilder.Entity<Client >().ToTable("Clients");
            // modelBuilder.Entity<FrontDesk>().ToTable("FrontDeskOperators");
            // modelBuilder.Entity<SuperAdmin>().ToTable("SuperAdmins");
            // modelBuilder.Entity<Rider>().ToTable("Riders");
            modelBuilder.Entity<Address>(Add=> {
                Add.HasOne(a => a.User).WithMany(u => u.Addresses)
                .HasForeignKey(K => K.UserId).IsRequired(false);

                Add.HasMany(a => a.Orders ).WithOne(o => o.Address)
                .HasForeignKey(a => a.AddressId);

                Add.HasMany(a => a.PickupItems ).WithOne(o => o.Address)
                .HasForeignKey(a => a.AddressId);
            });
            modelBuilder.Entity<User>(Add=> {
                Add.HasMany(a => a.Addresses).WithOne(u => u.User)
                .HasForeignKey(K => K.UserId);//.OnDelete(DeleteBehavior.Restrict);

                Add.HasIndex(i => i.SearchParam);
            });
            
            modelBuilder.Entity<Prod_MeasurementType>(prod_measure => 
            {
                prod_measure.HasMany(pm => pm.Properties)
                .WithOne(p => p.Prod_MeasurementType)
                .HasForeignKey(p => p.Prod_MeasurementTypeId).OnDelete(DeleteBehavior.Restrict);
            });
            modelBuilder.Entity<Prod_PropertyType>(prod_prop => 
            {
                prod_prop.HasMany(pp => pp.Properties)
                .WithOne(p => p.Prod_PropertyType).OnDelete(DeleteBehavior.Restrict);
            });       
            modelBuilder.Entity<Prod_Category>(prod_cat=> 
            {
                prod_cat.HasIndex( c => c.SearchParam).IsUnique(false);
                
                prod_cat.HasMany(pc => pc.Properties)
                .WithOne(p => p.Prod_Category)
                .HasForeignKey( p => p.Prod_CategoryId)
                .OnDelete(DeleteBehavior.Restrict);

                prod_cat.HasMany(pc => pc.SubCategories)
                .WithOne(p => p.Parent).HasForeignKey(p => p.ParentId).IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);

                prod_cat.HasOne(pc => pc.Parent)
                .WithMany(pc => pc.SubCategories)
                .HasForeignKey(pc => pc.ParentId).IsRequired(false);

                prod_cat.HasMany(p => p.Products)
                .WithOne(p => p.Prod_Category).HasForeignKey(p => p.Prod_CategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            });
            modelBuilder.Entity<Property>(prod_cat=> 
            {   
                prod_cat.HasMany(p => p.ProductProperties).WithOne(p => p.Property)
                .HasForeignKey(p => p.PropertyId).OnDelete(DeleteBehavior.Restrict);

                prod_cat.HasOne(p => p.Prod_MeasurementType)
                .WithMany( p=> p.Properties).HasForeignKey(p => p.Prod_MeasurementTypeId);

                 prod_cat.HasOne(p => p.Prod_PropertyType)
                .WithMany( p=> p.Properties).HasForeignKey(p => p.Prod_PropertyTypeId);

                prod_cat
                .HasIndex(pc => 
                new{pc.Prod_CategoryId, pc.Prod_PropertyTypeId, pc.Prod_MeasurementTypeId})
                .IsUnique(true);
                
            });
            modelBuilder.Entity<Product>(prod=> 
            {   
                prod.HasMany(pc => pc.ProductProperties)
                .WithOne(p => p.Product).OnDelete(DeleteBehavior.Cascade);               
                prod.HasIndex(p => p.SearchParam).IsUnique(false);
                
                prod.HasMany(p => p.Photos).WithOne( p => p.Product)
                .HasForeignKey(p => p.ProductId);

                prod.HasIndex(p => p.UniqueParam).IsUnique(false);

            });
            modelBuilder.Entity<Vendor>(vendor => {
                vendor.HasMany(v => v.Products).WithOne(v => v.Vendor)
                .HasForeignKey(v=>v.VendorId).OnDelete(DeleteBehavior.Restrict);
                vendor.HasOne(a => a.Address).WithOne(a => a.Vendor)
                .HasForeignKey<Address>(a => a.VendorId);
                vendor.HasOne(a => a.Logo).WithOne(a => a.Vendor)
                .HasForeignKey<Photo>(a => a.VendorId);
                vendor.HasIndex(v => v.SearchParam).IsUnique(false);

            });
            modelBuilder.Entity<ProductOrder>(POrder => {
                POrder.HasKey(po => new  {po.ProductId, po.OrderId});

                POrder.HasOne( po => po.Product).WithMany(p => p.ProductOrders)
                .HasForeignKey(po => po.ProductId);

                POrder.HasOne( po => po.Order).WithMany(p => p.ProductOrders)
             .HasForeignKey(po => po.OrderId);
            });

            modelBuilder.Entity<ProductProperty>( prod_prop => 
            {
                prod_prop.HasKey(pp => new {pp.ProductId, pp.PropertyId});

                prod_prop.HasOne(pp => pp.Product)
                .WithMany(PP => PP.ProductProperties)
                .HasForeignKey(PP => PP.ProductId);

                prod_prop.HasOne(pp => pp.Property)
                .WithMany(PP => PP.ProductProperties)
                .HasForeignKey(PP => PP.PropertyId);
            }
            );

            modelBuilder.Entity<Order>(order => {
                order.HasIndex(o => o.SearchParam).IsUnique(false);
                order.HasIndex(o => o.TrackingId).IsUnique(false);
                order.HasMany(o => o.PickupItems).WithOne(P => P.Order)
                .HasForeignKey(o => o.OrderId);
                order.HasOne(o => o.Client).WithMany(c => c.Orders)
                .HasForeignKey(o => o.ClientId).OnDelete(DeleteBehavior.Restrict);
                order.HasMany(o => o.ProductOrders).WithOne(o => o.Order).HasForeignKey(p => p.OrderId).OnDelete(DeleteBehavior.Restrict);
                
            });

            modelBuilder.Entity<Cart>(cart => {
                cart.HasOne(c => c.Client).WithMany(u => u.Carts)
                .HasForeignKey(c => c.ClientId);
                cart.HasIndex(o => o.TrackingId).IsUnique(false);
            });

            modelBuilder.Entity<PickupItem>(pickup => {
                pickup.HasOne( o => o.Order)
                    .WithMany(p => p.PickupItems).HasForeignKey(o => o.OrderId);
            });

            modelBuilder.Entity<Photo>(photo => {
                photo.HasOne(p => p.PickupItem)
                    .WithMany(o => o.Photos)
                    .OnDelete(DeleteBehavior.Cascade);
                photo.HasOne(p => p.Product)
                    .WithMany(o => o.Photos)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            modelBuilder.Entity<Service>(service =>{
                service.HasMany(p => p.Products).WithOne(s => s.Service).HasForeignKey(k => k.ServiceId);
                service.HasIndex(s => s.SearchParam).IsUnique(false);
                service.HasMany(s => s.Orders).WithOne(s => s.Service).HasForeignKey(f => f.ServiceId).IsRequired(true);
            });

            
        }
        
        public DbSet<Address> Addresses { get; set; }
        public DbSet<Admin> Admins { get; set; }
        public DbSet<Cart> Carts { get; set; }
        public DbSet<Client> Clients { get; set; }
        public DbSet<FrontDesk> FrontDeskOperators { get; set; }
        public DbSet<Message> Messages { get; set; }
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderRating> OrderRatings { get; set; }
        public DbSet<OrderSchedule> OrderSchedules { get; set; }
        public DbSet<PickupItem> PickupItems { get; set; }
        public DbSet<Review> Reviews { get; set; }
        public DbSet<Rider> Riders { get; set; }
        public DbSet<SuperAdmin> SuperAdmins { get; set; }
        public DbSet<Transaction> Transactions { get; set; }
        public DbSet<UnresolvedSearch> UnresolvedSearches { get; set; }
        public DbSet<Vendor> Vendors { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<Property> Properties { get; set; }
        public DbSet<Photo> Photos { get; set; }
        public DbSet<Prod_Category> Prod_Categories { get; set; }
        public DbSet<Prod_PropertyType> Prod_PropertyTypes { get; set; }
        public DbSet<Prod_MeasurementType> Prod_MeasurementTypes { get; set; }
        public DbSet<ProductOrder> ProductOrders { get; set; }
        public DbSet<ProductProperty> ProductProperties { get; set; }
        public DbSet<Service> Services { get; set; }
        public DbSet<App_BannerLink>AdLinks {get; set;}
        
    }
}