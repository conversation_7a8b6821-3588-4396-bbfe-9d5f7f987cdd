using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace QuickMessenger.API.Data.Model
{
    public class Product
    {
        
        public int Id { get; set; }
        [Required]
        public string Name { get; set; }
        [Required]
        public double Price { get; set; }
        public Prod_Category Prod_Category { get; set; }
        [Required]
        public int Prod_CategoryId { get; set; }
        public string Description { get; set; }
        public ICollection<Photo> Photos { get; set; }
        public Vendor Vendor { get; set; }
        public int? VendorId { get; set; }
        public bool Deactivated { get; set; }
        public bool OutOfStock { get; set; }
        public ICollection<ProductProperty> ProductProperties{ get; set; }
        public ICollection<ProductOrder> ProductOrders{ get; set; }
        public string SearchParam { get; set; }
        public string UniqueParam { get; set; }
        public int ServiceId { get; set; }
        public Service Service { get; set; }
    }
}