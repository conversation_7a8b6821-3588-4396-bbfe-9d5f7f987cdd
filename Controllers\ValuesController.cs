﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using QuickMessenger.API.Data.IRepo;

namespace QuickMessenger.API.Controllers
{
    [Authorize(Roles ="SuperAdmin")]
    [Route("api/[controller]")]
    [ApiController]
    public class ValuesController : ControllerBase
    {
        private readonly IQuickMessengerRepo _qmRepo;
        public ValuesController(IQuickMessengerRepo qmRepo)
        {
            _qmRepo = qmRepo;

        }
        // GET api/values
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var  admins = await _qmRepo.GetAdmins();
            var client = await _qmRepo.GetClients();
            return Ok(admins);
        }

        // GET api/values/5
        [HttpGet("{id}")]
        public ActionResult<string> Get(int id)
        {
            return "value";
        }

        // POST api/values
        [HttpPost]
        public void Post([FromBody] string value)
        {
        }

        // PUT api/values/5
        [HttpPut("{id}")]
        public void Put(int id, [FromBody] string value)
        {
        }

        // DELETE api/values/5
        [HttpDelete("{id}")]
        public void Delete(int id)
        {
        }
    }
}
