using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using QuickMessenger.API.Data.DTO.Client;
using QuickMessenger.API.Data.DTO.Shared;
using QuickMessenger.API.Data.DTO.Staff;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Data.Repo;

namespace QuickMessenger.API.Controllers.Client
{
    [ApiController]
    [Authorize(Policy = "QuickWaka")]
    [Route("api/client/[controller]")]
    public class ClientController : ControllerBase
    {
        private readonly IUserRepo _userRepo;
        private readonly IMapper _mapper;
        private readonly UserManager<User> _userManager;
        private readonly IOrderRepo _orderRepo;

        public ClientController(IUserRepo userRepo, I<PERSON>apper mapper, UserManager<User> userManager, IOrderRepo orderRepo)
        {
            this._userManager = userManager;
            this._mapper = mapper;
            this._userRepo = userRepo;
            this._orderRepo = orderRepo;

        }

        [HttpPost("addresses")]
        public async Task<IActionResult> getAllAddresses()
        {
            var Id = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var user = await _userRepo.GetAllUserAddresses(Id);
            var addressesToRet = _mapper.Map<ICollection<AddressDto2>>(user.Addresses);
            foreach (AddressDto2 add in addressesToRet)
            {
                if (add.Id == user.DefaultAddressId)
                {
                    add.DefaultAdd = true;
                }
            }
            return Ok(addressesToRet);
        }

        [HttpPost("addresses/{searchParam}")]
        public async Task<IActionResult> getAllAddresses(string SearchParam)
        {
            var Id = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var user = await _userRepo.GetAllUserAddresses(Id);
            SearchParam = SearchParam.ToUpper();
            var addresses = user.Addresses.Where(p => !string.IsNullOrEmpty(p.SearchParam) && p.SearchParam.Contains(SearchParam)).ToList();
            var addressesToRet = _mapper.Map<ICollection<AddressDto2>>(addresses);
            foreach (AddressDto2 add in addressesToRet)
            {
                if (add.Id == user.DefaultAddressId)
                {

                    add.DefaultAdd = true;
                }
            }
            return Ok(addressesToRet);
        }

        [HttpPut("address/makeDefault/{Id}")]
        public async Task<IActionResult> setDefaultAddress(int Id)
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var address = await _userRepo.GetAddress(Id);
            if (userId != address.UserId)
            {
                return BadRequest();
            }

            var user = await _userRepo.GetClient(Id);
            user.DefaultAddressId = address.Id;

            if(await _userRepo.SaveAll())

            return NoContent();

            else return BadRequest();

        }

        [HttpPost("address/add")]
        public async Task<IActionResult> AddnewAddress(AddressDto2 Address){
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var AddresstoCreate = _mapper.Map<Address>(Address);
            AddresstoCreate.UserId  = userId;
           
            _userRepo.Add(AddresstoCreate);

            if(await _userRepo.SaveAll())
            {
                 if(Address.DefaultAdd)
                    {
                        var user = await _userRepo.GetClient(userId);
                        user.DefaultAddressId = AddresstoCreate.Id;
                        await _userRepo.SaveAll();
                    }
                    return Ok();
            }
            

            return BadRequest("We had a challenge adding your new address.");

        }

        [HttpGet("address/{Id}")]
        public async Task<IActionResult> GetAddress(int Id){
             var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
             var user = await _userRepo.GetClient(userId);
            var address = await _userRepo.GetAddress(Id);
            if(userId != address.UserId)
            {
                return BadRequest("Unauthorised request");
            }
           
            var addresToreturn = _mapper.Map<AddressDto2>(address);
            addresToreturn.DefaultAdd = user.DefaultAddressId == Id;
            return Ok(addresToreturn);
        }
        [HttpPut("address/{Id}/update")]
        public async Task<IActionResult> UpdateAddress(int Id, AddressDto2 Address){
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var AddresstoUpdate = await _userRepo.GetAddress(Id);
            if(userId != AddresstoUpdate.UserId)
            {
                return BadRequest("Unauthorised update");
            }
            if(Id != Address.Id)
            return BadRequest("Invalid request Id");

            _mapper.Map(Address, AddresstoUpdate);

           
            var updated = await _userRepo.SaveAll();
            bool madeDefault = false;
                if(Address.DefaultAdd)
                {
                    var user = await _userRepo.GetClient(userId);
                    user.DefaultAddressId = Address.Id;
                    madeDefault = await _userRepo.SaveAll();
                }
                if(updated || madeDefault){
                    return Ok();
                }
          
            

            else return BadRequest("We had a challenge attmepting to update your address");
        }

        [HttpDelete("address/{Id}")]
        public async Task<IActionResult> DeleteAddress(int Id){
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var AddresstoDelete = await _userRepo.GetAddress(Id);
            if(userId != AddresstoDelete.UserId)
            {
                return BadRequest("Unauthorised request");
            }
            if(await  _userRepo.AddressHasOrder(AddresstoDelete.Id))
            {
                AddresstoDelete.User = null;
                AddresstoDelete.UserId = null;
            }
            else
            _userRepo.Delete(AddresstoDelete);
            if(await _userRepo.SaveAll())
            return Ok();

            return BadRequest("We could not delete the address");

        }

        [HttpGet("user/{Id}")]
        public async Task<IActionResult> GetClient(int Id)
        {
             var loggedInId = Convert.ToUInt32(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            if (loggedInId != Id)
                return Unauthorized();
            var user = await _userRepo.GetClient(Id);
            //if user is a Rider
            if (user.GetType().Name.Equals("Rider"))
            {
                var rider = (Rider)user;
                var riderToReturn = _mapper.Map<RiderDetailViewDto>(rider);
                return Ok(riderToReturn);
            }
            var userToReturn  = _mapper.Map<ClientDetailViewDto>(user);
            return Ok(userToReturn);
        }

        [HttpPut("user/{Id}")]
        public async Task<IActionResult> UpdateUser(int Id, ClientDetailViewDto client)
        {
             var loggedInId = Convert.ToInt32(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            if (loggedInId != Id || loggedInId != client.Id)
                return Unauthorized();
            var user = await _userRepo.GetClientOnlyDetail(Id);
            
            var existingUSer = await _userRepo
                        .GetDiffUserWithEmailOrPhone(client.Email, client.PhoneNumber, loggedInId);
                if (existingUSer != null)
                {
                    var errors = new List<object>();

                    if(client.Email.CompareTo(user.Email) !=0)
                    errors.Add(new { Email = "Email cannot be changed" });

                    if (client.PhoneNumber.Equals(existingUSer.PhoneNumber))
                        errors.Add(new { Phone = "Phone number already exists" });

                    return BadRequest(new { Errors = errors });
                }

                _mapper.Map(client, user);
                if(await _userRepo.SaveAll())
                return NoContent();

            return BadRequest();
        }

        [HttpPut("updateFCM/{Id}")]
        public async Task<IActionResult> UpdateFCMToken(int Id, FCmTokenDto client)
        {
             var loggedInId = Convert.ToUInt32(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            if (loggedInId != Id)
                return Unauthorized();
            var user = await _userRepo.GetClientOnlyDetail(Id);
              user.FCMToken = client.token;
                if(await _userRepo.SaveAll())
                return NoContent();

            return BadRequest();
        }

        [HttpPut("updatePassword/{Id}")]
        public async Task<IActionResult> UpdateClientPassword(int Id, StaffUpdatePassWord client)
        {
            var loggedInId = Convert.ToUInt32(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            if (client.Id != Id || loggedInId != Id)
                return Unauthorized();
            var user = await _userManager.FindByNameAsync(client.UserName);
            if(null == user)
            return BadRequest(new {errors = new {Error = "Invalid user"}});
            if (client.NewPassword.CompareTo(client.CurrentPassword) == 0) {
                return BadRequest(new {errors = new { NewPassword = new String[] {"Your new password should not be the same as your old password"}}});
            }
                
            var result = await _userManager.ChangePasswordAsync(user, client.CurrentPassword, client.NewPassword);

            if (result.Succeeded)
                return NoContent();

            return BadRequest(result.Errors);
        }

    
        [HttpDelete("delete/{Id}")]
        public async Task<IActionResult> DeleteClient(int Id)
        {
            var loggedInId = Convert.ToUInt32(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            if (loggedInId != Id)
                return Unauthorized();
            var user = await _userRepo.GetClient(Id);
            var lastDeleted = await _userRepo.GetLastDeletedUser();
            int count = 0;
            if(null != lastDeleted)
            {
                var split = lastDeleted.UserName.Split("_")[1].Split("@");
                count  = Int32.Parse(split[0]);
            }
             count++;
            user.LastName = "Deleted User_" + count.ToString();
            user.FirstName = "Deleted User_" + count.ToString();
            user.Email = "deleted_" + count.ToString();
            user.UserName = "deleted_" + count.ToString();
            user.NormalizedUserName = "DELETED_" + count.ToString() + "@DELETED.COM";
            user.NormalizedEmail = "DELETED_" + count.ToString() + "@DELETED.COM";
            user.Deleted = true;

            if(await _userRepo.SaveAll())
            return Ok();

            return BadRequest();
        }
     //TODO: Remeber to duplicate address from client and add tie to an order when an order is placed
    }
}