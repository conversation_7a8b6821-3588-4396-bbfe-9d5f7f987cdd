using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using QuickMessenger.API.Data.DTO.Vendor;

namespace QuickMessenger.API.Data.DTO.Staff
{
    public class UserDetailDto
    {
        [Required]
        public string  LastName { get; set; }
        [Required]
        public string FirstName { get; set; }
        [Required]
        public string Gender { get; set; }
        [Required]
        public string Phone { get; set; }
        public string Email { get; set; }
        public string PhotoUrl { get; set; }
        public bool HasPendingCart { get; set; }
        public int Id { get; set; }

        public int? VendorId { get; set; }
        public string vendorLogo { get; set; }

        public VendorDtoLite Vendor { get; set; }

    }
}