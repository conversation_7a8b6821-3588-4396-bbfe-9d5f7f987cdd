using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace QuickMessenger.API.Data.Model
{
    public class Order
    {
        public int Id { get; set; }
        public User Client { get; set; }
        public int ClientId { get; set; }
        public Address Address { get; set; }
        public int? AddressId {get; set;}
        public DateTime Date { get; set; }
        public DateTime PickupTime { get; set; }
        public int? RiderId { get; set; }
        public Rider Rider { get; set; }
        public ICollection<PickupItem> PickupItems { get; set; }
        public string Type { get; set; }
        public ICollection<ProductOrder> ProductOrders { get; set; }
          //State of the Order is any of the following 'Pending', 'Delivered', 'On The Way'
        public string State { get; set; }
        public string SearchParam { get; set; }
        public double Cost { get; set; }
        public double DeliveryCharge { get; set; }
        public double RiderEarning { get; set; }
        public double  ServiceCharge { get; set; }
        public DateTime TimeDelivered { get; set; }
        public int CartId { get; set; }
        public Cart Cart { get; set; }
        public int ServiceId { get; set; }
        public Service Service { get; set; }
        public string TrackingId { get; set; }
    }
}