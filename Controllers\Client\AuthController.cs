using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using QuickMessenger.API.Data.DTO.Client;
using Clayant =  QuickMessenger.API.Data.Model.Client;
using QuickMessenger.API.Data.DTO.Shared;
using QuickMessenger.API.Data.DTO.Staff;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.IService;
using QuickMessenger.API.Data.Model;
using System;
using Microsoft.Extensions.Configuration;
using QuickMessenger.API.Data.DTO.Vendor;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;

namespace QuickMessenger.API.Controllers.Client
{
    [ApiController]
    [AllowAnonymous]
    [Route("api/client/[controller]")]

    
    public class AuthController: ControllerBase
    {
         private readonly SignInManager<User> _signinManager;
        private readonly UserManager<User> _userManager;
        private readonly IAuthService _authService;
        private readonly IMapper _mapper;
        private readonly IUserRepo _userRepo;
        private readonly IUserService _userService;
        private readonly IConfiguration _config;
        private readonly IVendorRepo _vendorRepo;

        public AuthController(UserManager<User> _userManager,
        SignInManager<User> _signinManager, IAuthService _authService, 
        IMapper _mapper, IUserRepo userRepo,
        IUserService userService, IConfiguration config, IVendorRepo _vendorRepo){
        _userRepo = userRepo;
            this._mapper = _mapper;
            this._authService = _authService;
            this._userManager = _userManager;
            this._signinManager = _signinManager;
            this._userService = userService;
            this._config = config;
            this._vendorRepo = _vendorRepo;
        }
            [HttpPost("register")]
            public async Task<IActionResult> Register(ClientRegisterDto clientTobeCreated){
             //Check if user exists in the repo
                    var existingUSer = await _userRepo
                                .GetUserByEmail(clientTobeCreated.Email);
                    if (existingUSer != null && existingUSer.EmailConfirmed)
                        return BadRequest(new {Error = "The email address has been used to regiseter"});
                        //get user by phone number
                    existingUSer = await _userRepo
                                .GetUserByPhone(clientTobeCreated.Phone);
                    if (existingUSer != null && existingUSer.EmailConfirmed)
                        return BadRequest(new {Error = "The phone number has been used to regiseter"});

                        if(existingUSer != null && !existingUSer.EmailConfirmed)
                        {
                            await _userManager.DeleteAsync(existingUSer);
                        }
                    var userCreated = await _userService.CreateClient(clientTobeCreated);
                    if ( null != userCreated)
                    {
                   
                    var token = await _userManager.GenerateEmailConfirmationTokenAsync(userCreated);
                    //var confirmationLink = Url.Action(nameof(ConfirmEmail), "Auth", new { token, email = userCreated.Email }, Request.Scheme);
                    var confirmationLink  = 
                    Url.Content(_config.GetSection("Redirects:email_confirmation").Value + "?email=" +userCreated.Email + "&token="+token  );
                    await _userService.SendClientRegisterationEmail(userCreated, confirmationLink);
                        return Ok(new { Id = userCreated.Id });
                    }
                    return BadRequest(new { Error = "There was a challenge creating your account" });
            }

            [HttpGet("confirmemail")]
            public async Task<IActionResult> ConfirmEmail(string token, string email)
            {
                var user = await _userManager.FindByEmailAsync(email);
                if (user == null)
                    return BadRequest(new { Error = "Invalid email verification" });
            
                var result = await _userManager.ConfirmEmailAsync(user, token.Replace(" ", "+"));
                
                if(result.Succeeded )
                return Ok();
             return BadRequest(new { Error = "Invalid email verification" });
            }

            [HttpPost("resendemailconfirmation")]
            public async Task<IActionResult> ResendEmailConfirmation(CredentialDto credential){
                var user = await _userManager.FindByEmailAsync(credential.Username);
                if(null == user)
                return BadRequest(new { Error = "We had trouble finding this account"});

                if(!user.EmailConfirmed)
                {
                    var token = await _userManager.GenerateEmailConfirmationTokenAsync(user);
                    //var confirmationLink = Url.Action(nameof(ConfirmEmail), "Auth", new { token, email = user.Email }, Request.Scheme);
                    var confirmationLink  = 
                    Url.Content(_config.GetSection("Redirects:email_confirmation").Value + "?email=" +user.Email + "&token="+token  );
                    await _userService.SendClientRegisterationEmail(user, confirmationLink);
                    return Ok();
                }

                else return BadRequest(new { Error = "This email has been verified"});
                  

            }

            [HttpPost("resetpassword")]
            public async Task<IActionResult> PasswordReset(CredentialDto credential){
                var user = await _userManager.FindByEmailAsync(credential.Username);

                if(null == user)
                return BadRequest(new { Error = "We had trouble finding this account"});

                if(!user.EmailConfirmed)
                {
                    return BadRequest(new {Error = "Your email has not be confirmed"});
                }

                var token = await _userManager.GeneratePasswordResetTokenAsync(user);
                //var confirmationLink = Url.Action(nameof(ChangePassword), "Auth", new { token, email = user.Email }, Request.Scheme);
                var confirmationLink  = 
                    Url.Content(_config.GetSection("Redirects:password_reset").Value + "?email=" +user.Email + "&token="+token  );
                await _userService.SendPassswordResetEmail(user, confirmationLink);
                return Ok();
            }
            
            [HttpPost("changepassword")]
            public async Task<IActionResult> ChangePassword(ChangePasswordDto changepassword){
                var user = await _userManager.FindByEmailAsync(changepassword.Username);
                 if(null == user)
                return BadRequest(new { Error = "We had trouble finding this account"});


                var result  = await _userManager.ResetPasswordAsync(user, changepassword.Token.Replace(" ", "+"), changepassword.Password);

                if(result.Succeeded)
                {
                    return Ok();
                }

                return BadRequest(new {Error = result.Errors});
            }

            [HttpPost("login")]
            public async Task<IActionResult> Signin(LoginDto loginDto){
              
                var user = await _userManager.FindByEmailAsync(loginDto.Username);

                if(user != null && !user.EmailConfirmed)
                    return BadRequest(new { Error = "Your email is yet to be confirmed!"});

                if (user != null && !user.Deactivated)
                {
                    var result = await _signinManager
                    .CheckPasswordSignInAsync(user, loginDto.Password, false);

                    if (result.Succeeded)
                    {
                        var userToreturn = _mapper.Map<UserDetailDto>(user);
                        if(null != userToreturn.VendorId  && userToreturn.VendorId != 0){
                             var vendor = await  _vendorRepo.GetVendor(userToreturn.VendorId ?? 0);
                             var photo = await  _vendorRepo.GetVendorPhoto(vendor);
                             userToreturn.vendorLogo = photo.Url;
                             userToreturn.Vendor = _mapper.Map<VendorDtoLite>(vendor);
                        }
                       
                        userToreturn.Phone = user.PhoneNumber;
                        userToreturn.HasPendingCart = await _userRepo.HasPendingCart(user.Id);
                        var role =  _authService.GetRole(user);
                        return Ok(new
                        {
                            token = _authService.GenerateTokenHandler(user, false),
                            user = userToreturn, 
                            userIsRider = role.Name.ToLower().CompareTo("rider") == 0 ? true : false
                        });
                    }

                    else{
                        return BadRequest(new { Error = "Invalid username or password"});
                    }

                }
                return BadRequest(new { Error = "Invalid username or password"});

            }

            [HttpPost("externallogin")]
            public async Task<IActionResult> SocialLogin(SocialLogin socialLogin) {
           // 
            User userFromRepo  = new User();
            UserDetailDto userDetail =  new UserDetailDto();
            if(socialLogin.Provider.CompareTo("facebook") == 0)
            {
             var user = await _authService.VerifyFacebookAccessToken(socialLogin.Token);
             userFromRepo = await _userManager.FindByEmailAsync(user.Email);
             userDetail = _mapper.Map<UserDetailDto>(user);
              if(null == user)
                 return Unauthorized();
            }
            else if(socialLogin.Provider.CompareTo("google") == 0)
            {
                 var payload = await _authService.ValidateGoogleToken(socialLogin.Token);
                 userFromRepo = await _userManager.FindByEmailAsync(payload.Email);
                 userDetail = _mapper.Map<UserDetailDto>(payload);
                 if(null == payload)
                 return Unauthorized();
            }
            else if(socialLogin.Provider.CompareTo("apple") == 0)
            {
                 try
                    {
                        var validatedToken = await _authService.ValidateAppleIdentityTokenAsync(socialLogin.Token);

                          if (validatedToken is JwtSecurityToken jwtToken)
                            { 
                                userDetail.Email = jwtToken.Claims.FirstOrDefault(c => c.Type == "email")?.Value;
                                userDetail.FirstName = jwtToken.Claims.FirstOrDefault(c => c.Type == "given_name")?.Value;
                                 userDetail.LastName = jwtToken.Claims.FirstOrDefault(c => c.Type == "family_name")?.Value;

                            }

                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex.Message);
                        // Handle exceptions (token validation failure, etc.)
                        return Unauthorized();
                    }
            }
            else{
                return Unauthorized();
            }
        
            if(null == userFromRepo)
            {
                var user = _mapper.Map<Clayant>(userDetail);
                user.LastActive = DateTime.Now;
                user.DateRegistered = DateTime.Now;
                user.EmailConfirmed = true;
                user.UserName = userDetail.Email;
                user.SearchParam =$"{userDetail.LastName ?? ""} {userDetail.FirstName} {userDetail.Email} {userDetail.Phone ?? ""}";
                var userCreated = await _userService.CreateClient(user);
                userFromRepo = userCreated;
            }

            var objectToreturn = new { token = _authService.GenerateTokenHandler(userFromRepo, true),  user = userDetail};
            
            return Ok(objectToreturn);
        }

      
    }


}