using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using QuickMessenger.API.Data.DTO.Product;
using QuickMessenger.API.Data.DTO.Vendor;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Helpers.Params;

namespace QuickMessenger.API.Controllers.Client
{
    [ApiController]
    [AllowAnonymous]
    [Route("api/client/[controller]")]
    public class ServiceController : ControllerBase
    {
        private readonly IMapper _mapper;
        private readonly IProductRepo _productRepo;
        private readonly IVendorRepo _vendorRepo;
        public ServiceController(IProductRepo productRepo, IMapper mapper, IVendorRepo vendorRepo)
        {
            this._mapper = mapper;
            this._productRepo = productRepo;
             this._vendorRepo = vendorRepo;


        }
        [HttpPost("all/lite")]
        public async Task<IActionResult> GetALLServicesLite()
        {
            var services = await _productRepo.GetAllServices();
            var objToReturn = _mapper.Map<ICollection<ServiceLiteDto>>(services);
            return Ok(objToReturn);
        }

        [HttpPost("all")]
        public async Task<IActionResult> GetALLServices()
        {
            var services = await _productRepo.GetServicesWithPictures();
            // var objToReturn = _mapper.Map<ICollection<ServiceLiteDto2>>(services);
            return Ok(services);
        }

        [HttpPost("top6")]
        public async Task<IActionResult> GetTop6Services()
        {
            var services = await _productRepo.GetTop6ServicesWithMostOrders();
            // var objToReturn = _mapper.Map<ICollection<ServiceLiteDto2>>(services);
            return Ok(services);
        }

        [HttpPost("products")]
        public async Task<IActionResult> getAllProductsByService([FromQuery]ServiceProductParams Param)
        {
            var products = await _productRepo.GetProducts(Param);
            var service = await _productRepo.GetService(Param.ServiceNameId);
                 var objToreturn = new
                    {
                        Products = _mapper.Map<IEnumerable<ProductListForClientDto>>(products.Item1),
                        Pagination = new
                        {
                            CurrentPage = products.Item1.CurrentPage,
                            PageSize = products.Item1.PageSize,
                            TotalCount = products.Item1.TotalCount,
                            TotalPages = products.Item1.TotalPages
                        },
                        ChildrenCategories = _mapper.Map<List<Prod_CategoryListDtoLite2>>(products.Item2),
                        MinPrice = products.Item4,
                        MaxPrice = products.Item5, 
                        Category = _mapper.Map<Prod_CategoryListDtoLite2>(products.Item3),
                        Service = _mapper.Map<ServiceLiteDto>(service)
                    };

            return Ok(objToreturn);
        }

        [HttpPost("vendor_products")]
        public async Task<IActionResult> getAllProductsByServiceAndVendor([FromQuery]ServiceVendorProductParams Param){
             var products = await _productRepo.GetProducts(Param);
            var service = await _productRepo.GetService(Param.ServiceNameId);
                 var objToreturn = new
                    {
                        Products = _mapper.Map<IEnumerable<ProductListForClientDto>>(products.Item1),
                        Pagination = new
                        {
                            CurrentPage = products.Item1.CurrentPage,
                            PageSize = products.Item1.PageSize,
                            TotalCount = products.Item1.TotalCount,
                            TotalPages = products.Item1.TotalPages
                        },
                        ChildrenCategories = _mapper.Map<List<Prod_CategoryListDtoLite2>>(products.Item2),
                        MinPrice = products.Item4,
                        MaxPrice = products.Item5,
                        Category = _mapper.Map<Prod_CategoryListDtoLite2>(products.Item3),
                        Service = _mapper.Map<ServiceLiteDto>(service)
                    };

            return Ok(objToreturn);
        }

        [HttpPost("top_prod_categories/{ServiceNameId}")]
        public async Task<IActionResult> getAllTopProductCategories(string ServiceNameId)
        {
            var categories = await _productRepo.GetCategoriesByService(ServiceNameId);
               
                 var objToreturn = new
                    {
                        categories = _mapper.Map<ICollection<Prod_CategoryListDtoLite2>>(categories)
                    };

            return Ok(objToreturn);
        }

        [HttpPost("{serviceNameId}")]
        public async Task<IActionResult> GetServiceLite(string serviceNameId)
        {
            var Service = await _productRepo.GetService(serviceNameId);
             if(Service == null)
            return BadRequest();
            var serviceToReturn = _mapper.Map<ServiceLiteDto2>(Service);

            return Ok(serviceToReturn);
        }

        [HttpPost("defaultGeneric")]
        public async Task<IActionResult> GetDefaultGenericService(){

            var service  = await _productRepo.GetDefaultGenericService();

            var serviceToReturn = _mapper.Map<ServiceLiteDto2>(service);

            return Ok(serviceToReturn);

        }

        [HttpGet("{Id}")]
        public async Task<IActionResult> GetService(int Id)
        {
            var service = await _productRepo.GetService(Id);
            if(service == null)
            return BadRequest();
            var serviceToReturn = _mapper.Map<ServiceLiteDto2>(service);
            
            return Ok(serviceToReturn);
        }

        [HttpPost("minmaxprice/{serviceNameId}")]
        public async Task<IActionResult> GetMinMaxPriceForAllProductsByService(string serviceNameId){

            var result = await _productRepo.GetMinMaxPriceForAllProductsByService(serviceNameId);
            return Ok(new {
                MinPrice = result.Item1,
                MaxPrice = result.Item2,
            });
        }

        [HttpPost("vendors/{Id}")]
        public async Task<IActionResult> GetVendorsByService(int Id, [FromQuery]ClientVendorParams Param)
        {
            var vendors = await _vendorRepo.GetVendorsByService(Id, Param);
             var objToreturn = new
                    {
                        Vendors = _mapper.Map<IEnumerable<VendorListDtoLite>>(vendors),
                        Cities  = await _vendorRepo.GetDistinctCitiesForParams(Param),
                        Pagination = new
                        {
                            CurrentPage = vendors.CurrentPage,
                            PageSize = vendors.PageSize,
                            TotalCount = vendors.TotalCount,
                            TotalPages = vendors.TotalPages
                        }
                    };
            return Ok(objToreturn); 
        }
    }
}