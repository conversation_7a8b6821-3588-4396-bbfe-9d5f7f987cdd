name: Build and Deploy to Winhost

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    env:
      BUILD_CONFIGURATION: Release
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '6.0.x'

      - name: Install libssl
        run: |
          sudo apt-get update
          sudo apt-get install -y libssl-dev

      - name: Restore dependencies
        run: dotnet restore QuickMessenger.API.csproj

      - name: Build
        run: dotnet build QuickMessenger.API.csproj --configuration ${{ env.BUILD_CONFIGURATION }}

      - name: Publish
        run: |
          dotnet publish QuickMessenger.API.csproj \
            --configuration ${{ env.BUILD_CONFIGURATION }} \
            --output publish

      - name: Deploy via FTP
        uses: SamKirkland/FTP-Deploy-Action@v4.3.5
        with:
          server: ${{ secrets.FTP_SERVER }}
          username: ${{ secrets.FTP_USERNAME }}
          password: ${{ secrets.FTP_PASSWORD }}
          protocol: ftp
          port: 21
          local-dir: ./publish/
          server-dir: '/quickwak/api/'
          exclude: |
            web.config
