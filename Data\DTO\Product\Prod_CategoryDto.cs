using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace QuickMessenger.API.Data.DTO.Product
{
    public class Prod_CategoryDto
    {
        public int Id { get; set; }
        [Required]
        public string  Name { get; set; }
        public string NameId { get; set; }
        public string Description { get; set; }
        public Prod_CategoryDto Parent { get; set; }
        public string ImageUrl { get; set; }
        public int ParentId { get; set; }
        public ICollection<Prod_PropertyDto> Properties { get; set; }
        public bool RemovePhoto { get; set; }
         public bool Candelete { get; set; }
    }

    public class Prod_CategoryForUpdateDto
    {
        public int Id { get; set; }
        [Required]
        public string  Name { get; set; }
        public string NameId { get; set; }
        public string Description { get; set; }
        public string ImageUrl { get; set; }
        public int ParentId { get; set; }
        public ICollection<Prod_PropertyDto> Properties { get; set; }
        public bool RemovePhoto { get; set; }
    }
}