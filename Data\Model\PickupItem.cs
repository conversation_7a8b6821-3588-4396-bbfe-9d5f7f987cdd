using System;
using System.Collections.Generic;

namespace QuickMessenger.API.Data.Model
{
    public class PickupItem
    {
        public int Id { get; set; }
        public Address Address { get; set; }
        public int? AddressId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Size { get; set; }
        public bool Fragile { get; set; }
        public Order Order { get; set; }
        public int OrderId { get; set; }
        public ICollection<Photo> Photos { get; set; }
        public int Quantity { get; set; }
        public double Value { get; set; }
        public string  State { get; set; }

    }
}