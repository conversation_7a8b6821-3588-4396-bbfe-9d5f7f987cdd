using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace QuickMessenger.API.Data.Model
{
    public class Prod_PropertyType
    {
        public int Id { get; set; }
        [Required]
        public string Name { get; set; }
        public string Description { get; set; }
        public ICollection<Property> Properties { get; set; }
        [NotMapped]
        public bool CanDelete { get; set; }
        public string SearchParam { get; set; }

    }
}