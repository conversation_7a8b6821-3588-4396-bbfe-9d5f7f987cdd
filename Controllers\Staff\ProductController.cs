using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using QuickMessenger.API.Data.DTO.Product;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.IService;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Helpers;
using QuickMessenger.API.Helpers.Params;
using QuickMessenger.API.Data.DTO.Shared;

namespace QuickMessenger.API.Controllers.Staff
{
    [ApiController]
    [Authorize(Policy = "ProdMgt")]
    [Route("api/qm_475/staff/[controller]")]
    
    public class ProductController : ControllerBase
    {
        private static string _photoPath = "qm/product/product";
        private static string _servicePhotoPath = "product/service";
        private static int maxPhotos = 5;
        private static int maxServicePhoto = 3;
        private readonly IProductRepo _prodRepo;
        private readonly IVendorRepo _vendorRepo;
        private readonly IMapper _mapper;
        private readonly IProductService _productService;
        private readonly IPhotoService _PhotoService;
        //
        public ProductController(IProductRepo prodRepo, IMapper mapper, IProductService productService, IPhotoService PhotoService, IVendorRepo vendorRepo)
        {
            _PhotoService = PhotoService;
            _productService = productService;
            _mapper = mapper;
            _prodRepo = prodRepo;
            _vendorRepo = vendorRepo;

        }

        [HttpGet]
        public async Task<IActionResult> GetProducts([FromQuery] ProductParams productParams)
        {  
            
            var products = await _prodRepo.GetProducts(productParams);

            var objToreturn = new
            {
                Products = _mapper.Map<ICollection<ProductListDto>>(products),
                Pagination = new
                {
                    CurrentPage = products.CurrentPage,
                    PageSize = products.PageSize,
                    TotalCount = products.TotalCount,
                    TotalPages = products.TotalPages
                }
            };

            return Ok(objToreturn);

        }
        
        [HttpGet("all")]
        public async Task<IActionResult> GetAllProducts([FromQuery] ProductParams productParams)
        {  
            
            var products = await _prodRepo.GetProducts(productParams);

            var objToreturn = new
            {
                Products = _mapper.Map<ICollection<ProductListDto>>(products),
                Pagination = new
                {
                    CurrentPage = products.CurrentPage,
                    PageSize = products.PageSize,
                    TotalCount = products.TotalCount,
                    TotalPages = products.TotalPages
                }
            };

            return Ok(objToreturn);

        }

        [HttpGet("inactive")]
        public async Task<IActionResult> GetInactiveProducts([FromQuery] ProductParams productParams)
        {

            var products = await _prodRepo.GetInactiveProducts(productParams);
            var objToreturn = new
            {
                Products = _mapper.Map<IEnumerable<ProductDto>>(products),
                Pagination = new
                {
                    CurrentPage = products.CurrentPage,
                    PageSize = products.PageSize,
                    TotalCount = products.TotalCount,
                    TotalPages = products.TotalPages
                }
            };

            return Ok(objToreturn);

        }

        [HttpGet("{Id}")]
        public async Task<IActionResult> GetProduct(int Id)
        {
            var product = await _prodRepo.GetProduct(Id);
            if(product == null)
            return BadRequest();
            var productToReturn = _mapper.Map<ProductDto>(product);
            productToReturn.ProductProperties = await _prodRepo.GetProductProperties(Id);
            return Ok(productToReturn);
        }

        [HttpDelete("{Id}/delete")]
        public async Task<IActionResult> DeleteProduct(int Id)
        {
            if (!await _prodRepo.ProdcutHasRecords(Id))
            {
                var Product = await _prodRepo.GetProduct(Id);
                ICollection<Photo> Photos = Product.Photos;

                foreach (Photo photo in Photos)
                {
                    _PhotoService.DeletePhoto(photo.PublicId);
                    //_prodRepo.Delete(photo);
                }
                _prodRepo.DeleteAll(Photos);
                _prodRepo.Delete(Product);
                //Phot
                if (await _prodRepo.SaveAll())
                {
                    return Ok();
                }
            }
            else
            {
                return BadRequest(new { Error = "Could not delete product, because it has existing rercords" });
            }
            return BadRequest(new { Error = "Could not create Product Category" });
        }

        [HttpPost("{Id}/addPhoto")]
        public async Task<IActionResult> AddProductPhoto([FromForm] IFormFile file, int Id){
         if(  await _productService.AddProductPhoto(file, Id, _photoPath, maxPhotos))
         return
            NoContent();
            
           return BadRequest(new { Error = "Could not upload Product photo" });
        }

        [HttpPost("create")]
        public async Task<IActionResult> CreateProduct(ProductForCreateDto productDto){
            Vendor vendor = new Vendor();
            Prod_Category category = new Prod_Category();
            Service service = new Service();
            category = await _prodRepo.GetCategory(productDto.Prod_CategoryId);

            service = await _prodRepo.GetService(productDto.ServiceId);
            var properties = productDto.ProductProperties;
            productDto.ProductProperties = null;
            if(null != productDto.VendorId)
            {
                 vendor = await _vendorRepo.GetVendor(productDto.VendorId.GetValueOrDefault());
                if(null != vendor && await _prodRepo.ProductNameExistsInVendor(productDto.Name, vendor, category))
                {  
                    return BadRequest(new { Error = "A product with similar name already exists in the selected vendor and category" });
                }
            }
            
            if(!await _prodRepo.ProductNameExistsInCategory(productDto.Name, category))
            {
                Prod_Category CategoryParent = new Prod_Category();
                if(null != category.Parent)
                CategoryParent = category.Parent;
                var ProductTocreate =  _mapper.Map<Product>(productDto);
                ProductTocreate.SearchParam = $"{ProductTocreate.Name}".ToUpper();
                _prodRepo.Add(ProductTocreate);
                if(
                    await _prodRepo.SaveAll() && await _productService.CreateProductProperties(properties,ProductTocreate.Id )
                    )
                return Ok(new { Id = ProductTocreate.Id });
            }else{
                 return BadRequest(new { Error = "A product with similar name already exists in the selected category" });
            }

           return BadRequest(new { Error = "Could not create product" });
        }
         
        [HttpPut("{Id}/update")]
        public async Task<IActionResult> UpdateProduct(ProductForCreateDto productDto, int Id){
            Product ProductToupdate =  await _prodRepo.GetProductForUpdate(Id);
            Prod_Category category = await _prodRepo.GetCategory(productDto.Prod_CategoryId);
             Vendor vendor = new Vendor();
            ICollection<ProductPropertyDto> properties = productDto.ProductProperties;
            ICollection<PhotoDto> Photos  = productDto.Photos;
            productDto.Photos = null;
            productDto.ProductProperties = null;

            _mapper.Map(productDto, ProductToupdate);
            if(null != productDto.VendorId && productDto.VendorId != 0)
            {// if product has vendor, check for duplicate vendor name 
                vendor = await _vendorRepo.GetVendor(productDto.VendorId.GetValueOrDefault());

                if(null != vendor && await _prodRepo.ProductNameExistsInVendor(productDto.Name, vendor, category,Id))
                {  
                    return BadRequest(new { Error = "A product with similar name already exists in the selected vendor and category" });
                }

              // ProductToupdate.Vendor = vendor;

            }

             if(!await _prodRepo.ProductNameExistsInCategory(productDto.Name, category, Id))
            {
                 
                Prod_Category CategoryParent = new Prod_Category();
                if(null != category.Parent)
                CategoryParent = category.Parent;
                ProductToupdate.SearchParam = $"{ProductToupdate.Name}".ToUpper();
                var updated = await _prodRepo.SaveAll();

                var deletedPhoto = await _productService.DeleteProductPhotos(Photos, _photoPath);
                var updatedProps = await _productService.UpdateProductProperties(properties, Id);
                if(updated || deletedPhoto || updatedProps)
                {
                    return NoContent();
                }
                else {
                        return BadRequest(new { Error = "could not update product!" });
                }

            }else{

                 return BadRequest(new { Error = "A product with similar name already exists in the selected category" });
            }
             
        }

        [HttpGet("categories/lite")]
        public async Task<IActionResult> GetCategories()
        {
            var prod_category = await _prodRepo.GetCategories();
            var objToreturn = new
            {
                Categories = _mapper.Map<IEnumerable<Prod_CategoryListDtoLite>>(prod_category),
                
            };

            return Ok(objToreturn);
        }
       
        [HttpGet("categories")]
        public async Task<IActionResult> GetCategories([FromQuery]Prod_CategoryParams CategoryParam)
        {
            var prod_category = await _prodRepo.GetCategories(CategoryParam);
            var objToreturn = new
            {
                Categories = _mapper.Map<IEnumerable<Prod_CategoryListDto>>(prod_category),
                Pagination = new
                {
                    CurrentPage = prod_category.CurrentPage,
                    PageSize = prod_category.PageSize,
                    TotalCount = prod_category.TotalCount,
                    TotalPages = prod_category.TotalPages
                }
            };
            return Ok(objToreturn);
        }

        [HttpGet("categories/parents")]
        public async Task<IActionResult> GetCanBeParentCategories()
        {
            var prod_category = await _prodRepo.GetCanBeParentCategories();
            var CategoriesToReturn = _mapper.Map<IEnumerable<Prod_CategoryDto>>(prod_category);
            return Ok(CategoriesToReturn);
        }

        [HttpDelete("categories/{Id}/delete")]
        public async Task<IActionResult> DeleteProd_Category(int Id)
        {
            if (await _prodRepo.CategoryHasRecords(Id))
            {
                return BadRequest(new { Error = "This category has products or properties depending on it" });
            }
            var CategoryToDelete = await _prodRepo.GetCategory(Id);
            _prodRepo.Delete(CategoryToDelete);
            var properties = await _prodRepo.GetCategoryProperties(Id);
            if (properties != null)
                _prodRepo.DeleteAll(properties);
            _productService.DeleteCategoryPhoto(CategoryToDelete);
            if (await _prodRepo.SaveAll())
                return Ok();

            return BadRequest(new { Error = "Could not delete Product Category" });
        }

        [HttpPut("categories/{Id}/update")]
        public async Task<IActionResult> UpdateProd_Category(int Id, Prod_CategoryForUpdateDto Prod_CategoryDto)
        {
            var ParentCategory = await _prodRepo.GetTrackedCategory(Prod_CategoryDto.ParentId);
            var Category = await _prodRepo.GetTrackedCategory(Id);
            Prod_CategoryDto.NameId = Prod_CategoryDto.Name.GenerateValidNameId();
            ICollection<Prod_PropertyDto> Prod_properties = Prod_CategoryDto.Properties;
            Prod_CategoryDto.Properties = null;
            if(Category.ParentId != Prod_CategoryDto.ParentId)
            {
                //get all product properties that belong to products under this category and properties of the parent category and delete them

               var properties1 = await  _prodRepo.GetProductProperties(Category.ParentId ?? 0, Category.Id);
               _prodRepo.DeleteAll(properties1);
               if(Category.ParentId != null)
               {
                   var CategoryParent = await _prodRepo.GetCategory(Category.ParentId ?? 0);
                   var properties2 = await _prodRepo.GetProductProperties(CategoryParent.ParentId ?? 0, Category.Id);
                   _prodRepo.DeleteAll(properties2);
               }
               
            }
            _mapper.Map(Prod_CategoryDto, Category);
            if (ParentCategory != null)
            {
                Category.NameId = ParentCategory.NameId + "/" + Prod_CategoryDto.NameId;
                Category.Parent = ParentCategory;
            }
            else
                Category.ParentId = null;
            if (await _prodRepo.CategoryNameIdExists(Category.NameId, Prod_CategoryDto.Id))
                return BadRequest(new { Error = "A category already exists with a similar name" });


            if (await _productService.UpdateCategoryProperties(Prod_properties, Category))
            {
                if (Prod_CategoryDto.RemovePhoto)
                {
                    Category.ImageUrl = null;
                    _productService.DeleteCategoryPhoto(Category);
                }

                if (await _prodRepo.SaveAll())
                {
                    return NoContent();
                }
            }
            else
                return BadRequest(new { Error = "The deleted Category's properties have been assigned to products or the newly added properties already exists" });

            return BadRequest(new { Error = "Could not update Category" });
        }

        [HttpPost("categories/create")]
        public async Task<IActionResult> CreateProd_Category(Prod_CategoryDto Prod_CategoryDto)
        {
            var ParentCategory = await _prodRepo.GetTrackedCategory(Prod_CategoryDto.ParentId);
            Prod_CategoryDto.NameId = Prod_CategoryDto.Name.GenerateValidNameId();
            var Category = _mapper.Map<Prod_Category>(Prod_CategoryDto);
            if (ParentCategory != null)
            {
                Category.NameId = ParentCategory.NameId + "/" + Prod_CategoryDto.NameId;
                Category.Parent = ParentCategory;
            }
            else
                Category.ParentId = null;
            if (await _prodRepo.CategoryNameIdExists(Category.NameId))
                return BadRequest(new { Error = "A Category already exists with a similar name" });

            _prodRepo.AddCategory(Category);
            if (await _prodRepo.SaveAll())
            {
                return Ok();
            }
            return BadRequest(new { Error = "Could not create Product Category" });
        }

       
        [HttpPost("service/create")]
        public async Task<IActionResult> CreateService(ServiceDto serviceDto){
            var service = _mapper.Map<Service>(serviceDto);
            service.NameId = service.Name.GenerateValidNameId();
            if(await _prodRepo.ServiceNameExists(serviceDto.Name))
            return BadRequest(new { Error = "Service name already exists"});
            _prodRepo.Add(service);
            if(await _prodRepo.SaveAll())
            {
                return Ok(new { Id = service.Id });
            }
            return BadRequest(new { Error = "There was a challege creating service"});
        }

        [HttpPost("service/{Id}/addPhoto")]
        public async Task<IActionResult> AddServicePhoto([FromForm] IFormFile file, int Id){
            var service = await _prodRepo.GetServiceWithPhotos(Id);
         if(  await _productService.AddServicePhoto(file,  service, maxServicePhoto))
         return
            NoContent();
            
           return BadRequest(new { Error = "Could not upload service photo" });
        }
        
        [HttpGet("services")]
        public async Task<IActionResult> GetServices([FromQuery]ServiceParams ServiceParam){
            var services = await _prodRepo.GetServices(ServiceParam);
            var objToreturn = new
            {
                Categories = _mapper.Map<IEnumerable<ServiceListDto>>(services),
                Pagination = new
                {
                    CurrentPage = services.CurrentPage,
                    PageSize = services.PageSize,
                    TotalCount = services.TotalCount,
                    TotalPages = services.TotalPages
                }
            };
            return Ok(objToreturn);
        }

        [HttpPost("services")]
        public async Task<IActionResult> GetServices(){
            var services = await _prodRepo.GetAllServices();
            var objToreturn = _mapper.Map<ICollection<ServiceListDto>>(services);
            return Ok(objToreturn);
        }

        [HttpGet("services/{id}")]
        public async Task<IActionResult> GetService(int id){
            var services = await _prodRepo.GetService(id);
            var objToreturn = _mapper.Map<ServiceDto>(services);
            return Ok(objToreturn);
        }

        [HttpPut("services/{id}/update")]
        public async Task<IActionResult> UpdateService(ServiceDto serviceDto, int id){
          
            
              ICollection<PhotoDto> Photos  = serviceDto.Photos;
            serviceDto.Photos = null;     
            var defaultService = await _prodRepo.GetDefaultGenericService();
            if(null != defaultService && defaultService.Id != serviceDto.Id && serviceDto.IsDefaultGeneric )
            {
                defaultService.IsDefaultGeneric = false;
                await _prodRepo.SaveAll();
            }
             var service = await _prodRepo.GetServiceWithoutPhotos(id);     
            _mapper.Map(serviceDto, service);

            if(!await _prodRepo.ServiceNameExists(serviceDto.Name, id))
            {         
                var updated = await _prodRepo.SaveAll();

                var deletedPhoto = await _productService.DeleteProductPhotos(Photos, _servicePhotoPath);
               
                if(updated || deletedPhoto)
                {
                    return NoContent();
                }
                else {
                        return NoContent();
                }
            }

            return BadRequest(new  {Error = "Could not update service" });
        }

       [HttpDelete("services/{id}/delete")]
        public async Task<IActionResult> DeleteService(int id){
            var service = await _prodRepo.GetServiceWithPhotos(id);
            if(null == service)
            return BadRequest();
            if(! await _prodRepo.ServiceCanBeDeleted(id))
            {
                return BadRequest(new {Error = "Cannot delete service because there are products that depends on it"});
            }
               ICollection<Photo> Photos = service.Photos;
               if(null != Photos){
                    foreach (Photo photo in Photos)
                        {
                            _PhotoService.DeletePhoto(photo.PublicId);
                            //_prodRepo.Delete(photo);
                        }
                _prodRepo.DeleteAll(Photos);
                 if(!await _prodRepo.SaveAll())
                 return BadRequest();
               }
            _prodRepo.Delete(service);
            if(await _prodRepo.SaveAll())
            {
                return NoContent();
            }
            return BadRequest(new  {Error = "Could not update service" });
        }

        [HttpPost("categories/{Id}/addPhoto")]
        public async Task<IActionResult> AddCategoryPhoto([FromForm] IFormFile file, int Id)
        {
            var Category = await _prodRepo.GetTrackedCategory(Id);
            if (await _productService.AddCategoryPhoto(file, Category))
                return Ok();

            return BadRequest(new { Error = "Could not create Product Category" });
        }
        
        [HttpGet("categories/{Id}/properties")]
        public async Task<IActionResult> GetCategoryProperties(int Id)
        {
            var properties = await _prodRepo.GetCategoryPropertiesWithRelations(Id);
            return Ok(_mapper.Map<ICollection<Prod_PropertyDto>>(properties));
        }
        
        [HttpGet("categories/{Id}")]
        public async Task<IActionResult> GetCategory(int Id)
        {
            var category = await _prodRepo.GetCategory(Id);
            if(category == null)
            return BadRequest();
            category.Properties = await _prodRepo.GetCategoryPropertiesWithRelations(Id);
            var CategoryDto = _mapper.Map<Prod_CategoryDto>(category);
            return Ok(CategoryDto);
        }

        [HttpPost("propertyTypes")]
        public async Task<IActionResult> GetPropertyTypes()
        {
            var Prod_PropertyTypes = await _prodRepo.GetPropertyTypes();
            return
            Ok(_mapper.Map<IEnumerable<Prod_PropertyTypeDto>>(Prod_PropertyTypes));
        }

        [HttpGet("propertyTypes")]
        public async Task<IActionResult> GetPropertyTypes([FromQuery]Prod_PropertyTypeParams Prod_PropertyTypeParams)
        {

            var Prod_PropertyTypes = await _prodRepo.GetPropertyTypes(Prod_PropertyTypeParams);
            var objToreturn = new
            {
                propertyTypes = _mapper.Map<IEnumerable<Prod_PropertyTypeDto>>(Prod_PropertyTypes),
                Pagination = new
                {
                    CurrentPage = Prod_PropertyTypes.CurrentPage,
                    PageSize = Prod_PropertyTypes.PageSize,
                    TotalCount = Prod_PropertyTypes.TotalCount,
                    TotalPages = Prod_PropertyTypes.TotalPages
                }
            };
            return Ok(objToreturn);

        }
        
        [HttpGet("propertyTypes/{id}")]
        public async Task<IActionResult> GetPropertyTypeById(int id)
        {
            var Prod_PropertyTypes = await _prodRepo.GetPropertyType(id);
            var propertyType = _mapper.Map<Prod_PropertyTypeDto>(Prod_PropertyTypes);
            return Ok(propertyType);

        }

        [HttpDelete("propertyTypes/{Id}/delete")]
        public async Task<IActionResult> DeleteProd_PropertyType(int Id)
        {
            if (await _prodRepo.PropertyTypeHasRecords(Id))
            {
                return BadRequest(new { Error = "This Property type has products or properties depending on it" });
            }
            var propertTypeToDelete = await _prodRepo.GetPropertyType(Id);
            _prodRepo.Delete(propertTypeToDelete);
            var properties = await _prodRepo.GetPropertyTypeProperties(Id);
            if (properties != null)
                _prodRepo.DeleteAll(properties);
            if (await _prodRepo.SaveAll())
                return Ok();

            return BadRequest(new { Error = "Could not delete  Property Type" });
        }

        [HttpPost("propertyTypes/create")]
        public async Task<IActionResult> CreatePropertyTypes(Prod_PropertyTypeDto PropertyTypeDto)
        {

            if (await _prodRepo.Prod_PropertyTypeNameExists(PropertyTypeDto.Name))
                return BadRequest(new { Error = "A property with the same name already exists" });

            var PropertyTypeToCreate = _mapper.Map<Prod_PropertyType>(PropertyTypeDto);
            _prodRepo.Add(PropertyTypeToCreate);
            if (await _prodRepo.SaveAll())
                return Ok(_mapper.Map<Prod_PropertyTypeDto>(PropertyTypeToCreate));

            else return BadRequest(new { Error = "Could not Create Property Type" });
        }

        [HttpPut("propertyTypes/{id}/update")]
        public async Task<IActionResult> UpdatePropetyTypes(Prod_PropertyTypeDto PropertyTypeDto, int Id)
        {

            if (await _prodRepo.Prod_PropertyTypeNameExists(PropertyTypeDto.Name, Id))
                return BadRequest(new { Error = "A property with the same name already exists" });

            var prod_PropertyType = await _prodRepo.GetTrackedPropertyType(Id);
            _mapper.Map(PropertyTypeDto, prod_PropertyType);
            if (await _prodRepo.SaveAll())
                return NoContent();

            else return BadRequest(new { Error = "Could not update Property Type" });
        }

        [HttpGet("measurementTypes")]
        public async Task<IActionResult> GetMeasurementTypes([FromQuery]Prod_MeasurementTypeParam prod_MeasurementTypeParam)
        {

            var mTypes = await _prodRepo.GetMeasurementTypes(prod_MeasurementTypeParam);
            var objToreturn = new
            {
                measurementTypes = _mapper.Map<IEnumerable<Prod_MeasurementTypeDto>>(mTypes),
                Pagination = new
                {
                    CurrentPage = mTypes.CurrentPage,
                    PageSize = mTypes.PageSize,
                    TotalCount = mTypes.TotalCount,
                    TotalPages = mTypes.TotalPages
                }
            };
            return Ok(objToreturn);
        }

        [HttpGet("measurementTypes/{id}")]
        public async Task<IActionResult> GetMeasurementTypeById(int id)
        {

            var mTypes = await _prodRepo.GetMeasurementType(id);
            if(null == mTypes)
            return BadRequest();
            var measurementType = _mapper.Map<Prod_MeasurementTypeDto>(mTypes);
            
            return Ok(measurementType);
        }

        [HttpPost("measurementTypes")]
        public async Task<IActionResult> GetMeasurementTypes()
        {
            var Prod_MeasurementTypes = await _prodRepo.GetMeasurementTypes();
            return
            Ok(_mapper.Map<IEnumerable<Prod_MeasurementTypeDto>>(Prod_MeasurementTypes));
        }

        [HttpPut("measurementTypes/{id}/update")]
        public async Task<IActionResult> UpdateMeasurementTypes(Prod_MeasurementTypeDto measurementTypeDto, int Id)
        {

            if (await _prodRepo.Prod_MeasurementTypeNameExists(measurementTypeDto.Name, Id))
                return BadRequest(new { Error = "A measurement type with the same name already exists" });
            var measurermentType = await _prodRepo.GetTrackedMeasurementType(Id);
            _mapper.Map(measurementTypeDto, measurermentType);

            if (await _prodRepo.SaveAll())
                return NoContent();

            else return BadRequest(new { Error = "Could not update Measurement  Type" });
        }

        [HttpPost("measurementTypes/create")]
        public async Task<IActionResult> CreateMeasurementTypes(Prod_MeasurementTypeDto measurementTypeDto)
        {

            if (await _prodRepo.Prod_MeasurementTypeNameExists(measurementTypeDto.Name))
                return BadRequest(new { Error = "A measurement type with the same name already exists" });

            var measurementType = _mapper.Map<Prod_MeasurementType>(measurementTypeDto);
            _prodRepo.Add(measurementType);
            if (await _prodRepo.SaveAll())
                return Ok(_mapper.Map<Prod_MeasurementTypeDto>(measurementType));

            else return BadRequest(new { Error = "Could not Create Measurement Type" });
        }

        [HttpDelete("measurementTypes/{Id}/delete")]
        public async Task<IActionResult> DeleteProd_MeasurementType(int Id)
        {
            if (await _prodRepo.MeasurementTypeHasRecords(Id))
            {
                return BadRequest(new { Error = "This Measurement type has properties depending on it" });
            }
            var measurementTypeToDelete = await _prodRepo.GetMeasurementType(Id);
            _prodRepo.Delete(measurementTypeToDelete);
            var properties = await _prodRepo.GetMeasurementTypeProperties(Id);
            if (properties != null)
                _prodRepo.DeleteAll(properties);
            if (await _prodRepo.SaveAll())
                return Ok();

            return BadRequest(new { Error = "Could not delete  Measurement Type" });
        }

    }
}