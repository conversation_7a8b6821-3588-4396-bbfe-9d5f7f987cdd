using System;
using System.ComponentModel.DataAnnotations;

namespace QuickMessenger.API.Data.Model
{
    public class Photo
    {
        public int Id { get; set; }
        [Required]
        public string Url { get; set; }
        public string PublicId { get; set; }
        public DateTime DateAdded { get; set; }
        public Vendor Vendor { get; set; }
        public int? VendorId { get; set; }
        public Product Product { get; set; }
        public int? ProductId { get; set; }
        public Service Service { get; set; }
        public int? ServiceId { get; set; }
        public int? PickupItemId { get; set; }
        public PickupItem PickupItem { get; set; }

    }
}