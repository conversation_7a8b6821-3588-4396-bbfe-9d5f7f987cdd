using System.Collections.Generic;
using System.Threading.Tasks;
using QuickMessenger.API.Data.Model;

namespace QuickMessenger.API.Data.IRepo
{
    public interface IPhotoRepo
    {
        Task<Photo> GetPhoto(int Id);
        Task<ICollection<Photo>> GetPhotos(List<int> Ids);
         void Delete<T>(T entity) where T: class;
        void DeleteAll<T>(IEnumerable<T> entities) where T:class;
        Task<bool> SaveAll();
    }
}