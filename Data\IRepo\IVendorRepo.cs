using System.Collections.Generic;
using System.Threading.Tasks;
using QuickMessenger.API.Data.DTO.Shared;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Helpers;
using QuickMessenger.API.Helpers.Params;

namespace QuickMessenger.API.Data.IRepo
{
    public interface IVendorRepo
    {
        Task<PagedList<Vendor>> GetVendors(VendorParams vendorParams);
        Task<ICollection<string>> GetDistinctCitiesForParams(ClientVendorParams vendorParams);
         Task<ICollection<Vendor>> GetVendors();
         Task<ICollection<Vendor>> GetTop6endorsWithMostOrders();
        void AddAll<T>(IEnumerable<T> entities) where T: class;
        void Add<T>(T entity) where T: class;
        void Delete<T>(T entity) where T: class;
        void DeleteAll<T>(IEnumerable<T> entities) where T:class;
        Task<bool> SaveAll();
        Task<bool> Save();
        Task<Address> GetVendorAddress(Vendor vendor);
        Task<Photo> GetVendorPhoto(Vendor vendor);
        Task<Vendor> GetVendor(int Id);
        Task<Vendor> GetVendorWithoutRelation(int Id);
        // Task<ICollection<Vendor>> GetVendorsByService(int Id);
        Task<bool> VendorHasRecords(int Id);
        Task<bool> VendorNameAndAddressExists(int Id, string uniqueParam);
        Task<bool> VendorNameAndAddressExists(string uniqueParam);
        Task<Vendor> GetVendorByProductId(int ProductId);
        Task<PagedList<Vendor>> GetVendors(ClientVendorParams param);
         Task<PagedList<Vendor>> GetVendorsByService(int serviceId, ClientVendorParams param);

        Task<ICollection<Vendor>> GetVendors(string Search);

    }
}