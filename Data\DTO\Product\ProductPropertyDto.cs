namespace QuickMessenger.API.Data.DTO.Product
{
    public class ProductPropertyDto
    {
        public int PropertyId { get; set; }
        public int ProductId { get; set; }
        public string Value { get; set; }
         public bool Deleted { get; set; }
        public string  PropertyTypeName { get; set; }
        public string  MeasurementTypeSymbol { get; set; }
        public string  MeasurementTypeName { get; set; }
        public int getValue(){
            int number;
             int.TryParse(Value, out number);
             return number;
        } 
    }
}