namespace QuickMessenger.API.Helpers
{
    public class AuthMessageSenderOptions
    {
        public string SendGridKey { get; set; }
        public string SpaStaffUrl {get; set;}
        public string SendGridKey2 { get; set; }
        public SmtpSetting  SmtpSetting { get; set; }
        public EmailSetting EmailSetting { get; set; }
        public ZohoSetting ZohoSetting { get; set; }

        public AuthMessageSenderOptions2 AwsSES { get; set; }

    }

   public  class AuthMessageSenderOptions2
    {
        public string Key_Id { get; set; }
        public string Secrete_Key { get; set; }

    }

    public class SmtpSetting{
        public string Server { get; set; }
        public string Port { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
    }

    public class EmailSetting
    {
        public string Server { get; set; }
        public string Port { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public string FromEmail { get; set; }
        public string AccountId { get; set; }
    }

    public class ZohoSetting
    {
        public string ClientId { get; set; }
        public string ClientSecret { get; set; }
        public string RedirectUrl { get; set; }
        public string RefreshToken { get; set; }
        public string AccessToken { get; set; }
        public string TokenUrl { get; set; }
        public string AuthUrl { get; set; }
        public string AccountId { get; set; }
    }
}