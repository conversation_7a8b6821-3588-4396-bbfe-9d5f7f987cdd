using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Net.Http;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Google.Apis.Auth;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using QuickMessenger.API.Data.DTO.Shared;
using QuickMessenger.API.Data.IService;
using QuickMessenger.API.Data.Model;

namespace QuickMessenger.API.Data.Services
{
    public class AuthService : IAuthService
    {
        private readonly UserManager<User> _userManager;
        private readonly IConfiguration _config;

        public AuthService(UserManager<User> _userManager, IConfiguration _config)
        {
            this._config = _config;
            this._userManager = _userManager;
        }

        public string GenerateTokenHandler(User User, bool socialMedia)
        {
            //This method to generate user token 
            var role = GetRole(User);
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, User.Id.ToString()),
                new Claim(ClaimTypes.Name, User.UserName),
                new Claim(ClaimTypes.Role, role.Name),
                new Claim("user_type", role.Name == "Client" ? role.Name : "Staff"),
                new Claim("loginType",socialMedia? "social" : "username")
            };

            var key = new SymmetricSecurityKey(Encoding.UTF8
            .GetBytes(_config.GetSection("AppSettings:Token").Value));

            var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha512Signature);
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.Now.AddYears(6),
                SigningCredentials = credentials
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        public Role GetRole(User user)
        {
            return new Role{Name = user.GetType().Name};
        }
   
        public async Task<GoogleJsonWebSignature.Payload> ValidateGoogleToken(string googleTokenId)
        {
            GoogleJsonWebSignature.ValidationSettings settings = new GoogleJsonWebSignature.ValidationSettings();
            settings.Audience = new List<string>() { "781730229447-9qrvoghjk7b0j1rmms25nufffi36paeb.apps.googleusercontent.com" };
            GoogleJsonWebSignature.Payload payload = await GoogleJsonWebSignature.ValidateAsync(googleTokenId, settings);
            return payload;
        }

        public async Task<FacebookUserDto> VerifyFacebookAccessToken(string accessToken)
        {
            //var userInfoResponse = await Client.GetStringAsync($"https://graph.facebook.com/v2.8/me?fields=id,email,first_name,last_name,name,gender,locale,birthday,picture&access_token={model.AccessToken}");
            FacebookUserDto fbUser = null;
            var path = "https://graph.facebook.com/me?fields=id,email,first_name,last_name,name,gender,locale,birthday,picture&access_token=" + accessToken;
            var client = new HttpClient();
            var uri = new Uri(path);     
            var response = await client.GetAsync(uri);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                fbUser = Newtonsoft.Json.JsonConvert.DeserializeObject<FacebookUserDto> (content);
            }

            return fbUser;
        }


        public async Task<JwtSecurityToken> ValidateAppleIdentityTokenAsync(string identityToken)
        {
            // Fetch Apple's public keys. You should cache these keys and refresh them periodically.
            var appleKeys = await GetApplePublicKeysAsync();

            var tokenHandler = new JwtSecurityTokenHandler();
            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKeys = appleKeys,
                ValidateIssuer = true,
                ValidIssuer = "https://appleid.apple.com",
                ValidateAudience = true,
                ValidAudience = "com.quickwaka.ios", // Your Apple Services ID
                ValidateLifetime = true
            };

            // Validate token
            SecurityToken validatedToken;
            tokenHandler.ValidateToken(identityToken, validationParameters, out validatedToken);

            return validatedToken as JwtSecurityToken;
        }

    private async Task<IEnumerable<SecurityKey>> GetApplePublicKeysAsync()
    {
        var client = new HttpClient();
         var response = await client.GetAsync("https://appleid.apple.com/auth/keys");
        response.EnsureSuccessStatusCode();
        var jsonResponse = await response.Content.ReadAsStringAsync();

        using (var doc = JsonDocument.Parse(jsonResponse))
        {
            var keys = new List<SecurityKey>();
            foreach (var element in doc.RootElement.GetProperty("keys").EnumerateArray())
            {
                var k = new JsonWebKey
                {
                    Kty = element.GetProperty("kty").GetString(),
                    Kid = element.GetProperty("kid").GetString(),
                    Use = element.GetProperty("use").GetString(),
                    E = element.GetProperty("e").GetString(),
                    N = element.GetProperty("n").GetString(),
                    Alg = element.GetProperty("alg").GetString()
                };

                keys.Add(k);
            }

            return keys;
    }

    }
    }
}