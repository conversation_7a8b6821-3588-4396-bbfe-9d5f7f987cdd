using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace QuickMessenger.API.Data.DTO.Client
{
    public class CartForCreateDto
    {
        public int Id { get; set; }
        public CartForCreateDto()
        {
            Date = DateTime.Now;
        }
        [Required]
        public ICollection<ServiceOrdersForCreateDto> Orders { get; set; }
        public DateTime Date { get; set; }
        public int ClientId { get; set; }
    }

        public class CartForCreateDtoLazy
        {
            public int Id { get; set; }
            public CartForCreateDtoLazy()
            {
                Date = DateTime.Now;
            }
            public ICollection<ServiceOrdersForCreateDto> Orders { get; set; }
            public DateTime Date { get; set; }
            public int ClientId { get; set; }
        }


    public class CartForViewDto
    {
        public int Id { get; set; }
        public string TrackingId {get;set;}
        public CartForViewDto()
        {
            Date = DateTime.Now;
        }
        public ICollection<ServiceOrdersForViewDto> Orders { get; set; }
        public DateTime Date { get; set; }
        public int ClientId { get; set; }
        public double Cost { get; set; }
        public string State { get; set; }
    }

    public class CartForViewDto2
    {
        public int Id { get; set; }
        public string TrackingId {get;set;}
        public CartForViewDto2()
        {
            Date = DateTime.Now;
        }
        public ICollection<ServiceOrdersForViewDto2> Orders { get; set; }
        public DateTime Date { get; set; }
        public int ClientId { get; set; }
        public double Cost { get; set; }
    }


    public class CartListViewForRider{
        public string Lastname{get; set;} 
        public string FirstName { get; set; }
        public DateTime Date { get; set; }
        public int  Completed { get; set; }
        public int OutOf { get; set; }
        public int ClientId { get; set; }
        public int CartId { get; set; }
        // public ICollection<ServiceOrdersForViewDto2> Orders { get; set; }
        public string photoUrl { get; set; }
    }

     public class CartListViewForRider2{
        public string Lastname{get; set;} 
        public string FirstName { get; set; }
        public DateTime Date { get; set; }
        public int  Completed { get; set; }
        public int OutOf { get; set; }
        public int ClientId { get; set; }
        public int CartId { get; set; }
        public string State { get; set; }
        // public ICollection<ServiceOrdersForViewDto2> Orders { get; set; }
        public string photoUrl { get; set; }

        public ICollection<VendorOrderForRider> Vendors { get; set; }

        public ICollection<PickupItemDto> Pickups { get; set; }
        
    }

   
}