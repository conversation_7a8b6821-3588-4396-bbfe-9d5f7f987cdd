using System.ComponentModel.DataAnnotations;
namespace QuickMessenger.API.Data.Model
{
    public class ProductOrder
    {
        [Required]
        public int ProductId { get; set; }
        public Product Product { get; set; }   
        [Required] 
        public int OrderId { get; set; }
        public Order Order { get; set; }
        public int Quantity { get; set; }
        public double ProductPrice{get;set;}
         // state of the product Order is the state seen by vendors, riders and admins.
        //They can be 'Pending','Ready', 'Picked','Delivered'
        public string  State { get; set; }

    }
}