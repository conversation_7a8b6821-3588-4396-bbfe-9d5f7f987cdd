using System.ComponentModel.DataAnnotations;

namespace QuickMessenger.API.Data.Model
{
    public class ProductProperty
    {
        [Required]
        public int ProductId { get; set; }
        public Product Product { get; set; }
        [Required]
        public int PropertyId { get; set; }
        public Property Property { get; set; }
        //Nvalue is numeric value
        public int? NValue { get; set; }
        public string  Value { get; set; }
        
    }
}