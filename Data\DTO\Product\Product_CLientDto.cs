
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using QuickMessenger.API.Data.DTO.Shared;

namespace QuickMessenger.API.Data.DTO.Product{

    public class Product_ClientDto{
        [Required]
        public string Name { get; set; }
        [Required]
        [Range(1, double.MaxValue, ErrorMessage = "Enter an amount greater than 0.00" )]
        public double Price { get; set; }
        [Required]
        public string Description { get; set; }
        [Required]
        public bool OutOfStock { get; set; }
        public ICollection<PhotoDto> Photos { get; set; }
    }
}
