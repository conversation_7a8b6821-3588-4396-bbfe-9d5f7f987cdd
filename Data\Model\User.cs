using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Identity;

namespace QuickMessenger.API.Data.Model
{
    public  class User:  IdentityUser<int>
    {
        
        [Required]
        public string  LastName { get; set; }
        [Required]
        public string FirstName { get; set; }
        
        public string Gender { get; set; }
        public virtual ICollection<Address> Addresses {get;set;}
        public int DefaultAddressId { get; set; }
        [Required]
        public string SearchParam { get; set; }
        public string PhotoUrl { get; set; }
        [Required]
        public bool Deactivated { get; set; }
        public ICollection<Order> Orders { get; set; }
        public ICollection<Cart> Carts { get; set; }
        public DateTime DateRegistered { get; set; }
        public DateTime LastActive { get; set; }
        public int? VendorId { get; set; }
        //An vendor this user is an admin of
        public Vendor Vendor { get; set; }
        public string FCMToken { get; set; }
        public bool Deleted { get; set; }
    }
}