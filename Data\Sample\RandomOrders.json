[{"ClientId": 5, "AddressId": 36, "RiderId": 203, "State": "Pending", "Date": "2020-02-27", "Cost": 500.0, "PickupItems": [{"AddressId": 3, "Name": "Urbanshee", "Size": "Large", "Fragile": false, "PictureUrl": "https://picsum.photos/id/196/500/300"}, {"AddressId": 3, "Name": "Voratak", "Size": "Medium", "Fragile": true, "PictureUrl": "https://picsum.photos/id/257/500/300"}, {"AddressId": 3, "Name": "<PERSON><PERSON>", "Size": "Small", "Fragile": true, "PictureUrl": "https://picsum.photos/id/264/500/300"}], "Type": "Pickup", "ServiceId": 1}, {"Cost": 500.0, "ClientId": 4, "AddressId": 8, "RiderId": 203, "State": "Pending", "Date": "2020-02-12", "PickupItems": [{"AddressId": 3, "Name": "Re<PERSON><PERSON>ys", "Size": "Large", "Fragile": true, "PictureUrl": "https://picsum.photos/id/161/500/300"}, {"AddressId": 3, "Name": "Norsup", "Size": "Medium", "Fragile": true, "PictureUrl": "https://picsum.photos/id/142/500/300"}, {"AddressId": 3, "Name": "<PERSON><PERSON><PERSON>", "Size": "Small", "Fragile": true, "PictureUrl": "https://picsum.photos/id/110/500/300"}], "Type": "Pickup", "ServiceId": 2}, {"Cost": 500.0, "ClientId": 15, "AddressId": 2, "RiderId": 203, "State": "Pending", "Date": "2020-03-24", "PickupItems": [{"AddressId": 2, "Name": "Plutorque", "Size": "Large", "Fragile": true, "PictureUrl": "https://picsum.photos/id/161/500/300"}, {"AddressId": 2, "Name": "Makingway", "Size": "Medium", "Fragile": true, "PictureUrl": "https://picsum.photos/id/145/500/300"}, {"AddressId": 2, "Name": "Undertap", "Size": "Small", "Fragile": false, "PictureUrl": "https://picsum.photos/id/208/500/300"}], "Type": "Pickup", "ServiceId": 3}, {"Cost": 500.0, "ClientId": 4, "AddressId": 15, "RiderId": 203, "State": "Pending", "Date": "2020-03-24", "PickupItems": [{"AddressId": 2, "Name": "Entroflex", "Size": "Large", "Fragile": false, "PictureUrl": "https://picsum.photos/id/229/500/300"}, {"AddressId": 2, "Name": "Zosis", "Size": "Medium", "Fragile": false, "PictureUrl": "https://picsum.photos/id/141/500/300"}, {"AddressId": 2, "Name": "<PERSON><PERSON>", "Size": "Small", "Fragile": true, "PictureUrl": "https://picsum.photos/id/239/500/300"}], "Type": "Pickup", "ServiceId": 4}, {"Cost": 500.0, "ClientId": 16, "AddressId": 27, "RiderId": 203, "State": "Pending", "Date": "2020-03-22", "PickupItems": [{"AddressId": 2, "Name": "Metroz", "Size": "Large", "Fragile": false, "PictureUrl": "https://picsum.photos/id/110/500/300"}, {"AddressId": 2, "Name": "Freakin", "Size": "Medium", "Fragile": false, "PictureUrl": "https://picsum.photos/id/253/500/300"}, {"AddressId": 2, "Name": "Digitalus", "Size": "Small", "Fragile": true, "PictureUrl": "https://picsum.photos/id/227/500/300"}], "Type": "Pickup", "ServiceId": 5}, {"Cost": 500.0, "ClientId": 3, "AddressId": 18, "RiderId": 203, "State": "Pending", "Date": "2019-09-04", "ProductOders": [{"ProductId": 2, "Quantity": 1}, {"ProductId": 4, "Quantity": 5}], "Type": "Purchase", "ServiceId": 6}, {"Cost": 500.0, "ClientId": 12, "AddressId": 20, "RiderId": 203, "State": "Pending", "Date": "2020-03-11", "ProductOders": [{"ProductId": 11, "Quantity": 1}, {"ProductId": 3, "Quantity": 3}], "Type": "Purchase", "ServiceId": 5}, {"Cost": 500.0, "ClientId": 1, "AddressId": 35, "RiderId": 203, "State": "Pending", "Date": "2020-03-10", "ProductOders": [{"ProductId": 11, "Quantity": 5}, {"ProductId": 3, "Quantity": 3}], "Type": "Purchase", "ServiceId": 4}, {"Cost": 500.0, "ClientId": 1, "AddressId": 25, "RiderId": 203, "State": "Pending", "Date": "2020-03-13", "ProductOders": [{"ProductId": 8, "Quantity": 4}, {"ProductId": 7, "Quantity": 2}], "Type": "Purchase", "ServiceId": 3}, {"Cost": 500.0, "ClientId": 11, "AddressId": 34, "RiderId": 203, "State": "Pending", "Date": "2020-03-12", "ProductOders": [{"ProductId": 2, "Quantity": 2}, {"ProductId": 4, "Quantity": 4}], "Type": "Purchase", "ServiceId": 2}, {"Cost": 500.0, "ClientId": 11, "AddressId": 40, "RiderId": 203, "State": "Pending", "Date": "2020-03-23", "ProductOders": [{"ProductId": 6, "Quantity": 2}, {"ProductId": 9, "Quantity": 3}], "Type": "Purchase", "ServiceId": 1}, {"ClientId": 13, "AddressId": 2, "RiderId": 203, "State": "Pending", "Date": "2018-06-09", "ProductOders": [{"ProductId": 2, "Quantity": 5}, {"ProductId": 6, "Quantity": 4}], "Type": "Purchase", "ServiceId": 2}, {"Cost": 500.0, "ClientId": 13, "AddressId": 27, "RiderId": 203, "State": "Pending", "Date": "2019-06-14", "ProductOders": [{"ProductId": 9, "Quantity": 1}, {"ProductId": 8, "Quantity": 4}], "Type": "Purchase", "ServiceId": 3}, {"Cost": 500.0, "ClientId": 4, "AddressId": 35, "RiderId": 203, "State": "Pending", "Date": "2018-12-14", "ProductOders": [{"ProductId": 5, "Quantity": 2}, {"ProductId": 4, "Quantity": 1}], "Type": "Purchase", "ServiceId": 4}, {"Cost": 500, "ClientId": 11, "AddressId": 50, "RiderId": 203, "State": "Pending", "Date": "2018-03-19", "ProductOders": [{"ProductId": 3, "Quantity": 3}, {"ProductId": 5, "Quantity": 5}], "Type": "Purchase", "ServiceId": 5}, {"Cost": 500.0, "ClientId": 8, "AddressId": 31, "RiderId": 203, "State": "Pending", "Date": "2018-12-31", "PickupItems": [{"AddressId": 1, "Name": "Bolax", "Size": "Large", "Fragile": true, "PictureUrl": "https://picsum.photos/id/212/500/300"}, {"AddressId": 1, "Name": "<PERSON><PERSON>", "Size": "Medium", "Fragile": true, "PictureUrl": "https://picsum.photos/id/129/500/300"}, {"AddressId": 1, "Name": "Pathways", "Size": "Small", "Fragile": true, "PictureUrl": "https://picsum.photos/id/260/500/300"}], "Type": "Pickup", "ServiceId": 6}, {"Cost": 500.0, "ClientId": 13, "AddressId": 19, "RiderId": 203, "State": "Pending", "Date": "2018-08-14", "PickupItems": [{"AddressId": 1, "Name": "Tourmania", "Size": "Large", "Fragile": false, "PictureUrl": "https://picsum.photos/id/237/500/300"}, {"AddressId": 1, "Name": "Zolar", "Size": "Medium", "Fragile": false, "PictureUrl": "https://picsum.photos/id/257/500/300"}, {"AddressId": 1, "Name": "D<PERSON><PERSON>", "Size": "Small", "Fragile": true, "PictureUrl": "https://picsum.photos/id/175/500/300"}], "Type": "Pickup", "ServiceId": 5}]