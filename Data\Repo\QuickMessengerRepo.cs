using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using QuickMessenger.API.Data.Model;
using System.Linq;

namespace QuickMessenger.API.Data.Repo
{
    public class QuickMessengerRepo : Data.IRepo.IQuickMessengerRepo
    {
        private readonly DataContext _context;
        public QuickMessengerRepo(DataContext context)
        {
            _context = context;
        }
        public async Task<List<Admin>> GetAdmins()
        {
             return await _context.Admins.AsQueryable().ToListAsync();
        }

        public async Task<List<Client>> GetClients()
        {
            return await _context.Clients.AsQueryable().ToListAsync();
        }

        public async Task<List<Rider>> GetRiders()
        {
            return await _context.Riders.AsQueryable().ToListAsync();
        }

        public async Task<bool> SaveAll()
        {
           return await _context.SaveChangesAsync() > 0;
        }
    }
}