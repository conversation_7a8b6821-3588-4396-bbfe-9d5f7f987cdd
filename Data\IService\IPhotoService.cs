using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using QuickMessenger.API.Data.Model;

namespace QuickMessenger.API.Data.IService
{
    public interface IPhotoService
    {
        Task<string> AddPhoto(IFormFile file, string path);
        Task<bool> DeletePhoto(string Url);
         int  GetNextIndex(int max, ICollection<Photo> existingPhotos);
    }
}