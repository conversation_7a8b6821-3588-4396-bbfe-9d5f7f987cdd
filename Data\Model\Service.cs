using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace QuickMessenger.API.Data.Model
{
    public class Service
    
    {
        public int Id {get;set;}
        public ICollection<Product> Products {get;set;}
        public string Name {get;set;} 
        public string Description {get;set;}
        public ICollection<Photo> Photos { get; set; }
        public string NameId { get; set; }
        [NotMapped]
        public bool CanDelete { get; set; }
        public string SearchParam { get; set; }
        public ICollection<Order> Orders { get; set; }
        //Generic Services are services that all products belong to
        public bool Generic { get; set; }
        public bool PickupAllowed { get; set; }
        public bool PurchaseAllowed { get; set; }
        public bool IsDefaultGeneric { get; set; }
        public double ServiceCharge { get; set; }
    }
}