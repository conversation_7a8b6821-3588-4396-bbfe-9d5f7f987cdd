using System.ComponentModel.DataAnnotations;
using QuickMessenger.API.Data.DTO.Shared;

namespace QuickMessenger.API.Data.DTO.Vendor
{
    public class VendorListDto
    {  
        public int Id { get; set; }
        [Required]
        public string Name { get; set; }
        [Required]
        public AddressDto Address { get; set; }
        public PhotoDto Logo { get; set; }
        [Required]
        public string Email { get; set; }
        [Required]
        public string Phone { get; set; }
        public string Phone2 { get; set; }
        public string State { get; set; }

    }


    public class VendorListDtoLite{
         public int Id { get; set; }
        [Required]
        public string Name { get; set; }
        public string ImageUrl { get; set; }
        public string Address { get; set; }
        public string State { get; set; }
    }
}