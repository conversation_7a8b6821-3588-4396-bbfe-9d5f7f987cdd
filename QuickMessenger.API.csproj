<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <AspNetCoreHostingModel>InProcess</AspNetCoreHostingModel>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Razor.Design" Version="2.2.0" PrivateAssets="All"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="3.1.0"/>
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="8.1.0"/>
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="3.1"/>
    <PackageReference Include="SendGrid" Version="9.11.0"/>
    <PackageReference Include="CloudinaryDotNet" Version="1.8.0"/>
    <PackageReference Include="Google.Apis.Auth.AspNetCore3" Version="1.49.0"/>
    <PackageReference Include="FluentValidation.AspNetCore" Version="8.5.1"/>
    <PackageReference Include="AspNet.Security.OAuth.Validation" Version="2.0.0"/>
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="3.1.0"/>
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="3.1.0"/>
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="3.1"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="3.1"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="3.1"/>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="3.1.0"/>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.0"/>
    <PackageReference Include="Ardalis.EFCore.Extensions" Version="1.1.0"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="3.1.0"/>
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0"/>
    <PackageReference Include="System.Linq.Async" Version="4.1.1"/>
    <PackageReference Include="FirebaseAdmin" Version="2.4.0"/>
    <PackageReference Include="AWSSDK.SimpleEmail" Version="3.7.200.49"/>
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.0.3"/>
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="7.0.3"/>
    <PackageReference Include="MimeKit" Version="4.12.0"/>
    <PackageReference Include="MailKit" Version="4.12.0"/>
  </ItemGroup>
  <ItemGroup>
    <Content Include="EmailTemplates\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
</Project>