﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QuickMessenger.API.Migrations
{
    public partial class VendorID_to_USer_Table : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "VendorId",
                table: "Users",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Users_VendorId",
                table: "Users",
                column: "VendorId");

            migrationBuilder.AddForeignKey(
                name: "FK_Users_Vendors_VendorId",
                table: "Users",
                column: "VendorId",
                principalTable: "Vendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Users_Vendors_VendorId",
                table: "Users");

            migrationBuilder.DropIndex(
                name: "IX_Users_VendorId",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "VendorId",
                table: "Users");
        }
    }
}
