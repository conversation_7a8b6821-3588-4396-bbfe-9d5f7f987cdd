using System.ComponentModel.DataAnnotations;
using QuickMessenger.API.Data.DTO.Shared;
using QuickMessenger.API.Helpers;

namespace QuickMessenger.API.Data.DTO.Vendor
{
    public class VendorDto
    {  
        public int Id { get; set; }
        [Required]
        public string Name { get; set; }
        public string Description { get; set; }
        [Required]
        public AddressDto Address { get; set; }
        public bool CanDelete { get; set; }
        public PhotoDto Logo { get; set; }
        [Required]
        public string Email { get; set; }
        [Required]
        public string Phone { get; set; }
        public string Phone2 { get; set; }

        public bool Deactivated { get; set; }
        public string State { get; set; }

    }

    public class VendorDtoLite
    {  
        public int Id { get; set; }
        [Required]
        public string Name { get; set; }
        [Required]
        public string Address { get; set; }
        public string ImageUrl { get; set; }
        public string State { get; set; }

    }

    public class VendorUpdateForClientDto{
        [Required]
        public int Id { get; set; }
        [ValidStateValues]
        [Required]
        public string State { get; set; }
    }

    public class VendorStateUpdateDto{
        [ValidStateValues]
        [Required]
        public string State { get; set; }
    }
}