using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using QuickMessenger.API.Data.DTO.Product;
using QuickMessenger.API.Data.DTO.Shared;
using QuickMessenger.API.Data.Model;

namespace QuickMessenger.API.Data.DTO.Client
{
    public class ServiceOrdersForCreateDto
    {
        public int Id { get; set; }
        [Required]
        public int ClientId { get; set; }
        public int AddressId {get; set;}
        public DateTime Date { get; set; }
        public DateTime PickupTime { get; set; }
        public ICollection<PickupItemDto2> PickupItems { get; set; }
        [Required]
        public string Type { get; set; }
        public ICollection<ProductOrderDto2> ProductOrders { get; set; }
        public double Cost { get; set; }
        public double DeliveryCharge { get; set; }
        [Required]
        public int ServiceId { get; set; }
        
    }
    public class ServiceOrdersForCreateDtoLazy
    {
        public int Id { get; set; }
        [Required]
        public int ClientId { get; set; }
        public int AddressId {get; set;}
        public DateTime Date { get; set; }
        public DateTime PickupTime { get; set; }
        [Required]
        public string Type { get; set; }
        public double Cost { get; set; }
        public double DeliveryCharge { get; set; }
        [Required]
        public int ServiceId { get; set; }
        
    }

    public class ServiceOrdersForViewDto
    {
        public int Id { get; set; }
        public string TrackingId { get; set; }
        public ServiceOrdersForViewDto()
        {
            Date = DateTime.Now;
        }
        [Required]
        public int ClientId { get; set; }
        public int AddressId {get; set;}
        public AddressDto2 Address { get; set; }
        //public int MyProperty { get; set; }
        public DateTime Date { get; set; }
        public DateTime PickupTime { get; set; }
        public ICollection<PickupItemDto2> PickupItems { get; set; }
        [Required]
        public string Type { get; set; }
        public ICollection<ProductOrderDto> ProductOrders { get; set; }
        public double Cost { get; set; }
        public double Subtotal { get; set; }
        public string State { get; set; }
        public double DeliveryCharge { get; set; }
        [Required]
        public int ServiceId { get; set; }
        public double ServiceCharge { get; set; }
        public ServiceLiteDto2 Service { get; set; }
        
    }

    public class ServiceOrdersForViewDto2
    {
        public int Id { get; set; }
        public string TrackingId { get; set; }
        public ServiceOrdersForViewDto2()
        {
            Date = DateTime.Now;
        }
        [Required]
        public int ClientId { get; set; }
        public int AddressId {get; set;}
        public AddressDto2 Address { get; set; }
        //public int MyProperty { get; set; }
        public DateTime Date { get; set; }
        public DateTime TimeDelivered { get; set; }
        public DateTime PickupTime { get; set; }
        public ICollection<PickupItemViewDto> PickupItems { get; set; }
        [Required]
        public string Type { get; set; }
        public ICollection<ProductOrderDto> ProductOrders { get; set; }
        public double Cost { get; set; }
        public string State { get; set; }
        public double DeliveryCharge { get; set; }
        [Required]
        public int ServiceId { get; set; }
        public ServiceLiteDto2 Service { get; set; }
        public string CartTrackingId { get; set; }
        
    }

    public class ServiceOrderViewForRider{
        public int AddressId {get; set;}
        public AddressDto2 Address { get; set; }
        public DateTime Date { get; set; }
        public DateTime TimeDelivered { get; set; }
        public DateTime PickupTime { get; set; }
        public ICollection<PickupItemViewDto> PickupItems { get; set; }
        public string Type { get; set; }
        public ICollection<ProductOrderDto> ProductOrders { get; set; }
    }
}