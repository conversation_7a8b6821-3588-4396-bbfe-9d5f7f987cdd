using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using QuickMessenger.API.Data.DTO;
using QuickMessenger.API.Data.DTO.Product;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Helpers;

namespace QuickMessenger.API.Data.Sample
{
    public class Seed
    {
        private readonly UserManager<User> _userManager;
        private readonly IMapper _mapper;
        private readonly IProductRepo _prodRepo;
        private readonly DataContext _dataContext;

        public Seed(UserManager<User> userManager, IMapper mapper, IProductRepo prodRepo, DataContext dataContext)
        {
            _dataContext = dataContext;
            _prodRepo = prodRepo;
            _mapper = mapper;
            _userManager = userManager;
        }

        public async Task SeedUsers()
        {
            var userData = System.IO.File.ReadAllText("Data/Sample/RandomClientData.json");
          //  var ClientData = System.IO.File.ReadAllText("Data/Sample/RandomClientData.json");
            var Clients = JsonConvert.DeserializeObject<List<Client>>(userData);
            if (!await _userManager.Users.AnyAsync())
            {
                foreach (var client in Clients)
                {
                    client.DateRegistered = DateTime.Now;
                    client.LastActive  = DateTime.Now;
                    client.Email = client.Email.ToLower();
                    client.UserName = client.Email;
                    client.SearchParam = (client.LastName + client.FirstName + client.Email + client.PhoneNumber + "Client").ToUpper();
                    await _userManager.CreateAsync(client, "password");
                }

                userData = System.IO.File.ReadAllText("Data/Sample/RandomSuperAdmin.json");
                var Admins = JsonConvert.DeserializeObject<List<SuperAdmin>>(userData);
                foreach (var admin in Admins)
                {
                    admin.UserName = admin.Email;
                    admin.SearchParam = $"{admin.LastName} {admin.FirstName} {admin.Email} {admin.PhoneNumber} Admin".ToUpper();
                    await _userManager.CreateAsync(admin, "password");
                }

                userData = System.IO.File.ReadAllText("Data/Sample/RandomStaffData.json");

                var Staff = JsonConvert.DeserializeObject<List<SampleStaff>>(userData);

                //create Sample staff
                foreach (var staff in Staff)
                {
                    User user;
                    switch (staff.Role)
                    {
                        case "Admin":
                            user = _mapper.Map<Admin>(staff);
                            break;
                        case "FrontDesk":
                            user = _mapper.Map<FrontDesk>(staff);
                            break;
                        default:
                            user = _mapper.Map<Rider>(staff);
                            break;
                    }
                    user.SearchParam = $"{user.LastName} {user.FirstName} {user.Email} {staff.Role} {user.PhoneNumber}".ToUpper();
                    user.UserName = staff.Email;
                    user.PhoneNumber = staff.Phone;
                    user.EmailConfirmed = true;
                    await _userManager.CreateAsync(user, "password");
                }
               
            }

            if (!await _dataContext.Prod_Categories.AsQueryable().AnyAsync())
            {
              var cateGoryData = System.IO.File.ReadAllText("Data/Sample/RandomCategories.json");
              var CategoriesDto = JsonConvert.DeserializeObject<ICollection<Prod_Category>>(cateGoryData);   
              //var Categories = _mapper.Map<ICollection<Prod_Category>>(CategoriesDto);
              foreach(Prod_Category category in CategoriesDto){
                  if(category.SubCategories.Count > 0){
                      foreach(Prod_Category sub in category.SubCategories){
                          sub.SearchParam = $"{sub.Name} {sub.NameId} {sub.Description} ".ToUpper();
                           if(sub.SubCategories.Count > 0)
                           { 
                               foreach(Prod_Category sub2 in sub.SubCategories)
                               {
                                   sub2.SearchParam = $"{sub2.Name} {sub2.NameId} {sub2.Description} ".ToUpper();
                                   if(sub2.SubCategories.Count > 0)
                                   {
                                       foreach(Prod_Category sub3 in sub2.SubCategories){
                                           sub3.SearchParam = $"{sub3.Name} {sub3.NameId} {sub3.Description} ".ToUpper(); 
                                       }
                                   }
                               }
                           }
                      }
                      
                  }
                  
                  _prodRepo.AddCategory(category);
                  await _prodRepo.SaveAll();
              }
              
            }

            if (! await _dataContext.Prod_PropertyTypes.AsQueryable().AnyAsync())
            {
              var propertyTypeData = System.IO.File.ReadAllText("Data/Sample/RandomPropertyTypes.json");
              var PropertyTypesDto = JsonConvert.DeserializeObject<List<Prod_PropertyTypeDto>>(propertyTypeData); 
              var PropertyTypes = _mapper.Map<List<Prod_PropertyType>>(PropertyTypesDto);
              foreach(Prod_PropertyType propertyType in PropertyTypes ){
                    _prodRepo.Add(propertyType);
              }
               await _prodRepo.SaveAll();
            }

            if (! await _dataContext.Prod_MeasurementTypes.AsQueryable().AnyAsync())
            {
              var measurementTypeData = System.IO.File.ReadAllText("Data/Sample/RandomMeasurementTypes.json");
              var Prod_MeasurementTypes = JsonConvert.DeserializeObject<IEnumerable<Prod_MeasurementTypeDto>>(measurementTypeData); 
              var prod_meas = _mapper.Map<IEnumerable<Prod_MeasurementType>>(Prod_MeasurementTypes);
              foreach(Prod_MeasurementType propertyType in prod_meas ){
                    _prodRepo.Add(propertyType);
              }
               await _prodRepo.SaveAll();
            }

            if(! await _dataContext.Vendors.AsQueryable().AnyAsync())
            {
                var vendorsText = System.IO.File.ReadAllText("Data/Sample/RandomVendors.json");
                var Vendors = JsonConvert.DeserializeObject<ICollection<Vendor>>(vendorsText); 
               // var Vendors = _mapper.Map<ICollection<Vendor>>(VendorsDto);
                foreach(Vendor vendor in Vendors){
                 vendor.SearchParam =   $"{vendor.Name} {vendor.Email} {vendor.Phone} {vendor.Phone2} {vendor.Address.Street} {vendor.Address.City} {vendor.Address.State}".ToUpper();
                 vendor.UniqueParam = $"{vendor.Name}{vendor.Address.Country}{vendor.Address.State}{vendor.Address.City}{vendor.Address.Street}".ToUpper().RemoveSpacesAndSpecialCharatcters();
                        _dataContext.Add(vendor);
                }
                await _dataContext.SaveChangesAsync();
            }
           if (! await _dataContext.Services.AsQueryable().AnyAsync())
           {
               var servicesText = System.IO.File.ReadAllText("Data/Sample/RandomServices.json");
               var varServcies = JsonConvert.DeserializeObject<ICollection<Service>>(servicesText);
               foreach(Service service in varServcies){
                   service.SearchParam = $"{service.Name} {service.NameId}".RemoveSpecialCharatcters().ToUpper().Trim();
                   service.NameId = service.Name.GenerateValidNameId();
                   _dataContext.Add(service);
               }

               await _dataContext.SaveChangesAsync();
           }
            if(! await _dataContext.Products.AsQueryable().AnyAsync())
            {
                var productsText = System.IO.File.ReadAllText("Data/Sample/RandomProducts.json");
                var Products = JsonConvert.DeserializeObject<ICollection<Product>>(productsText); 
               // var Vendors = _mapper.Map<ICollection<Vendor>>(VendorsDto);
               int count = 0;
                foreach(Product product in Products)
                {
                 var Category = await _prodRepo.GetCategory(product.Prod_CategoryId);
                 Prod_Category CategoryParent = new Prod_Category();
                 if(null != Category.Parent)
                 CategoryParent = Category.Parent;
                 product.SearchParam = $"{product.Name}".RemoveSpecialCharatcters().ToUpper();
                 product.UniqueParam = $"{product.Name.RemoveSpecialCharatcters().ToUpper()}";
                 //Model.Service service= await _prodRepo.GetService(new Random().Next(1,6));
                product.ServiceId = new Random().Next(1,6);
                _dataContext.Add(product);
                if(++count > 10)
                break;
                }
                await _dataContext.SaveChangesAsync();
            }

            if(! await _dataContext.Carts.AsQueryable().AnyAsync())
            {    
                 var CartText = System.IO.File.ReadAllText("Data/Sample/RandomCarts.json");
                var cartsSample = JsonConvert.DeserializeObject<List<Cart>>(CartText); 
                foreach(Cart cart in cartsSample)
                {
                    _dataContext.Add(cart);
                }
                await _dataContext.SaveChangesAsync();
            }

            if(! await _dataContext.Orders.AsQueryable().AnyAsync())
            {
                 var OrderText = System.IO.File.ReadAllText("Data/Sample/RandomOrders.json");
                var ordersSample = JsonConvert.DeserializeObject<List<SampleOrder>>(OrderText); 
                var orders = _mapper.Map<List<Order>>(ordersSample);
                var Rider = await _dataContext.Riders.AsQueryable().FirstOrDefaultAsync();
                foreach(Order od in orders)
                {
                    od.RiderId = Rider.Id;
                     od.CartId = new Random().Next(1,7);
                    User client = await _dataContext.Carts.AsQueryable().Where(c => c.Id == od.CartId).Select(c => c.Client).FirstOrDefaultAsync();
                    var service1 = await _dataContext.Services.AsQueryable().Where(a => a.Id == od.ServiceId).FirstOrDefaultAsync();
                    Address Address = await _dataContext.Addresses.AsQueryable().Where( a => a.Id == od.ClientId).FirstOrDefaultAsync();  
                    
                    od.SearchParam =   $"{client.FirstName} {client.LastName} {client.PhoneNumber} {Address.Street} {Address.City} {Address.State} {service1.Name} {od.TrackingId}";
                    var PickupItems = od.PickupItems;
                    if(null != od.PickupItems)
                    foreach(PickupItem pu in od.PickupItems){  
                        od.SearchParam = od.SearchParam + $" {pu.Name}";
                    }

                    //od.PickupItems = null;

                    if(null != od.ProductOrders)
                    foreach(ProductOrder pu in od.ProductOrders){
                        Product product = await _dataContext.Products.Include(p => p.Vendor).Where(p => p.Id == pu.ProductId).FirstOrDefaultAsync();
                        od.SearchParam = od.SearchParam + $" {product.Name}";
                        if(null != product.Vendor)
                        od.SearchParam = od.SearchParam + $" {product.Vendor.Name}";
                        
                    }
                    od.SearchParam = od.SearchParam.ToUpper();
                    int id = new Random().Next(1,6);
                    var service = await _dataContext.Services.AsQueryable().Where( s => s.Id == id).FirstOrDefaultAsync();
                    od.Service = service;
                            _dataContext.Add(od);
                await _dataContext.SaveChangesAsync();
                
            }
                
        }
        
    }


    }

}