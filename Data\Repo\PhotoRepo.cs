using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.Model;

namespace QuickMessenger.API.Data.Repo
{
    public class PhotoRepo : IPhotoRepo
    {
        private readonly DataContext _context;
        public PhotoRepo(DataContext dataContext)
        {
            _context = dataContext;

        }
        public void Delete<T>(T entity) where T : class
        {
             _context.Remove(entity);
        }

        public void DeleteAll<T>(IEnumerable<T> entities) where T : class
        {
            _context.RemoveRange(entities);
        }

        public async  Task<Photo> GetPhoto(int Id)
        {
            return await _context.Photos.AsQueryable().Where( p => p.Id == Id).FirstOrDefaultAsync();
        }

        public async Task<ICollection<Photo>> GetPhotos(List<int> Ids)
        {
            return await _context.Photos.AsQueryable().Where(
                p => Ids.Contains(p.Id)
            ).ToListAsync();
        }

        public async Task<bool> SaveAll()
        {
            return await _context.SaveChangesAsync() > 0;
        }
    }
}