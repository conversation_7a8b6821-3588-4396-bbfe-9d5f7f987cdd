using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using QuickMessenger.API.Data.DTO.Product;
using QuickMessenger.API.Data.DTO.Shared;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.IService;
using QuickMessenger.API.Data.Model;

namespace QuickMessenger.API.Data.Services
{
    public class ProductService : IProductService
    {
        private static string _CategoryPhotoPath = "product/Category";
        private static string _ServicePhotoPath = "product/service";
        private readonly IProductRepo _prod_Repo;
        private readonly IMapper _mapper;
        private readonly IPhotoService _photoService;
        private readonly IPhotoRepo _photoRepo;
        public ProductService(IProductRepo prod_Repo, IMapper mapper, IPhotoService photoService, IPhotoRepo photoRepo)
        {
            _photoRepo = photoRepo;
            _photoService = photoService;
            _mapper = mapper;
            _prod_Repo = prod_Repo;

        }

        public Task<bool> CreateCategoryProperties(ICollection<Prod_PropertyDto> Prod_propeerties, Prod_Category Category)
        {
            throw new System.NotImplementedException();
        }

        public Task DeleteCategoryPhoto(Prod_Category Category)
        {
            //Category.ImageUrl = null;
            _photoService.DeletePhoto($"{_CategoryPhotoPath}_{Category.Id}");
            return Task.CompletedTask;
        }

        public async Task<bool> UpdateCategoryProperties
        (ICollection<Prod_PropertyDto> Prod_properties, Prod_Category Category)
        {
            var Ids = Prod_properties
                                     .Select(P => P.Id).ToList();
            var propertiesFromRepo = await _prod_Repo.GetCategoryProperties(Category.Id);
            List<int> IdsTodelete = propertiesFromRepo
            .Where(p => !Ids.Contains(p.Id) && p.Id != 0).Select(p => p.Id).ToList();

            if (await _prod_Repo.AnyPropertyHasRecord(IdsTodelete))
                return false;
            var propertiesToDelete = await _prod_Repo.GetProperties(IdsTodelete);
            _prod_Repo.DeleteAll(propertiesToDelete);
            //update existing properties
            
            var propertiesToAddDto = Prod_properties.Where(p => p.Id == 0);
            var propertiesToAdd = _mapper.Map<ICollection<Property>>(propertiesToAddDto);
            if (propertiesToAdd.Count() > 0)
                _prod_Repo.AddAll(propertiesToAdd);
            //because of the unique constraint, 
            //check if the Properties to be added (excluding the ones to be deleted) exist in the database
            if (await _prod_Repo.
            AnyPropertyExists(propertiesToAdd, Category, IdsTodelete))
                return false;
            return true;
        }

        public async Task<bool> AddCategoryPhoto(IFormFile file, Prod_Category Category)
        {
            string url = await _photoService.AddPhoto(file, $"{_CategoryPhotoPath}_{Category.Id}");

            if (!url.Equals(Category.ImageUrl))
            {
                Category.ImageUrl = url;
                if (await _prod_Repo.SaveAll())
                    return true;
            }
            if (!string.IsNullOrEmpty(url))
                return true;
            return false;
        }

        public async Task<bool> AddProductPhoto(IFormFile file, int Id, string path, int maxPhotos)
        {
            var   product = await _prod_Repo.GetProduct(Id);
            var productPhotos = product.Photos;
            var MPath = $"{path}_{product.Id}";
            if(productPhotos.Count >= maxPhotos)
            return false;
            
            if(productPhotos.Count > 0)
            {
                MPath = MPath + "_" + _photoService.GetNextIndex(maxPhotos,productPhotos);
            }

            else MPath = MPath + "_1";
           string url = await _photoService.AddPhoto(file, MPath);
            Photo photo = new Photo
            {
                PublicId = MPath,
                DateAdded = DateTime.Now,
                Product = product,
                ProductId = product.Id,
                Url = url
            };
           _prod_Repo.Add(photo);
           return await _prod_Repo.SaveAll();
           
        }

        public async Task<bool> DeleteProductPhotos(ICollection<PhotoDto> PhotosDToToDelete, string _photoPath)
        {
            if(null == PhotosDToToDelete)
            return false;
            var IdsToDelete = PhotosDToToDelete.Where(p => p.Deleted).Select(p => p.Id).ToList();
            var PhotosToDelete = await _photoRepo.GetPhotos(IdsToDelete);
            _photoRepo.DeleteAll(PhotosToDelete);
            if( PhotosToDelete.Count() > 0 && await _photoRepo.SaveAll()){
                foreach(int id in IdsToDelete)
                    _photoService.DeletePhoto($"{_photoPath}_{id}");
                return true;
            }
            return false;

        }

        public async Task<bool> UpdateProductProperties(ICollection<ProductPropertyDto> properties, int Id)
        {
            var allIds = properties.Select(p => p.PropertyId);
            var IdsTodelete = properties.Where(p => p.Deleted).Select(p => p.PropertyId);
            
            var ProdProps = await _prod_Repo.GetProductProperties(allIds, Id);
            var RepoIds = ProdProps.Select(p => p.PropertyId).ToList();
            var newPropertiesDto =  properties.Where(p => !RepoIds.Contains(p.PropertyId));
            var propsToDeleteFromRepo = ProdProps.Where(p => IdsTodelete.Contains(p.PropertyId));
            foreach(ProductPropertyDto dto in properties){
                _mapper.Map(dto, ProdProps.Where(p => p.PropertyId == dto.PropertyId).FirstOrDefault());
            }
            if(propsToDeleteFromRepo.Count() > 0)
            _prod_Repo.DeleteAll(propsToDeleteFromRepo);

            foreach(ProductPropertyDto dto in newPropertiesDto){
                var property = _mapper.Map<ProductProperty>(dto);
                property.ProductId = Id;
                _prod_Repo.Add(property);
            }

            return await _prod_Repo.SaveAll();

        }
        public async Task<bool> CreateProductProperties(ICollection<ProductPropertyDto> properties, int Id)
        {
            if(null == properties || properties.Count == 0)
            return true;
            
            foreach(ProductPropertyDto dto in properties){
                var property = _mapper.Map<ProductProperty>(dto);
                property.ProductId = Id;
                _prod_Repo.Add(property);
            }

            return await _prod_Repo.SaveAll();

        }

        public async Task<bool> AddServicePhoto(IFormFile file, Service service, int maxPhotos)
        {
            
            var productPhotos = service.Photos;
            var path = $"{_ServicePhotoPath}_{service.Id}";
            if(productPhotos.Count >= maxPhotos)
            return false;
            
            if(productPhotos.Count > 0)
            {
                path = path + "_" + _photoService.GetNextIndex(maxPhotos,productPhotos);
            }

            else path = path + "_1";
           string url = await _photoService.AddPhoto(file, path);
            Photo photo = new Photo
            {
                PublicId = path,
                DateAdded = DateTime.Now,
                Service = service,
                ServiceId = service.Id,
                Url = url
            };
           _prod_Repo.Add(photo);
           return await _prod_Repo.SaveAll();
           
        }
    }
}