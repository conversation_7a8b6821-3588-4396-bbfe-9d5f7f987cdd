using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using QuickMessenger.API.Data.DTO.Vendor;
using QuickMessenger.API.Data.DTO.Staff;
using QuickMessenger.API.Data.DTO.Product;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.IService;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Helpers.Params;

namespace QuickMessenger.API.Controllers.Staff
{
    [ApiController]
    [Authorize(Policy = "ProdMgt")]
    [Route("api/qm_475/staff/[controller]")]
    public class VendorController : ControllerBase
    {
        private static string _photoPath = "qm/vendor/vendor";
        private readonly IMapper _mapper;
        private readonly IVendorRepo _vendorRepo;
        private readonly IProductRepo _productRepo;
        private readonly IPhotoService _photoService;
        public VendorController(IMapper mapper, IVendorRepo vendorRepo, IProductRepo productRepo, IPhotoService photoService)
        {
            _photoService = photoService;
            _vendorRepo = vendorRepo;
            this._productRepo = productRepo;
            _mapper = mapper;

        }
        [HttpGet]
        public async Task<IActionResult> GetVendors([FromQuery] VendorParams vendorParams)
        {
            var vendorsFromRepo = await _vendorRepo.GetVendors(vendorParams);
            var objToreturn = new
            {
                Vendors = _mapper.Map<IEnumerable<VendorListDto>>(vendorsFromRepo),
                Pagination = new
                {
                    CurrentPage = vendorsFromRepo.CurrentPage,
                    PageSize = vendorsFromRepo.PageSize,
                    TotalCount = vendorsFromRepo.TotalCount,
                    TotalPages =  vendorsFromRepo.TotalPages
                }
            };
            
            return Ok(objToreturn);
           
        }

        [HttpGet("lite")]
        public async Task<IActionResult> GetVendorsLite()
        {
            var vendorsFromRepo = await _vendorRepo.GetVendors();
            var objToreturn = new
            {
                Vendors = _mapper.Map<IEnumerable<VendorListDtoLite>>(vendorsFromRepo)
            };
            
            return Ok(objToreturn);
           
        }


        [HttpGet("{Id}")]
        public async Task<IActionResult> GetVendor(int Id)
        {
            var vendorFromRepo = await _vendorRepo.GetVendor(Id);
            var vendorToReturn = _mapper.Map<VendorDto>(vendorFromRepo);

            return Ok(vendorToReturn);
        }

        [HttpPost("create")]
        public async Task<IActionResult> CreateVendor(VendorDto vendorDto)
        {

            var VendorToCreate = _mapper.Map<Vendor>(vendorDto);
            VendorToCreate.Address.VendorId = null;
            VendorToCreate.Address.UserId = null;
            VendorToCreate.State = "Open";

            if(await _vendorRepo.VendorNameAndAddressExists(VendorToCreate.UniqueParam))
            {
                return BadRequest(new { Error = "A vendor with a similar name and location already exist" });
            }
            _vendorRepo.Add(VendorToCreate);

            if (await _vendorRepo.SaveAll())
            {
                return Ok(new { Id = VendorToCreate.Id });
            }

            return BadRequest();
        }

        [HttpPut("{Id}/update")]
        public async Task<IActionResult> UpdateVendor(int Id, VendorDto vendorDto)
        {
            bool  vendorUpdate = false,AddUpdate = false, logoUpdate = false;
            var Vendor = await _vendorRepo.GetVendorWithoutRelation(Id);
            _mapper.Map(vendorDto, Vendor);
             if(await _vendorRepo.VendorNameAndAddressExists(Id, Vendor.UniqueParam))
            {
                return BadRequest(new { Error = "A vendor with a similar name and location already exist" });
            }
            //update Vendor

            Vendor.Address = null;
            Vendor.Logo = null;
            if(await _vendorRepo.SaveAll())
                vendorUpdate = true;

            var Address = await _vendorRepo.GetVendorAddress(Vendor);
            _mapper.Map(vendorDto.Address, Address);
            Address.UserId = null;
            if(await _vendorRepo.Save())
            AddUpdate = true;

             var Logo = await _vendorRepo.GetVendorPhoto(Vendor);
            //delete logo
            if (vendorDto.Logo != null && vendorDto.Logo.Deleted && Logo!=null)
            {
                _photoService.DeletePhoto($"{_photoPath}_{Id}");

                _vendorRepo.Delete(Logo);
                if(await _vendorRepo.Save())
                logoUpdate = true;
            }

            if (vendorUpdate || AddUpdate || logoUpdate)
            {
                return NoContent();
            }
            return BadRequest(new { Error = "Could not update  Vendor" });
        }

        [HttpPut("{Id}/state")]
        public async Task<IActionResult> UpdateVendorState(int Id, VendorStateUpdateDto vendorStateDto)
        {
            var vendor = await _vendorRepo.GetVendorWithoutRelation(Id);
            if (vendor == null)
            {
                return NotFound(new { Error = "Vendor not found" });
            }

            vendor.State = vendorStateDto.State;

            if (await _vendorRepo.SaveAll())
            {
                return NoContent();
            }

            return BadRequest(new { Error = "Could not update vendor state" });
        }

        [HttpGet("{Id}/products")]
        public async Task<IActionResult> GetVendorProducts(int Id, [FromQuery] VendorProductParams productParams)
        {
            // Verify vendor exists
            var vendor = await _vendorRepo.GetVendorWithoutRelation(Id);
            if (vendor == null)
            {
                return NotFound(new { Error = "Vendor not found" });
            }

            // Set the vendor ID in the params
            productParams.VendorId = Id;

            var products = await _productRepo.GetProducts(productParams);

            var objToReturn = new
            {
                Products = _mapper.Map<IEnumerable<ProductListDto>>(products.Item1),
                Vendor = _mapper.Map<VendorListDto>(vendor),
                Pagination = new
                {
                    CurrentPage = products.Item1.CurrentPage,
                    PageSize = products.Item1.PageSize,
                    TotalCount = products.Item1.TotalCount,
                    TotalPages = products.Item1.TotalPages
                },
                ChildrenCategories = _mapper.Map<List<Prod_CategoryListDtoLite2>>(products.Item2),
                MinPrice = products.Item4,
                MaxPrice = products.Item5,
                Category = _mapper.Map<Prod_CategoryListDtoLite2>(products.Item3)
            };

            return Ok(objToReturn);
        }

        [HttpDelete("{Id}/delete")]
        public async Task<IActionResult> Delete(int Id){
            if(!await _vendorRepo.VendorHasRecords(Id))
            {
                var Vendor = await _vendorRepo.GetVendor(Id);
                var Logo = await _vendorRepo.GetVendorPhoto(Vendor); 
                var Add = Vendor.Address;
                if(Add != null)
                _vendorRepo.Delete(Add);
                if(Logo != null)
                _vendorRepo.Delete(Logo);
                _vendorRepo.Delete(Vendor);

                _photoService.DeletePhoto($"{_photoPath}_{Id}");
                
                if(await _vendorRepo.Save())
                return Ok();
            }
            else return BadRequest(new { Error = "Could not delete Vendor, The vendor has one or more products" });

         return BadRequest(new { Error = "Could not delete  Vendor" });
        }

        [HttpPost("{Id}/addPhoto")]
        public async Task<IActionResult> AddPhoto([FromForm] IFormFile file, int Id){

            var Vendor = await _vendorRepo.GetVendor(Id);
            var Photo = await _vendorRepo.GetVendorPhoto(Vendor);
            string url = await _photoService.AddPhoto(file, $"{_photoPath}_{Id}");
            if(Photo == null){     
                Photo = new Photo{
                    
                    VendorId = Id
                };
                 _vendorRepo.Add(Photo);
            }
                    Photo.Url = url;
                    Photo.DateAdded = DateTime.Now;
                    Photo.PublicId = $"{_photoPath}_{Id}";

            
            if( await _vendorRepo.Save())
                {
                    return Ok();
                }
            return BadRequest(new { Error = "Could not upload  Photo" });
            
        }
        
    }
}