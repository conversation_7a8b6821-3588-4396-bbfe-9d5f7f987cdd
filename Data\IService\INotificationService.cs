using System.Threading.Tasks;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Data.Services;

namespace QuickMessenger.API.Data.IService
{
    public interface INotificationService
    {
         Task SendOrderNotification(string Title, string Body, Order order, NotificationTopic topic);
          Task SendOrderNotification(string Title, string Body, Cart cart, NotificationTopic topic);
    }
}