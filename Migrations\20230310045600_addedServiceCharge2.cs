﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace QuickMessenger.API.Migrations
{
    public partial class addedServiceCharge2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<double>(
                name: "ServiceCharge",
                table: "Services",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<double>(
                name: "ServiceCharge",
                table: "Orders",
                nullable: false,
                defaultValue: 0.0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ServiceCharge",
                table: "Services");

            migrationBuilder.DropColumn(
                name: "ServiceCharge",
                table: "Orders");
        }
    }
}
