
using FirebaseAdmin.Messaging;
using Newtonsoft.Json;

namespace QuickMessenger.API.Data.DTO.FireBase
{
    class FireBaseConfig
    {
        public string ApiKey { get; set; }
        public string ProjectId { get; set; }
        public string AppId { get; set; }
        public string Client_id { get; set; }
        public int Client_type { get; set; }
    }

    class FireBaseData
    {
        public string Title { get; set; }
        public string  Body { get; set; }
        public string Topic { get; set; }
        public int? CartId { get; set; }
        public int? OrderId { get; set; }
        public string Token { get; set; }
    }
}


