using System;
using System.ComponentModel.DataAnnotations;

namespace QuickMessenger.API.Helpers.Params
{
    public class PaginationParams
    {
        private int minPageSize = 20;
        public int PageNumber { get; set; } = 1;
        [Required]
        public int PageSize { get{
            return this.minPageSize;}
            set{
                if(value > 20)
                this.minPageSize = value;
            } }
        public string SearchTerm { get; set; }
    }

    public class Prod_CategoryParams:PaginationParams
    {
        
    }
    public class ServiceParams:PaginationParams
    {
        
    }
     public class Prod_MeasurementTypeParam: PaginationParams
    {
        
    }
     public class Prod_PropertyTypeParams: PaginationParams
    {
        
    }
     public class UserParam: PaginationParams
    {      
        [Required]
        public string[] Roles { get; set; }
        public bool Deactivated { get; set; }
        
    }

    public class ClientListParams: PaginationParams
    {  
      public bool Deactivated { get; set; }
    }

    public class VendorParams: PaginationParams
    {
        
    }
    public class ProductParams: PaginationParams
    {
        public bool Deactivated { get; set; }
        // public int[] VendorIds { get; set; }
    }

     public class OrderParams: PaginationParams
    {
        //State of the Order is any of the following 'Pending', 'Delivered', 'On The Way'
       public string[] States { get; set; }
       //Type of the order is either 'Purchase' or 'Pickup'
       public string Type { get; set; }
       public DateTime? FromDate { get; set; }
       public DateTime? ToDate { get; set; }

    }

    public class DateRangeParam{
        public DateTime? Start { get; set; }
        public DateTime? End { get; set; }
        public string SearchParam { get; set; }
    }

    public class ClientProductParams: PaginationParams
    {
        public double MinPrice { get; set; }
        public double MaxPrice { get; set; }
        public int[] Categories { get; set; }
        public int Category { get; set; }
        public string Sort { get; set; }
       
    }

    public class ServiceProductParams: PaginationParams
    {
        public double MinPrice { get; set; }
        public double MaxPrice { get; set; }
        public int Category { get; set; }
        public string Sort { get; set; }
        [Required]
        public string ServiceNameId { get; set; }
       //public string SearchTerm { get; set; }           
    }

    public class VendorProductParams: PaginationParams
    {
        public double MinPrice { get; set; }
        public double MaxPrice { get; set; }
        public int Category { get; set; }
        public string Sort { get; set; }
        [Required]
        public int VendorId { get; set; }
        
       
    }

    public class ServiceVendorProductParams: ServiceProductParams{
         public int VendorId { get; set; }
    }

    public class ClientVendorParams: PaginationParams
    {
         public string[] Cities { get; set; }
         public string State { get; set; }
    }

}