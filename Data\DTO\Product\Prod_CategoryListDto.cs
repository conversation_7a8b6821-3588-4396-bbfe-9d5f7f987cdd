namespace QuickMessenger.API.Data.DTO.Product
{
    public class Prod_CategoryListDto
    {
        public int Id { get; set; }
        public string  Name { get; set; }
        public string NameId { get; set; }
        public string Description { get; set; }
        public Prod_CategoryListDtoLiteParent Parent { get; set; }
        public string ImageUrl { get; set; }
       
    }

    public class Prod_CategoryListDtoLite
    {
         public int Id { get; set; }
         public string  Name { get; set; }
         public string ImageUrl { get; set; }
         public Prod_CategoryListDtoLiteParent Parent { get; set; }
    }

    public class Prod_CategoryListDtoLiteParent
    {        
         public string  Name { get; set; }
         public Prod_CategoryListDtoLiteParent Parent { get; set; }
    }

    public class Prod_CategoryListDtoLiteParent2
    {        
         public string  Name { get; set; }
         public int Id { get; set; }
         public Prod_CategoryListDtoLiteParent2 Parent { get; set; }
    }

    public class Prod_CategoryListDtoLite2
    {
         public int Id { get; set; }
         public string  Name { get; set; }
         public string NameId { get; set; }
        public Prod_CategoryListDtoLiteParent2 Parent { get; set; }
    }
}