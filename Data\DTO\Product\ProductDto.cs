using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using QuickMessenger.API.Data.DTO.Shared;
using QuickMessenger.API.Data.DTO.Vendor;
using QuickMessenger.API.Data.Model;

namespace QuickMessenger.API.Data.DTO.Product
{
    public class ProductDto
    {
         public int Id { get; set; }
        [Required]
        public string Name { get; set; }
        [Required]
        public double Price { get; set; }
        public Prod_CategoryDto Prod_Category { get; set; }
        [Required]
        public int Prod_CategoryId { get; set; }
        public string Description { get; set; }
        public ICollection<PhotoDto> Photos { get; set; }
        public VendorDto Vendor { get; set; }
        public int? VendorId { get; set; }
        public ICollection<ProductPropertyDto> ProductProperties{ get; set; }
        public bool CanDelete { get; set; }
        public bool Deactivated { get; set; }
        public bool OutOfStock { get; set; }
         public int ServiceId { get; set; }
         public ServiceDto Service { get; set; }
         public string HmtlDescription { get; set; }
         public string TextDescription { get; set; }
       
    }

    public class ProductForCreateDto
    {
         public int Id { get; set; }
        [Required]
        public string Name { get; set; }
        [Required]
        public double Price { get; set; }
        [Required]
        public int Prod_CategoryId { get; set; }
        [Required]
        public int ServiceId { get; set; }
        public string Description { get; set; }
        public int? VendorId { get; set; }
        public ICollection<ProductPropertyDto> ProductProperties{ get; set; }
        public ICollection<PhotoDto> Photos { get; set; }
        public bool Deactivated { get; set; }
        public bool OutOfStock { get; set; }
    }

}