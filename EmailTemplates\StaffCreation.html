<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>QuickWaka Registration</title>

  <style type="text/css">
    img { max-width: 600px; outline: none; text-decoration: none; -ms-interpolation-mode: bicubic;}
    a img { border: none; }
    table { border-collapse: collapse !important;}
    #outlook a { padding:0; }
    .ReadMsgBody { width: 100%; }
    .ExternalClass { width: 100%; }
    .backgroundTable { margin: 0 auto; padding: 0; width: 100% !important; }
    table td { border-collapse: collapse; }
    .ExternalClass * { line-height: 115%; }
    .container-for-gmail-android { min-width: 600px; }


    /* General styling */
    * {
      font-family: Helvetica, Arial, sans-serif;
    }

    body {
      -webkit-font-smoothing: antialiased;
      -webkit-text-size-adjust: none;
      width: 100% !important;
      margin: 0 !important;
      height: 100%;
      color: #676767;
    }

    td {
      font-family: Helvetica, Arial, sans-serif;
      font-size: 14px;
      color: #777777;
      text-align: center;
      line-height: 21px;
    }

    a {
      color: #676767;
      text-decoration: none !important;
    }

    .pull-left {
      text-align: left;
    }

    .pull-right {
      text-align: right;
    }

    .header-lg,
    .header-md,
    .header-sm {
      font-size: 32px;
      font-weight: 700;
      line-height: normal;
      padding: 35px 0 0;
      color: #4d4d4d;
    }

    .header-md {
      font-size: 24px;
    }

    .header-sm {
      padding: 5px 0;
      font-size: 18px;
      line-height: 1.3;
    }

    .content-padding {
      padding: 20px 0 30px;
    }

    .mobile-header-padding-right {
      width: 290px;
      text-align: right;
      padding-left: 10px;
    }

    .mobile-header-padding-left {
      width: 290px;
      text-align: left;
      padding-left: 10px;
    }

    .free-text {
      width: 100% !important;
      padding: 10px 60px 0px;
    }

    .block-rounded {
      border-radius: 5px;
      border: 1px solid #e5e5e5;
      vertical-align: top;
    }

    .button {
      padding: 55px 0 0;
    }

    .info-block {
      padding: 0 20px;
      width: 260px;
    }

    .mini-block-container {
      padding: 30px 50px;
      width: 500px;
    }

    .mini-block {
      background-color: #ffffff;
      width: 498px;
      border: 1px solid #cccccc;
      border-radius: 5px;
      padding: 60px 75px;
    }

    .block-rounded {
      width: 260px;
    }

    .info-img {
      width: 258px;
      border-radius: 5px 5px 0 0;
    }

    .force-width-img {
      width: 480px;
      height: 1px !important;
    }

    .force-width-full {
      width: 600px;
      height: 1px !important;
    }

    .user-img img {
      width: 82px;
      border-radius: 5px;
      border: 1px solid #cccccc;
    }

    .user-img {
      width: 92px;
      text-align: left;
    }

    .user-msg {
      width: 236px;
      font-size: 14px;
      text-align: left;
      font-style: italic;
    }

    .code-block {
      padding: 10px 0;
      border: 1px solid #cccccc;
      width: 20px;
      color: #4d4d4d;
      font-weight: bold;
      font-size: 18px;
      align-self: center;
    }

     .mini-img {
      padding: 5px;
      width: 140px;
    }

    .mini-img img {
      border-radius: 5px;
      width: 140px;
    }

    .mini-imgs {
      padding: 25px 0 30px;
    }

    .progress-bar {
      padding: 0 15px 0;
    }

    .step {
      vertical-align: top;
    }

    .step img {
      width: 109px;
      height: 78px;
    }

    .active {
      font-weight: bold;

    }

  </style>

  <style type="text/css" media="screen">
    @import url(http://fonts.googleapis.com/css?family=Oxygen:400,700);
  </style>

  <style type="text/css" media="screen">
    @media screen {
      /* Thanks Outlook 2013! */
      * {
        font-family: 'Oxygen', 'Helvetica Neue', 'Arial', 'sans-serif' !important;
      }
    }
  </style>

  <style type="text/css" media="only screen and (max-width: 480px)">
    /* Mobile styles */
    @media only screen and (max-width: 480px) {

      table[class*="container-for-gmail-android"] {
        min-width: 290px !important;
        width: 100% !important;
      }

      table[class="w320"] {
        width: 320px !important;
      }

      td[class*="mobile-header-padding-left"] {
        width: 160px !important;
      }

      img[class="force-width-gmail"] {
        display: none !important;
        width: 0 !important;
        height: 0 !important;
      }

      td[class="mobile-block"] {
        display: block !important;
      }

      td[class="mini-img"],
      td[class="mini-img"] img{
        width: 150px !important;
      }

      td[class*="mobile-header-padding-left"] {
        width: 160px !important;
        padding-left: 0 !important;
      }

      td[class*="mobile-header-padding-right"] {
        width: 160px !important;
        padding-right: 0 !important;
      }

      td[class="header-lg"] {
        font-size: 24px !important;
        padding-bottom: 5px !important;
      }

      td[class="header-md"] {
        font-size: 18px !important;
        padding-bottom: 5px !important;
      }

      td[class="content-padding"] {
        padding: 5px 0 30px !important;
      }

       td[class="button"] {
        padding: 5px !important;
      }

      td[class*="free-text"] {
        padding: 10px 18px 30px !important;
      }

      img[class="force-width-img"],
      img[class="force-width-full"] {
        display: none !important;
      }

      td[class="info-block"] {
        display: block !important;
        width: 280px !important;
        padding-bottom: 40px !important;
      }

      td[class="info-img"],
      img[class="info-img"] {
        width: 278px !important;
      }

      td[class="mini-block-container"] {
        padding: 8px 20px !important;
        width: 280px !important;
      }

      td[class="mini-block"] {
        padding: 20px 0 !important;
      }

      td[class*="step"] img {
        width: 86px !important;
        height: 62px !important;
      }

      td[class="progress-bar"] {
        padding: 0 11px 25px;
      }

      td[class="user-img"] {
        display: block !important;
        text-align: center !important;
        width: 100% !important;
        padding-bottom: 10px;
      }

      td[class="user-msg"] {
        display: block !important;
        padding-bottom: 20px;
      }
    }
  </style>
</head>
<body bgcolor="#f7f7f7">
  <table align="center" cellpadding="0" cellspacing="0" class="container-for-gmail-android" width="100%">
    <tr>
      <td align="left" valign="top" width="100%" style="background:repeat-x url(http://s3.amazonaws.com/swu-filepicker/4E687TRe69Ld95IDWyEg_bg_top_02.jpg) #ffffff;">
        <center>
         <img src="http://s3.amazonaws.com/swu-filepicker/SBb2fQPrQ5ezxmqUTgCr_transparent.png" class="force-width-gmail">
          <table cellspacing="0" cellpadding="0" width="100%" bgcolor="#ffffff" background="http://s3.amazonaws.com/swu-filepicker/4E687TRe69Ld95IDWyEg_bg_top_02.jpg" style="background-color:transparent">
            <tr>
              <td width="100%" height="80" valign="top" style="text-align: center; vertical-align:middle;">   
                  
              </td>
            </tr>
          </table>
        </center>
      </td>
    </tr>
    <tr>
      <td align="left" valign="top"></td>
    </tr>
    <tr>
      <td align="center" valign="top" width="100%" style="background-color: #f7f7f7;" class="content-padding">
        <center>
          <table cellspacing="0" cellpadding="0" width="600" class="w320">
            <tr>
              <td class="header-lg"> 
                  Hello {0}, 
              </td>
            </tr>
            <tr>
              <td class="header-md">
                You've been added as a staff at QuickMessenger
              </td>
            </tr>
            <tr>
              <td class="free-text">
                There's one last step until your account is 100% ready. We need you to sign in into QuickMessenger.
              </td>
            </tr>
            <tr>
              <td class="mini-block-container">
                <table cellspacing="0" cellpadding="0" width="100%"  style="border-collapse:separate !important;">
                  <tr>
                    <td class="mini-block">
                      <table cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                          <td class="progress-bar">
                            <table cellpadding="0" cellspacing="0" width="100%">
                              <tr>
                                <span>Your default Password is:</span>
                              </tr>
                                <tr>
                                    <td class="code-block" >
                                    {1}
                                    </td>
                                  </tr>
                              <tr>
  
                                <!-- STEP ONE -->
                                <td class="step">
                                  <img src="http://s3.amazonaws.com/swu-filepicker/0oYLLViRBKv7a0Lzh6vC_v3_03.jpg" alt="step one" /><br />
                                  Create Account
                                </td>
  
                                 <!-- STEP TWO -->
                                <td class="step active">
                                  <img src="http://s3.amazonaws.com/swu-filepicker/YL3H3V5bRyGuy6pess9T_v33_04.jpg" alt="step one" /><br />
                                  Sign In
                                </td>
  
                                 <!-- STEP THREE -->
                                <td class="step">
                                  <img src="http://s3.amazonaws.com/swu-filepicker/YSTlgtgaTSa897tPTUhl_v3_05.jpg" alt="step one" />
                                  Complete
                                </td>
  
                              </tr>
                            </table>
                          </td>
                        </tr>
                        <tr>
                          <td class="button">
                            <div>
                              <v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="http://" style="height:45px;v-text-anchor:middle;width:155px;" arcsize="15%" strokecolor="#ffffff" fillcolor="#ff6f6f">
                                <w:anchorlock/>
                                <center style="color:#ffffff;font-family:Helvetica, Arial, sans-serif;font-size:14px;font-weight:regular;">Sign In</center>
                              </v:roundrect>
                            <a href="{2}"
                            style="background-color:#ff6f6f;border-radius:5px;color:#ffffff;display:inline-block;font-family:'Cabin', Helvetica, Arial, sans-serif;font-size:14px;font-weight:regular;line-height:45px;text-align:center;text-decoration:none;width:155px;-webkit-text-size-adjust:none;mso-hide:all;">Sign In</a></div>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </center>
      </td>
    </tr>
  
    <tr>
      <td align="center" valign="top" width="100%" style="background-color: #f7f7f7; height: 100px;">
        <center>
          <table cellspacing="0" cellpadding="0" width="600" class="w320">
            <tr>
              <td style="padding: 25px 0 25px">
                <strong>QuickMessenger Inc</strong><br />
                1234 Awesome St <br />
                Wonderland <br /><br />
              </td>
            </tr>
          </table>
        </center>
      </td>
    </tr>
  </table>
  </body>
  </html>