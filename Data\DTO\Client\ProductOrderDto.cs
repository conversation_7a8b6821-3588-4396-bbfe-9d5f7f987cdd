using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using QuickMessenger.API.Data.DTO.Product;
using QuickMessenger.API.Data.DTO.Shared;

namespace QuickMessenger.API.Data.DTO.Client
{
    public class ProductOrderDto
    {
        public int ProductId { get; set; }
        public int OrderId { get; set; }
        public ProductDto Product { get; set; }    
        public int Quantity { get; set; }
        public double ProductPrice{get;set;}
        public string State { get; set; }
        public DateTime Date { get; set; }
    }

    public class ProductOrderDto2
    {
        //  public int Id { get; set; }
        [Required]
        public int ProductId { get; set; } 
        public int OrderId { get; set; }
        [Required]
        public int Quantity { get; set; }
        public double ProductPrice{get;set;}
    }


    public class ProductDto3 { 
        public string Name { get; set; }
        public string State { get; set; }
        public double Price { get; set; }
        public int quantity { get; set; }
        public int Id { get; set; }
        public int OrderId { get; set; }
        public string PhotoUrl { get; set; }

    }

     public class CartDetailtViewForRiderDto{
        public string Lastname{get; set;} 
        public string FirstName { get; set; }
        public string Phone { get; set; }
        public DateTime Date { get; set; }
        public int ClientId { get; set; }
        public int CartId { get; set; }
        public AddressDto Address { get; set; }
         public ICollection<PickupItemViewDto> PickupItems { get; set; }
         public ICollection<VendorOrderForRider> Vendors { get; set; }
        public string photoUrl { get; set; }
    }

    public class VendorOrderForRider{
        public string Name { get; set; }
        public AddressDto Address { get; set; }
        public string Phone { get; set; }
        public ICollection<ProductDto3> Products { get; set; }
        public double Total { get; set; }
    }

    public class VendorOrder{
        public string LastName { get; set; }
        public string FirstName { get; set; }
        public DateTime  Date { get; set; }
        public double Total { get; set; }
        public ICollection<ProductDto3> Products;
    }
}