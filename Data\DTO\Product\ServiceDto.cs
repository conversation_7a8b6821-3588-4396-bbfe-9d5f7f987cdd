using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using QuickMessenger.API.Data.DTO.Shared;

namespace QuickMessenger.API.Data.DTO.Product
{
    public class ServiceDto
    {
        public int Id {get;set;}
        [Required]
        public string Name {get;set;} 
        public string NameId {get;set;} 
        [Required]
        public string Description {get;set;}
        public ICollection<PhotoDto> Photos { get; set; }
        public bool CanDelete { get; set; }
        public bool Generic { get; set; }
        public bool PickupAllowed { get; set; }
        public bool PurchaseAllowed { get; set; }
        public bool IsDefaultGeneric { get; set; }
    }

    public class ServiceListDto
    {
        public int Id {get;set;}
        public string Name {get;set;} 
        public string NameId {get;set;} 
        //public string Description { get; set; }
        public string PictureUrl { get; set; } 
        //public int MyProperty { get; set; }
        public bool Generic { get; set; }
        public bool PickupAllowed { get; set; }
        public bool PurchaseAllowed { get; set; }
        public bool IsDefaultGeneric { get; set; }
    }

    public class ServiceDto2
    {
        public int Id {get;set;}
        public string Name {get;set;} 
        public string NameId {get;set;} 
        public string PictureUrl { get; set; }
        public string Description { get; set; }
        public bool PickupAllowed { get; set; }
        public bool PurchaseAllowed { get; set; }
        public bool IsDefaultGeneric { get; set; }
    }

    public class ServiceLiteDto{
        public int Id { get; set; }
        public string Name { get; set; }
        public string NameId { get; set; }
       
    }

    public class ServiceLiteDto2{

        public int Id { get; set; }
        public string Name { get; set; }
        public string PictureUrl { get; set; }

        public bool Generic { get; set; }
        public bool PickupAllowed { get; set; }
        public bool PurchaseAllowed { get; set; }
        public bool IsDefaultGeneric { get; set; }
       
    }

}