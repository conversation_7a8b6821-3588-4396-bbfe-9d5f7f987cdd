using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using QuickMessenger.API.Data.DTO.Staff;
using QuickMessenger.API.Data.DTO.Client;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Helpers;
using QuickMessenger.API.Helpers.Params;

namespace QuickMessenger.API.Data.IRepo
{
    public interface IOrderRepo
    {
         void DetachAllEntities();
         void DetachEntity<T>(T entity) where T: class;
         void DetachEntities<T>(IEnumerable<T> entities) where T: class;
         void ReloadEntity<T>(T entity) where T: class;
          void Delete<T>(T entity) where T: class;
          void Update<T>(T entity) where T: class;
          void DeleteAll<T>(IEnumerable<T> entities) where T:class;
          Task<PagedList<Order>> GetAllPaidOrders(OrderParams orderParams);
           Task<ICollection<Order>> GetOrdersByClient(int ClientId);
           Task<ICollection<ProductOrder>> GetProductOrdersByVendor(int VendorId);
           Task<ICollection<VendorOrder>> GetUnpreparedOrdersByVendorGroupedByClient(int VendorId);
           Task<ICollection<VendorOrder>> GetOrdersByVendorAndDateRangeGroupedByClient(int VendorId, DateTime start, DateTime end);

          Task<ICollection<ProductOrder>> GetOrdersByVendor(int VendorId);
          Task<ICollection<VendorOrder>> GetTodayPendingOrdersByVendorGroupedByClient(int VendorId);
          Task<Order> GetOrder(int Id);
          Task<ICollection<int>> GetOrderIdsForCart(int CartId);
          Task<ICollection<string>> GetOrderTrackingIdsForCart(int CartId);
          Task<Order> GetNoLazyOrder(int Id);
          Task<Cart> GetCurrentPendingCart(int ClientId);
          Task<ICollection<Order>> GetOrdersByDate(DateTime Date);
          Task<ICollection<Order>> GetOrdersByDateRange(DateTime StartDate, DateTime EndDate);
          Task<ICollection<Order>> GetOrdersWithMonth(int  Year, int month);
          Task<ICollection<OrdersByLocationList>> GetOrdersGroupedByCities(int  Year, int month);
          Task<ICollection<OrdersByLocationList>> GetOrdersGroupedByCities(DateTime Date);
          Task<ICollection<OrdersByLocationList>> GetOrdersGroupedByCities(DateTime StartDate, DateTime EndDate);
          Task<ICollection<OrdersByVendorList>> GetOrdersGroupedByVendor(int  Year, int month);
          Task<ICollection<OrdersByVendorList>> GetOrdersGroupedByVendor(DateTime Date);
          Task<ICollection<OrdersByVendorList>> GetOrdersGroupedByVendor(DateTime StartDate, DateTime EndDate);
          Task<ICollection<OrdersByProductList>> GetOrdersGroupedByProduct(int  Year, int month);
          Task<ICollection<OrdersByProductList>> GetOrdersGroupedByProduct(DateTime Date);
          Task<ICollection<OrdersByProductList>> GetOrdersGroupedByProduct(DateTime StartDate, DateTime EndDate);
          Task<ICollection<OrdersByRiderList>> GetOrdersGroupedByRider(int  Year, int month);
          Task<ICollection<OrdersByRiderList>> GetOrdersGroupedByRider(DateTime Date);
          Task<ICollection<OrdersByRiderList>> GetOrdersGroupedByRider(DateTime StartDate, DateTime EndDate);
          Task<ICollection<Rider>> GetAllActiveRiders();
          Task<Cart> GetCart(int Id);
          Task<ProductOrder> GetProductOrder(int ProductId, int OrderId);
          Task<ICollection<ProductOrder>> GetProductOrders(int[] ProductId, int OrderId);
          Task<ICollection<PickupItem>> GetPickupsByOrderId(int[] pickupIds, int OrderId);
          Task<ICollection<PickupItem>> GetPickupsByOrder(int OrderId);
          Task<ICollection<int>> GetPickupsByOrderIds(int OrderId);
          Task<ICollection<ProductOrder>> GetProductOrdersByOrder(int OrderId);
          Task<PickupItem> GetPickup(int OrderId);
          Task<PickupItem> GetPickupWithPhotos(int OrderId);
          Task<Cart> GetCartWithOrdersint (int Id);
          Task<Cart> GetCartWithoutOrders (int Id);
          Task<ICollection<Order>> GetOrdersByCart (int CartId);
          Task<ICollection<Cart>> GetCartsByUser (int UserId);
          Task<ICollection<CartListViewForRider2>> GetCartSummaryByRider (int UserId);
          Task<ICollection<CartListViewForRider2>> GetCartSummaryByRider (int UserId, DateTime date);
          Task<ICollection<CartListViewForRider2>> GetCartSummaryByRider (int UserId, DateTime start, DateTime end, string SearchParam);
          Task<ICollection<CartListViewForRider2>> GetDeliveredCartSummaryByRider (int UserId);
           Task<ICollection<CartListViewForRider2>> GetPendingCartSummaryByRider (int UserId, string SearchParam);
          Task<ICollection<CartListViewForRider2>> GetOntheWayCartSummaryByRider(int UserId, string SearchParam);
          Task<ICollection<CartListViewForRider2>> GetOntheWayCartSummaryByRider(int UserId, DateTime start, DateTime end, string SearchParam);
          Task<ICollection<Cart>> GetNonPendingCartsByUser (int UserId);
          Task<Cart> GetCartByTrackingId(string trackingId);
          Task<Order> GetOrderByTrackingId(string trackingId);
          Task<ICollection<Photo>> GetAllPhotosBelongingToCart(int CartId);
          Task<Cart> GetCartByTransactionReference(string Tx_ref);
           void Add<T>(T entity) where T: class;
          Task<Cart> GetLastCart();
          Task<bool> SaveAll();
          void AddAll<T>(IEnumerable<T> entities) where T: class;
          Task<Cart> GetCartDetailsForRider(int cartId, int userId);
           Task<ICollection<PickupItem>> GetPickupItemsByRiderAndCart(int userId, int cartId);
          Task<ICollection<VendorOrderForRider>> GetRiderProductOrdersGroupedByVendors(int cuserId, int cartId);
          Task<ICollection<ProductOrder>> GetProductOrdersByClient(int clinentID);
        Task <ICollection<Order>>GetOrderByCartAndRider(int cartId, int userId);
        Task<ICollection<CartListViewForRider2>> GetDeliveredCartSummaryByRider(int userId, DateTime start, DateTime end, string SearchParam);
         Task<ICollection<CartListViewForRider2>> GetPendingCartSummaryByRider(int userId, DateTime start, DateTime end, string SearchParam);
        Task <ICollection<RiderEarningDto>>GetRiderEarningsForDateRange(DateTime startTime, DateTime endTime, int riderID);
        Task<ICollection<RiderEarningGroup>> GetRiderEarningsGroupedByWeek(int userId);
        Task <ICollection<RiderEarningGroup>> GetRiderEarningsGroupedByMonth(int userId);
    }
}