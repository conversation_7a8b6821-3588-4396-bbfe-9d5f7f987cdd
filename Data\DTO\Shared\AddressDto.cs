using System.ComponentModel.DataAnnotations;

namespace QuickMessenger.API.Data.DTO.Shared
{
    public class AddressDto
    {
        public int Id { get; set; }
        public int UserId{get;set;}
        [Required]
        public string Street { get; set; }
        [Required]
        public string Country { get; set; }
        [Required]
        public string State { get; set; }
        [Required]
        public string City {get; set;}
        public bool Deleted { get; set; }
        public double Longitude { get; set; }
        public double Latitude { get; set; }
    }

    public class AddressDto2{
        public int Id { get; set; }
        public string Phone { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public bool DefaultAdd { get; set; }
        public string Country { get; set; }
        public string City { get; set; }
        public string  State { get; set; }
        public string Street { get; set; }
        //longitude and latitude
        public double Longitude { get; set; }
        public double Latitude { get; set; }
    }
}