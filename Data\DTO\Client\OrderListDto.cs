using System;
using System.Collections.Generic;
using QuickMessenger.API.Data.DTO.Shared;

namespace QuickMessenger.API.Data.DTO.Client
{
    public class OrderListDto
    {
         public int Id { get; set; }
        public string ClientName { get; set; }
        public AddressDto Address { get; set; }
        public DateTime Time { get; set; }
        public string RiderName { get; set; }
        public int NumberOfItems{ get; set; }
        public string ItemsNames {get;set;}
        public string Type { get; set; }
        public string State { get; set; }
        public string Service { get; set; }
        public string TrackingId { get; set; }
        public double Cost { get; set; }
        
    }

    public class OrderListAppViewDto
    {
            public int Id { get; set; }
            public double Cost { get; set; }
            public string TrackingId { get; set; }
            public DateTime TimeDelivered { get; set; }
            public DateTime Date { get; set; }
            public string State { get; set; }
            public ICollection<PickupItemDto2> PickUpItems { get; set; }
            public ICollection<ProductOrderDto> ProductOrders { get; set; }

    }
}