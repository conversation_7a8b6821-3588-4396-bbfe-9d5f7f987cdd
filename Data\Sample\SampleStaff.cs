using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace QuickMessenger.API.Data.Sample
{
    public class SampleStaff: Model.User
    {
        public string Phone { get; set; }
        public string Role { get; set; }

        [NotMapped]
        public DateTime DateRegistered { get; set; }
        [NotMapped]
        public DateTime LastActive { get; set; }

        public SampleStaff()
        {
            this.DateRegistered = DateTime.Now;
            this.LastActive = DateTime.Now;
        }
        
    }
}