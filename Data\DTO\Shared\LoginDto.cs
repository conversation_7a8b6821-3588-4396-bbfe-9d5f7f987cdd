using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace QuickMessenger.API.Data.DTO.Shared
{
    public class LoginDto
    {
        [Required]
        [DataType(DataType.EmailAddress)]
        public string Username { get; set; }
        [Required]
        public string Password { get; set; }
    }

    public class CredentialDto{
        [Required]
        [DataType(DataType.EmailAddress)]
        public string Username { get; set; }
    }


    public class ChangePasswordDto{
        [Required]
        public string Token {get;set;}
        [DataType(DataType.Password)]
        [StringLength(255, MinimumLength = 6, ErrorMessage = "Password should be at least 6 characters")]
        public string Password { get; set; }
        [Compare("Password")]
        [StringLength(255, MinimumLength = 6, ErrorMessage = "Password should be at least 6 characters")]
        public string ConfirmPassword { get; set; }
        [Required]
        [DataType(DataType.EmailAddress)]
        public string Username { get; set; }
    }

     public class SocialLogin
    {
        public string Provider { get; set; }
        public string Token { get; set; }
    }

     public class FacebookUserDto
    {     
     [JsonProperty("id")]
     public string Id { get; set; }
     [JsonProperty("first_name")]
     public string FirstName { get; set; }
     [JsonProperty("last_name")]
     public string LastName { get; set; }
     [JsonProperty("username")]
     public string Username { get; set; }
     [JsonProperty("email")]
     public string Email { get; set; }
    }


}