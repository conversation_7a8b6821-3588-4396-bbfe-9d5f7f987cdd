using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace QuickMessenger.API.Data.Model
{
    public class Vendor
    {
        public int Id { get; set; }
        [Required]
        public string Name { get; set; }
        public string Description { get; set; }
        public string SearchParam { get; set; }
        public Address Address { get; set; }
        public int AddressId { get; set; }
        public ICollection<Product> Products { get; set; }
        public Photo Logo { get; set; }
        public string UniqueParam { get; set; }
        // public ICollection<Order> Orders { get; set; }
        [Required]
        public string Email { get; set; }
        [Required]
        public string Phone { get; set; }
        public string Phone2 { get; set; }
        [NotMapped]
        public bool CanDelete { get; set; }

        public bool Deactivated { get; set; }

        // vendor state can Closed, Open or Deactivated.
        public string State { get; set; }

        //List of  user admins in this vendor
        public ICollection<User> Users { get; set; }
    }
}