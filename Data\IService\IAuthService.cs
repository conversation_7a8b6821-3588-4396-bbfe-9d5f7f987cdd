using System.IdentityModel.Tokens.Jwt;
using System.Threading.Tasks;
using Google.Apis.Auth;
using QuickMessenger.API.Data.DTO.Shared;
using QuickMessenger.API.Data.Model;

namespace QuickMessenger.API.Data.IService
{
    public interface IAuthService
    {
         Role GetRole(User user);
         string GenerateTokenHandler(User user, bool socialMedia);
        Task<GoogleJsonWebSignature.Payload> ValidateGoogleToken(string googleTokenId);
        Task<FacebookUserDto> VerifyFacebookAccessToken(string accessToken);
        Task<JwtSecurityToken> ValidateAppleIdentityTokenAsync(string accessToken);
    }
}