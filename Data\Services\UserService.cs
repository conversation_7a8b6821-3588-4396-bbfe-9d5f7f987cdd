
using Microsoft.Extensions.Options;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using QuickMessenger.API.Data.DTO.Staff;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.IService;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Helpers;
using System.Text;
using System.Security.Cryptography;
using QuickMessenger.API.Data.DTO.Shared;
using System.Collections.Generic;
using System.Linq;
using QuickMessenger.API.Data.DTO.Client;
using System;

namespace QuickMessenger.API.Data.Services
{

    public class UserService : IUserService
    {
        private readonly DataContext _context;
        private readonly IMapper _mapper;
        private readonly UserManager<User> _userManager;
        private readonly IUserRepo _userRepo;
        private readonly IEmailSender _emailSender;
        private readonly IPhotoService _photoService;
        private readonly IOrderRepo _orderRepo;

        public UserService(DataContext context, IMapper mapper,
        UserManager<User> userMAnager, IUserRepo userRepo, IEmailSender emailSender,
        IOptions<AuthMessageSenderOptions> optionsAccessor, IPhotoService photoService, IOrderRepo orderRepo)
        {
            _photoService = photoService;
            _emailSender = emailSender;
            _userRepo = userRepo;
            _userManager = userMAnager;
            _mapper = mapper;
            _context = context;
            _options = optionsAccessor.Value;
            _orderRepo = orderRepo;
        }

        public async Task<IdentityResult> CreateStaff(StaffDetailDto Staff)
        {
            Staff.DateRegistered = DateTime.Now;
            User userToBeCreated;
            switch (Staff.Role)
            {
                case "Admin":
                    userToBeCreated = _mapper.Map<Admin>(Staff);
                    break;
                case "FrontDesk":
                    userToBeCreated = _mapper.Map<FrontDesk>(Staff);
                    break;
                default:
                    userToBeCreated = _mapper.Map<Rider>(Staff);
                    break;
            }
            Staff.Password = await GenerateDefaultPassWord(6);
            userToBeCreated.EmailConfirmed = true;
            var rsesult = await _userManager.CreateAsync(userToBeCreated, Staff.Password);
            Staff.Id = userToBeCreated.Id;
            return rsesult;

        }

        public async Task SendStaffCreationEmail(StaffDetailDto userToBeCreated)
        {
                string[] args = new string[] { userToBeCreated.FirstName, userToBeCreated.Password, _options.SpaStaffUrl + "login" };
                string HtmlHead =   System.IO.File.ReadAllText("EmailTemplates/StaffCreation.html");
                string HtmlBody =  System.IO.File.ReadAllText("EmailTemplates/StaffCreationBody.html");
                HtmlBody = string.Format(HtmlBody, args);
                await _emailSender.SendEmailAsync(userToBeCreated.Email
                , "YOU'VE BEEN ADDED AS A STAFF OF QUICK WAKA",
                HtmlHead + HtmlBody);
        }

        public async Task SendClientRegisterationEmail(User userToBeCreated, string url)
        {
            {
                string[] args = new string[] { userToBeCreated.FirstName, url };
                string HtmlHead = await System.IO.File.ReadAllTextAsync("EmailTemplates/ClientRegistration.html");
                string HtmlBody = await System.IO.File.ReadAllTextAsync("EmailTemplates/ClientRegistrationBody.html");
                HtmlBody = string.Format(HtmlBody, args);
                await _emailSender.SendEmailAsync(userToBeCreated.Email
                , "Confirm Email Address",
                HtmlHead + HtmlBody);
            }
        }

        public AuthMessageSenderOptions _options { get; }

        private async Task<string> GenerateDefaultPassWord(int size)
        {
            return await Task.Run(() =>
            {
                char[] chars =
                "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890".ToCharArray();
                byte[] data = new byte[size];
                using (RNGCryptoServiceProvider crypto = new RNGCryptoServiceProvider())
                {
                    crypto.GetBytes(data);
                }
                StringBuilder result = new StringBuilder(size);
                foreach (byte b in data)
                {
                    result.Append(chars[b % (chars.Length)]);
                }
                return result.ToString();
            });

        }

        public async Task<bool> UpdateStaff(StaffDetailDto staff, User user, string staffPhotoPath)
        {
            bool save1 = false, save2 = false, save3 = false, save4 = false;
             if(user.Email.CompareTo(staff.Email) != 0)
            {
                save4 = true;
                user.Email = staff.Email;
                user.UserName = staff.Email;
                await _userManager.UpdateAsync(user);
            }
            if (staff.RemovePhoto)
            {
                save1 = true;
                staff.PhotoUrl = null;
                _photoService.DeletePhoto($"{staffPhotoPath}_{staff.Id}");
            }
            if (!user.GetType().Name.Equals(staff.Role))
            {
                if (await _userRepo.UpdateStaffRoleAsync(user, staff.Role))
                    save2 = true;
            }
            IEnumerable<AddressDto> staffAddresses = staff.Addresses;
            staff.Addresses = null;
            _mapper.Map(staff, user);

            List<int> deleteList = staffAddresses.Where(s => s.Deleted == true).Select(s => s.Id).ToList();
            List<int> updateList = staffAddresses.Where(s => s.Deleted == false).Select(s => s.Id).ToList();

            IEnumerable<Address> addressesToDelete = await _userRepo.GetAddressList(deleteList);
            _userRepo.DeleteAll(addressesToDelete);

            IEnumerable<Address> addressesToUpdate = await _userRepo.GetAddressList(updateList);
            foreach (AddressDto add in staffAddresses)
            {
                if (add.Id != 0)
                {
                    //update existing Address
                    var TempAdd = addressesToUpdate.FirstOrDefault(a => a.Id == add.Id);
                    _mapper.Map(add, TempAdd);
                }
                else
                {
                    //Create new Address
                    var Address = _mapper.Map<Address>(add);
                    Address.UserId = staff.Id;
                    _userRepo.Add(Address);
                }
                    
            }
           
            save3 = await _userRepo.SaveAll();
            
            return (save1 || save2 || save3 || save4);
        }

        public async Task<Client> CreateClient(ClientRegisterDto client)
        {
            Client userToBeCreated = _mapper.Map<Client>(client);
            var result = await _userManager.CreateAsync(userToBeCreated, client.Password);
            return userToBeCreated;
        }

        public async Task<Client> CreateClient(Client client)
        {
            Client userToBeCreated = _mapper.Map<Client>(client);
            var result = await _userManager.CreateAsync(userToBeCreated);
            return userToBeCreated;
        }

        public async Task SendPassswordResetEmail(User userToBeCreated, string url)
        {
            {
                string[] args = new string[] { userToBeCreated.FirstName, url };
                string HtmlHead = await System.IO.File.ReadAllTextAsync("EmailTemplates/ClientRegistration.html");
                string HtmlBody = await System.IO.File.ReadAllTextAsync("EmailTemplates/PasswordResetBody.html");
                HtmlBody = string.Format(HtmlBody, args);
                await _emailSender.SendEmailAsync(userToBeCreated.Email
                , "Password Reset Request",
                HtmlHead + HtmlBody);
            }
        }

        public async  Task ContactUS(Contact contact)
        {
             
                await _emailSender.SendEmailAsync(contact.Email
                , "inquiry-" + contact.Name ,
                contact.Message);
        }

        public async  Task SendClientOrderConfirmationEmail(User user, Cart cart, ICollection<Order> orders)
        {
                string OrderString = "";
                int orderCount = 0;
                foreach(Order o in orders)
                {
                    var order = await _orderRepo.GetOrder(o.Id);
                    orderCount++; 
                    var orderDate = order.Date.ToString("MMMM dd, yyyy");
                    var pickupAddressHtmlString = "";
                    var pickupItemHtmlString = "";
                    var pickupItemsHtmlbody = System.IO.File.ReadAllText("EmailTemplates/PickupItem.html");
                    string PickupAddressHtmlBody = System.IO.File.ReadAllText("EmailTemplates/PickupAddress.html");
                    //pickup Items
                    if(order.Type.ToLower().CompareTo("pickup") == 0)
                    {
                        
                        foreach(PickupItem pickup in order.PickupItems)
                        {
                            var pickAddArgs = new string[] {$"{pickup.Address.FirstName}  {pickup.Address.LastName} ", $"{ pickup.Address.Street}, { pickup.Address.City}, { pickup.Address.State}", pickup.Address.Phone};
                            pickupAddressHtmlString += string.Format(PickupAddressHtmlBody, pickAddArgs);
                            var pickupPhoto = "";
                            if(null != pickup.Photos && pickup.Photos.Count > 0)
                            pickupPhoto = pickup.Photos.ElementAt(0).Url;
                            var pickupItemArgs = new string[] {pickupPhoto,pickup.Name, $"{pickup.Quantity}", pickup.Size};
                            pickupItemHtmlString += string.Format(pickupItemsHtmlbody,pickupItemArgs);

                        }
                        
                    }
                    var deleiveryAddArgs = new string[] {$"{order.Address.FirstName}  {order.Address.LastName} ", $"{ order.Address.Street}, { order.Address.City}, { order.Address.State}", order.Address.Phone};
                    PickupAddressHtmlBody =  PickupAddressHtmlBody.Replace("Pickup", "Delivery");
                    var  deliveryAddressHtmlString = string.Format(PickupAddressHtmlBody, deleiveryAddArgs);
                    // purchase items 
                    var purchaseItemItemHtmlString = "";  
                    var purchaseItemHtmlString = "";
                    if(null != order.ProductOrders && order.ProductOrders.Count > 0 )
                    {
                        var purchaseItemItemHtmlbody = System.IO.File.ReadAllText("EmailTemplates/PurchaseItemItem.html");
                        foreach(ProductOrder po in order.ProductOrders)
                        {
                            var vendorName = (null != po.Product.Vendor) ? po.Product.Vendor.Name : "";
                            Photo photo = new Photo();
                            photo.Url = "";
                            var photos = po.Product.Photos;
                            if(null != photos)
                            photo = photos.ElementAt(0);
                            if(null != po.Product.Vendor)
                            {
                                var soldByHtmlBody = System.IO.File.ReadAllText("EmailTemplates/SoldBy.html");
                                vendorName = string.Format(soldByHtmlBody, new string[]{vendorName});
                            }
                            var productOrderArgs = new string[]{photo.Url, po.Product.Name, $"{po.Product.Price.FormatForMoney()}", $"{po.Quantity}", vendorName };
                            purchaseItemItemHtmlString += string.Format(purchaseItemItemHtmlbody, productOrderArgs);

                        }

                        var PurchaseItemHtmlBody = System.IO.File.ReadAllText("EmailTemplates/PurchaseItem.html");
                        purchaseItemHtmlString = string.Format(PurchaseItemHtmlBody, new string[]{purchaseItemItemHtmlString});
                    }
                    
                    string[] singleOrderArgs = new string[] {$"{orderCount}", order.TrackingId, 
                                                                cart.TrackingId,orderDate, order.Type, 
                                                                pickupAddressHtmlString, deliveryAddressHtmlString,
                                                                $"{Math.Round(order.Cost - order.DeliveryCharge,2).FormatForMoney()}",
                                                                $"{Math.Round(order.DeliveryCharge, 2).FormatForMoney()}",
                                                                $"{Math.Round(order.Cost, 2).FormatForMoney()}",
                                                                 purchaseItemHtmlString, pickupItemHtmlString};

                    var orderDetailHtmlString = System.IO.File.ReadAllText("EmailTemplates/OrderDetail.html");
                    OrderString += string.Format(orderDetailHtmlString, singleOrderArgs);
                }
                
                string[] TemplateMainCartArgs = new string[] { $"Hi {user.FirstName} {user.LastName ?? ""}, thank you for using QuickWaka!", cart.TrackingId, $"{cart.Cost.FormatForMoney()}",  OrderString};
                
                string HtmlBody =  System.IO.File.ReadAllText("EmailTemplates/OrderEmailtemplate.html");
                string HtmlBody2 =  System.IO.File.ReadAllText("EmailTemplates/AdminOrderNotificationTemplate.html");
                HtmlBody = string.Format(HtmlBody, TemplateMainCartArgs);
                var admins = await _userRepo.GetAllActiveAdmins();
                var superAdmins = await _userRepo.GetAllActiveSuperAdmins();
                await _emailSender.SendEmailAsync(user.Email
                , "Order Confirmation",
                 HtmlBody);
                 foreach(Admin admin in admins)
                 {
                     string[] TemplateMainCartArgs2 = new string[] { $"Hello {admin.FirstName}, A customer:  {user.FirstName} {user.LastName ?? ""} has placed new order(s)", cart.TrackingId, $"{cart.Cost}",  OrderString};
                     HtmlBody2 = string.Format(HtmlBody2,TemplateMainCartArgs2);
                     _emailSender.SendEmailAsync(admin.Email, "A New Order Has been placed", HtmlBody2);
                 }

                 foreach(SuperAdmin admin in superAdmins)
                 {
                     string[] TemplateMainCartArgs2 = new string[] { $"Hello {admin.FirstName}, A customer:  {user.FirstName} {user.LastName ?? ""} has placed new order(s)", cart.TrackingId, $"{cart.Cost}",  OrderString};
                     HtmlBody2 = string.Format(HtmlBody2,TemplateMainCartArgs2);
                     _emailSender.SendEmailAsync(admin.Email, "A New Order Has been placed", HtmlBody2);
                 }

        }

        public async Task<bool> UpdateClientLastLogin(User user)
        {
            user.LastActive = DateTime.Now;
            var result = await _userManager.UpdateAsync(user);
            return result.Succeeded;
        }
    }
}
