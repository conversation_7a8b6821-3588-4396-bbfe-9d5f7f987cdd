using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using QuickMessenger.API.Data.DTO.Product;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Helpers;
using QuickMessenger.API.Helpers.Params;

namespace QuickMessenger.API.Data.IRepo
{
    public interface IProductRepo
    {

        Task<List<Product>> GetProductListWithVendorAndAddress(List<int> Ids);
        Task<Service>GetNoLazyService(int id);
        Task<Product> GetProductWithVendor(int Id);
        Task<PagedList<Prod_Category>> GetCategories(Prod_CategoryParams ProdCatParam);
        Task<PagedList<Service>> GetServices(ServiceParams ServiceParam);
        Task<ICollection<Service>> GetAllServices();
        Task<ICollection<Prod_Category>> GetCategories( );
        Task<PagedList<Prod_PropertyType>> GetPropertyTypes(Prod_PropertyTypeParams Prod_PropertyTypeParams);

        void Add<T>(T entity) where T: class;

        void AddCategory(Prod_Category category);

        void AddAll<T>(IEnumerable<T> entities) where T: class;

        void Delete<T>(T entity) where T: class;
        void DeleteAll<T>(IEnumerable<T> entities) where T:class;
        Task<bool> SaveAll();
        Task<bool> CategoryHasRecords(int Id);
        Task<bool> PropertyTypeHasRecords(int Id);
        Task<bool> MeasurementTypeHasRecords(int Id);
        Task<Service> GetServiceWithPhotos(int Id);
        Task<bool> ServiceCanBeDeleted(int id);
        Task<Service> GetService(int Id);
        Task<Service> GetService(string ServiceNameId);
        Task<Service> GetServiceWithoutPhotos(int Id);
        Task<Service> GetDefaultGenericService();
        Task<Prod_Category> GetCategory(int Id);
        Task<Prod_Category> GetCategoryWithoutParent(int Id);
        Task<Prod_Category> GetTrackedCategory(int Id);
        Task<Prod_PropertyType> GetPropertyType(int Id);
        Task<Prod_PropertyType> GetTrackedPropertyType(int Id);
        Task<Prod_MeasurementType> GetMeasurementType(int Id);
        Task<Prod_MeasurementType> GetTrackedMeasurementType(int Id);
        Task<IEnumerable<Prod_PropertyType>> GetPropertyTypes();
        Task<IEnumerable<Prod_MeasurementType>> GetMeasurementTypes();
        Task<PagedList<Prod_MeasurementType>> GetMeasurementTypes(Prod_MeasurementTypeParam measurementTypeParam);
        Task<bool> Prod_PropertyTypeNameExists(string Name);
        Task<bool> Prod_PropertyTypeNameExists(string Name, int Id);
        Task<bool> Prod_MeasurementTypeNameExists(string Name);
        Task<bool> Prod_MeasurementTypeNameExists(string Name, int Id);
        Task<bool> CategoryNameIdExists(string NameId);
        Task<bool> ServiceNameExists(string Name);
        Task<bool> ServiceNameExists(string Name, int Id );
        Task<ICollection<Property>> GetProperties(ICollection<Prod_PropertyDto> prod_Properties);

            
         Task<ICollection<ProductProperty>> GetProductProperties(int PropertyCategoryId, int ProductCategoryId);
         //get the properties of products that belong a particular productcategory and also whose properties belong to aother product category
        Task<bool> CategoryNameIdExists(string NameId,int Id);
        Task<ICollection<Property>> GetProperties(List<int> Ids);
        Task<ICollection<Property>> GetCategoryProperties(int CategoryId);
        Task<IEnumerable<Property>> GetPropertyTypeProperties(int CategoryId);
        Task<IEnumerable<Property>> GetMeasurementTypeProperties(int CategoryId);
        Task<ICollection<Property>> GetCategoryPropertiesWithRelations(int CategoryId);
        Task<ICollection<Prod_Category>>  GetCanBeParentCategories();
        Task<bool>  AnyPropertyHasRecord(List<int> propertyIds);
        Task<bool>  AnyPropertyExists(IEnumerable<Property> Properties, Prod_Category Category,List<int> IdsToBeDeleted);
        Task<PagedList<Product>> GetInactiveProducts(ProductParams productParams);
        Task<PagedList<Product>> GetProducts(ProductParams productParams);
        //This method below is suppose to return products, top parent categories, child categories, and category for a particular serviceId
        Task<Tuple<PagedList<Product>,List<Prod_Category>, Prod_Category, double, double>>GetProducts(ServiceProductParams productParams);
        Task<Tuple<PagedList<Product>,List<Prod_Category>, Prod_Category, double, double>>GetProducts(ServiceVendorProductParams productParams);
        Task<Tuple<PagedList<Product>,List<Prod_Category>, Prod_Category, double, double>>GetProducts(VendorProductParams productParams);

         Task<Tuple<PagedList<Product>,List<Prod_Category>, Prod_Category, double, double>>GetProducts(ClientProductParams productParams);
        Task<ICollection<Prod_Category>> GetCategoriesByService(string NameId);
        Task<ICollection<Prod_Category>> GetCategoriesBySearch(string Search);
        Task<ICollection<Prod_Category>> GetCategoriesByVendor(int Id);
       // Task<PagedList<Product>> GetProducts(ClientProductParams productParams);
        Task<PagedList<Product>> GetAllProducts(ProductParams productParams);
        Task<Product> GetProduct(int Id);
        Task<Product> GetProductForUpdate(int Id);
        Task<ICollection<ProductPropertyDto>> GetProductProperties(int Id);
        Task<bool> ProdcutHasRecords (int Id);
        Task<ICollection<Product>> GetInActiveProducts();
        Task<ICollection<Product>> GetActiveProducts();
        Task<bool> ProductNameExistsInCategory(string name, Prod_Category category);
        Task<bool> ProductNameExistsInCategory(string name, Prod_Category category, int productId);
        Task<bool> ProductNameExistsInVendor(string name, Vendor vendor, Prod_Category category);
        Task<bool> ProductNameExistsInVendor(string name, Vendor vendor, Prod_Category category, int Id);
        Task<ICollection<ProductProperty>> GetProductProperties(IEnumerable<int> list, int Id);
        Task<ICollection<ServiceDto2>> GetTop6ServicesWithMostOrders();
         Task<ICollection<ServiceDto2>> GetServicesWithPictures();
         Task<ICollection<Product>> GetProducts(string Search);

         Task<Tuple<double, double>> GetMinMaxPriceForAllProducts();
         Task<Tuple<double, double>> GetMinMaxPriceForAllProductsByVendor(int  nameId);
         Task<Tuple<double, double>> GetMinMaxPriceForAllProductsByService(string nameId);
         Task<Tuple<double, double>> GetMinMaxPriceForSearch(string SearchTerm);
    }
}