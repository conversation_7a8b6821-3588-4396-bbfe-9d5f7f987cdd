using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using QuickMessenger.API.Data.DTO.Product;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Helpers.Params;
using QuickMessenger.API.Helpers;
using QuickMessenger.API.Data.IService;
using Microsoft.AspNetCore.Http;
using System.Text.RegularExpressions;

namespace QuickMessenger.API.Controllers.Client
{
    [ApiController]
    [AllowAnonymous]
    [Route("api/client/[controller]")]
    public class ProductController: ControllerBase
    {  private static string _photoPath = "qm/product/product";
        private static string _servicePhotoPath = "product/service";
        private static int maxPhotos = 5;
        private readonly IProductService _productService;
         private readonly IVendorRepo _vendorRepo;
        private readonly IProductRepo _productRepo;
        private readonly IMapper _mapper;
        private readonly IUserRepo _userRepo;

        public ProductController(IVendorRepo vendorRepo, IProductRepo productRepo, IMapper mapper, IUserRepo userRepo, IProductService productService)
        {
            this._productService = productService;
            this._mapper = mapper;
            this._userRepo = userRepo;
            this._productRepo = productRepo;
            this._vendorRepo = vendorRepo;

        }
        
        [HttpPost]
        public async Task<IActionResult> GetProducts([FromQuery]ClientProductParams Param){
                
                var products = await _productRepo.GetProducts(Param);
               
                  var objToreturn = new
                    {
                        Products = _mapper.Map<IEnumerable<ProductListForClientDto>>(products.Item1),
                        Pagination = new
                        {
                            CurrentPage = products.Item1.CurrentPage,
                            PageSize = products.Item1.PageSize,
                            TotalCount = products.Item1.TotalCount,
                            TotalPages = products.Item1.TotalPages
                        },
                        ChildrenCategories = _mapper.Map<List<Prod_CategoryListDtoLite2>>(products.Item2),
                        MinPrice = products.Item4,
                        MaxPrice = products.Item5,
                        Category = _mapper.Map<Prod_CategoryListDtoLite2>(products.Item3),
                       // Service = _mapper.Map<ServiceLiteDto>(service)
                    };

            return Ok(objToreturn);
              
        }
    
        [HttpGet("{Id}")]
        public async Task<IActionResult> GetProduct(int Id)
        {
            var product = await _productRepo.GetProduct(Id);
            if(product == null)
            return BadRequest();
            var productToReturn = _mapper.Map<ProductDto>(product);
            //format description to have html and text 
            {
                productToReturn.HmtlDescription = $@"
                    <html>
                    <head>
                        <style>
                        body {{
                            font-family: Arial, sans-serif;
                            font-size: 16px;
                            color: #333333;
                        }}
                        </style>
                    </head>
                    <body>
                        {(string.IsNullOrEmpty( productToReturn.Description) ? "" : productToReturn.Description)}
                    </body>
                    </html>
                    ";
             productToReturn.TextDescription = Regex.Replace((string.IsNullOrEmpty( productToReturn.Description) ? "" : productToReturn.Description), @"<[^>]+>", "")
             .Replace("&nbsp", "");
             
            }

            productToReturn.ProductProperties = await _productRepo.GetProductProperties(Id);
            return Ok(productToReturn);
        }

         [HttpPost("top_prod_categories/{Search}")]
         public async Task<IActionResult> getAllTopProductCategoriesbySearch(string Search)
        {
            var categories = await _productRepo.GetCategoriesBySearch(Search);
               
                 var objToreturn = new
                    {
                        categories = _mapper.Map<ICollection<Prod_CategoryListDtoLite2>>(categories)
                    };

            return Ok(objToreturn);
        }

        [HttpPost("minmaxprice")]

        public async Task<IActionResult> GetMinMaxPriceForAllProducts(){

            var result = await _productRepo.GetMinMaxPriceForAllProducts();
            return Ok(new {
                MinPrice = result.Item1,
                MaxPrice = result.Item2,
            });
        }

        [HttpPost("search/minmaxprice")]
        public async Task<IActionResult> GetMinMaxPriceForAllProducts(string SearchTerm){

            var result = await _productRepo.GetMinMaxPriceForSearch(SearchTerm);
            return Ok(new {
                MinPrice = result.Item1,
                MaxPrice = result.Item2,
            });
        }

        //update product details for vendor admin
        [HttpPut("{Id}/update")]
        public async Task<IActionResult> UpdateProduct(Product_ClientDto productDto, int Id)
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var user = await _userRepo.GetClient(userId);
            var productFromRepo = await _productRepo.GetProduct(Id);
            if(user.VendorId == productFromRepo.VendorId)
            {
                    productFromRepo.Name = productDto.Name;
                    productFromRepo.OutOfStock = productDto.OutOfStock;
                    productFromRepo.Price = productDto.Price;
                    productFromRepo.Description = productDto.Description;
                    productFromRepo.SearchParam = $"{productDto.Name}".ToUpper();
                    productFromRepo.UniqueParam = productDto.Name.RemoveSpecialCharatcters().ToUpper();
                    await _productRepo.SaveAll();
                    return Ok();
            }else 
            return Unauthorized();  
        }

        [HttpPost("{Id}/addPhoto")]
        public async Task<IActionResult> AddProductPhoto([FromForm] IFormFile file, int Id){
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
            var user = await _userRepo.GetClient(userId);
            var productFromRepo = await _productRepo.GetProduct(Id);
            if(user.VendorId == productFromRepo.VendorId){

                 if(  await _productService.AddProductPhoto(file, Id, _photoPath, maxPhotos))
                    return
                        NoContent();
            
                    return BadRequest(new { Error = "Could not upload Product photo" });
            }
            else 
            return Unauthorized();  
        }

    }
}