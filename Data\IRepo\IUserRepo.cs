using System.Collections.Generic;
using System.Threading.Tasks;
using QuickMessenger.API.Data.DTO.Staff;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Helpers;
using QuickMessenger.API.Helpers.Params;

namespace QuickMessenger.API.Data.IRepo
{
    public interface IUserRepo
    {
         Task<PagedList<User>> GetStaffLazyAddress(UserParam UserParam, int UserId);
         Task<PagedList<Client>> GetClientLazyAddress(ClientListParams UserParam);
         Task<PagedList<Client>> GetAllClientLazyAddress(ClientListParams UserParam);
         Task<PagedList<Client>> GetDeactivtedClientLazyAddress(ClientListParams UserParam, int UserId);
         Task<User> GetAllUserAddresses(int Id);
          Task<ICollection<App_BannerLink>> GetAllActiveAppBannerLinks();
         Task<User> GetStaff(int Id);
         Task<Address> GetAddress(int Id);
         Task<User> GetUserByEmailOrPhone(string email, string phoe); 
         Task<User> GetUserByEmail(string email); 
         Task<User> GetUserByPhone( string phoe); 
         Task<User> GetUser(int Id);
         Task<User> GetDiffUserWithEmailOrPhone(string email, string phone, int Id);
         Task<IEnumerable<Address>> GetAddressList(List<int> Ids);
         Task<bool> UpdateStaffRoleAsync(User Staff, string Role);
         void Add<T>(T entity) where T: class;
         void Delete<T>(T entity) where T: class;
         Task<bool> UserHasRecords(int Id);
         void DeleteAll<T>(IEnumerable<T> entities) where T:class;
         Task<bool> SaveAll();

         Task<ICollection<Rider>> GetAllActiveRiders();
          Task<ICollection<Admin>> GetAllActiveAdmins();
          Task<ICollection<SuperAdmin>> GetAllActiveSuperAdmins();
         Task<Address> GetStaffAddress(int Id);
         Task<bool> HasPendingCart(int userId);
         Task<User> LazyGetStaff(int Id);
         Task<Client> GetClientLazy(int Id);
         Task<User> GetClient(int Id);
         Task<Rider> GetRider(int Id);
         Task<bool> AddressHasOrder(int Id);
         Task<User> GetClientOnlyDetail(int Id);
        Task<User> GetLastDeletedUser();
    }
}