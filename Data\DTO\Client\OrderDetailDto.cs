using System;
using System.Collections.Generic;
using QuickMessenger.API.Data.DTO.Product;
using QuickMessenger.API.Data.DTO.Shared;
using QuickMessenger.API.Data.DTO.Staff;

namespace QuickMessenger.API.Data.DTO.Client
{
    public class OrderDetailDto
    {
         public int Id { get; set; }
        public ClientDetailDto Client { get; set; }
        public AddressDto2 Address { get; set; }
        public DateTime Date { get; set; }
        public DateTime PickupTime { get; set; }
        public StaffDetailDto Rider { get; set; }
        public string State { get; set; }
        public ICollection<PickupItemDto> PickUpItems { get; set; }
        public string Type { get; set; }
        public ICollection<ProductOrderDto> ProductOrders { get; set; }
        public double Cost { get; set; }
        public double DeliveryCharge { get; set; }
        public string TrackingId { get; set; }
        public string CartTrackingId { get; set; }
        public DateTime TimeDelivered { get; set; }
         public ServiceLiteDto2 Service { get; set; }
    }

    public class OrderDetailDto2
    {
         public int Id { get; set; }
        //public ClientDetailDto Client { get; set; }
        public AddressDto Address { get; set; }
        public DateTime Date { get; set; }
        public DateTime PickupTime { get; set; }
        public StaffDetailDto Rider { get; set; }
        public string State { get; set; }
        public ICollection<PickupItemDto2> PickUpItems { get; set; }
        public string Type { get; set; }
        public ICollection<ProductOrderDto> ProductOrders { get; set; }
        public double Cost { get; set; }
        public double DeliveryCharge { get; set; }
        public string TrackingId { get; set; }
        public DateTime TimeDelivered { get; set; }
    }
}