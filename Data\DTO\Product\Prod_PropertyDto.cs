using System.ComponentModel.DataAnnotations;

namespace QuickMessenger.API.Data.DTO.Product
{
    public class Prod_PropertyDto
    {
        public int Id { get; set; }
        public int Prod_CategoryId { get; set; }
        [Required]
        public int Prod_PropertyTypeId { get; set; }
        public int? Prod_MeasurementTypeId { get; set; }
        public string  PropertyTypeName { get; set; }
        public string  MeasurementTypeSymbol { get; set; }
        public string  MeasurementTypeName { get; set; }
        public bool CanDelete { get; set; }

    }
}