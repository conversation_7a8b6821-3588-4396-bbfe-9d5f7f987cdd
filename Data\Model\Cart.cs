using System;
using System.Collections.Generic;

namespace QuickMessenger.API.Data.Model
{
    public class Cart
    {
        public int Id { get; set; }
        public string TrackingId { get; set; }
        public ICollection<Order> Orders { get; set; }
        public DateTime Date { get; set; }
        public User Client  { get; set; }
        public int ClientId { get; set; }
        //States are: Pending, Paid. Delivered. Closed.
        public string State { get; set; }
        public double Cost { get; set; }
        public string Tx_ref { get; set; }
        public DateTime? DatePaid { get; set; }
    
    }
}