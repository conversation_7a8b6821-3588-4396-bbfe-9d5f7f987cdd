//using System.Data.SqlClient;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using QuickMessenger.API.Data.DTO.Shared;
using QuickMessenger.API.Data.DTO.Staff;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.IService;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Helpers;
using QuickMessenger.API.Helpers.Params;

namespace QuickMessenger.API.Data.Repo
{
    public class UserRepo : IUserRepo
    {
        private readonly DataContext _context;
        private readonly IMapper _mapper;
        private readonly IPhotoService _photoService;

        public UserRepo(DataContext context, IMapper mapper, IPhotoService photoService)
        {
            _photoService = photoService;
            _mapper = mapper;
            _context = context;

        }

        public void Add<T>(T entity) where T : class
        {
            _context.Add(entity);
        }

        public void Delete<T>(T entity) where T : class
        {
            _context.Remove(entity);
        }

        public void DeleteAll<T>(IEnumerable<T> entites) where T : class
        {
            _context.RemoveRange(entites);
        }

        public async Task<PagedList<User>> GetStaffLazyAddress(UserParam UserParam, int UserId)
        {
            var staff = _context.Users.AsEnumerable()
            .Where(u => UserParam.Roles.Contains(u.GetType().Name)
             && u.Id != UserId && u.Deactivated == UserParam.Deactivated);

            if (!string.IsNullOrEmpty(UserParam.SearchTerm))
                staff = staff.Where(u => u.SearchParam.Contains(UserParam.SearchTerm.ToUpper()));

            staff = staff.OrderBy(u => u.LastName);
            return  PagedList<User>.Create(staff, UserParam.PageNumber, UserParam.PageSize);

        }


        public async Task<User> GetStaff(int Id)
        {
            return await _context.Users.
            AsQueryable().FirstOrDefaultAsync(u => u.Id == Id);
        }

        public async Task<User> LazyGetStaff(int Id)
        {
            return await _context.Users.Include(u => u.Addresses)
            .FirstOrDefaultAsync(u => u.Id == Id && !u.Deactivated);
        }

        public async Task<bool> SaveAll()
        {
            return await _context.SaveChangesAsync() > 0;
        }

    
        public async Task<bool> UpdateStaffRoleAsync(User Staff, string Role)
        {

            int result = await _context.Database.ExecuteSqlCommandAsync(
                    $"UPDATE Users SET Discriminator={Role} WHERE Id={Staff.Id}"
                    //new SqlParameter("@StaffId", Staff.Id), 
                    //new SqlParameter("@Role", Role)
                    );
            return true;
        }

        public async Task<Address> GetAddress(int Id)
        {
            return await _context.Addresses.AsQueryable().FirstOrDefaultAsync(a => a.Id == Id);
        }

        public async Task<IEnumerable<Address>> GetAddressList(List<int> Ids)
        {
            return await _context.Addresses.AsQueryable().Where(a => Ids.Contains(a.Id)).ToListAsync(); 
        }

        public async Task<bool> UserHasRecords(int Id)
        {
            return 
             await _context.Orders.AsQueryable().AnyAsync(o => o.ClientId == Id);

        }

        public async Task<User> GetUserByEmailOrPhone(string email, string phone)
        {
            return await _context.Users.AsQueryable().Where(u => u.Email == email 
                    || u.PhoneNumber == phone).FirstOrDefaultAsync();
        }

         public async Task<User> GetUser(int Id)
        {
            return await _context.Users.AsQueryable().Where(u => u.Id ==
                    Id).FirstOrDefaultAsync();
        }

        public async Task<User> GetUserByEmail(string email)
        {
            return await _context.Users.AsQueryable().Where(u => u.Email == email )
            .FirstOrDefaultAsync();
        }

        public async Task<User> GetUserByPhone( string phone)
        {
            return await _context.Users.AsQueryable().Where(u => u.PhoneNumber == phone).FirstOrDefaultAsync();
        }

        public async Task<User> GetDiffUserWithEmailOrPhone(string email, string phone, int Id)
        {
            return await _context.Users.AsQueryable().Where(u => (u.Email == email 
                    || u.PhoneNumber == phone) && u.Id != Id).FirstOrDefaultAsync();
        }

        public async Task<Address> GetStaffAddress(int Id)
        {
           return await _context.Addresses.AsQueryable().Where( add => add.UserId ==Id)
           .FirstOrDefaultAsync();
        }

        public async   Task<PagedList<Client>> GetClientLazyAddress(ClientListParams UserParam)
        {
            
            var client = _context.Clients.Include(u => u.Addresses)
            .AsQueryable().Where(u => u.Deactivated == UserParam.Deactivated);

            if (!string.IsNullOrEmpty(UserParam.SearchTerm))
            {
                UserParam.SearchTerm = UserParam.SearchTerm.ToUpper();
                client = client.AsQueryable().Where(u => u.SearchParam.Contains(UserParam.SearchTerm));
            }    

            client = client.OrderBy(u => u.LastName);
            return await PagedList<Client>.CreateAsync(client, UserParam.PageNumber, UserParam.PageSize);
        }

        public async Task<User> GetAllUserAddresses(int Id){
            var user = await _context.Users.AsQueryable().Where(u => u.Id == Id)
            .Include(u => u.Addresses)
            .FirstOrDefaultAsync();

            return user;
        }
        public async   Task<PagedList<Client>> GetAllClientLazyAddress(ClientListParams UserParam)
        {
            var client = _context.Clients.Include(u => u.Addresses)
            .AsQueryable().Where(u => true);

            if (!string.IsNullOrEmpty(UserParam.SearchTerm))
                client = client.AsQueryable().Where(u => u.SearchParam.Contains(UserParam.SearchTerm.ToUpper()));


            client = client.OrderBy(u => u.LastName);
            return await PagedList<Client>.CreateAsync(client, UserParam.PageNumber, UserParam.PageSize);
        }


        public async Task<PagedList<Client>> GetDeactivtedClientLazyAddress(ClientListParams UserParam, int UserId)
        {
            var client = _context.Clients.Include(u => u.Addresses)
            .AsQueryable().Where(u => u.Deactivated);

            if (!string.IsNullOrEmpty(UserParam.SearchTerm))
                client = client.AsQueryable().Where(u => u.SearchParam.Contains(UserParam.SearchTerm.ToUpper()));


            client = client.OrderBy(u => u.LastName);
            return await PagedList<Client>.CreateAsync(client, UserParam.PageNumber, UserParam.PageSize);
        }

        public async Task<Client> GetClientLazy(int Id)
        {
            return await _context.Clients.Include(c => c.Addresses)
            .Include(c => c.Orders)
            .Where(c => c.Id == Id).FirstOrDefaultAsync();
            
        }

        public async Task<User> GetClient(int Id)
        {
            return await _context.Users.Include(c => c.Addresses)
            .Where(c => c.Id == Id).FirstOrDefaultAsync();
        }

         public async Task<User> GetClientOnlyDetail(int Id)
        {
            return await _context.Users
            .AsQueryable().Where(c => c.Id == Id).FirstOrDefaultAsync();
        }
        public async Task<ICollection<Rider>> GetAllActiveRiders()
        {
            return await _context.Riders.AsQueryable().Where(r => !r.Deactivated).ToListAsync();
        }

        public async Task<bool> HasPendingCart(int userId)
        {
           return await _context.Carts.AsQueryable().Where(c => c.ClientId == userId && c.State.CompareTo("Pending") == 0).AnyAsync();
        }

        public async Task<Rider> GetRider(int Id)
        {
           return await _context.Riders.AsQueryable().Where(r => r.Id == Id).FirstOrDefaultAsync();
        }

        public async Task<bool> AddressHasOrder(int Id)
        {
           return await _context.Addresses.AsQueryable().Where(a => a.Id == Id).AnyAsync();
        }

        public async Task<ICollection<Admin>> GetAllActiveAdmins()
        {
           return await _context.Admins.AsQueryable().Where(o => !o.Deactivated ).ToListAsync();
        }

        public async Task<ICollection<SuperAdmin>> GetAllActiveSuperAdmins()
        {
           return await _context.SuperAdmins.AsQueryable().Where(o => !o.Deactivated ).ToListAsync();
        }

        public async Task<ICollection<App_BannerLink>> GetAllActiveAppBannerLinks()
        {
           return await _context.AdLinks.AsQueryable().Where(p => p.IsActive == true).ToListAsync();
        }

        public async Task<User> GetLastDeletedUser()
        {
            var lastDeleted = await _context.Users.AsQueryable()
            .Where(u => u.Deleted).OrderByDescending(p => p.UserName).FirstOrDefaultAsync();
            return lastDeleted;
        }
    }
}