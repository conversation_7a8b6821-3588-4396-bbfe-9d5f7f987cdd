using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Http;
using QuickMessenger.API.Data.DTO.Shared;

namespace QuickMessenger.API.Data.DTO.Staff
{
    public class StaffDetailDto
    {
        public int Id { get; set; }
        
        [Required]
        [EmailAddress]
        public string Email { get; set; }
        [Required]
        public string FirstName { get; set; }
        [Required]
        public string LastName { get; set; }
        [Required]
        [Phone]
        public string Phone { get; set; }
        [Required]
        public string Gender { get; set; }
        [Required]
        public string Role { get; set; }
        public bool RemovePhoto{get;set;}
        public string Password { get; set; }
        public string PhotoUrl { get; set; }
        public ICollection<AddressDto> Addresses { get; set; }
        public bool Deactivated { get; set; }
        [NotMapped]
        public DateTime DateRegistered { get; set; }
        [NotMapped]
        public DateTime LastActive { get; set; }

        public StaffDetailDto()
        {
            this.LastActive = DateTime.Now;
        }

    }

    public class StaffUpdatePassWord{
        [Required]
        public int Id { get; set; }
        [Required]
        public string UserName { get; set; }
        [Required]
        [StringLength(255,MinimumLength = 6)]
        [DataType(DataType.Password)]
        public string CurrentPassword { get; set; }
        [Required]
        [DataType(DataType.Password)]
        [StringLength(255,MinimumLength = 6)]
        public string NewPassword { get; set; }
        [Required]
        [StringLength(255, MinimumLength = 6)]
        [DataType(DataType.Password)]
        [Compare("NewPassword")]
        public string ConFirmNewPassWord { get; set; }
    }
}