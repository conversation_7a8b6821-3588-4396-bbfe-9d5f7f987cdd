using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using QuickMessenger.API.Data.IRepo;
using QuickMessenger.API.Data.Model;
using QuickMessenger.API.Data.DTO.Shared;
using QuickMessenger.API.Helpers;
using QuickMessenger.API.Helpers.Params;

namespace QuickMessenger.API.Data.Repo
{
    public class VendorRepo : IVendorRepo
    {
        private readonly DataContext _context;
        public VendorRepo(DataContext context)
        {
            _context = context;

        }

        public void Add<T>(T entity) where T : class
        {
            _context.Add(entity);
        }

        public void AddAll<T>(IEnumerable<T> entities) where T : class
        {
           _context.AddRange(entities);
        }

        public void Delete<T>(T entity) where T : class
        {
            _context.Remove(entity);
        }

        public void DeleteAll<T>(IEnumerable<T> entities) where T : class
        {
            _context.RemoveRange(entities);
        }

        public async Task<ICollection<Vendor>> GetTop6endorsWithMostOrders()
        {
            var vendors =  _context.ProductOrders.AsQueryable().Where(v => v.Product.Vendor != null)
            .Include(o => o.Product).ThenInclude(o => o.Vendor)
            .AsEnumerable()
            .GroupBy(v => v.Product.Vendor)
            .OrderByDescending(v => v.Count())
            .Select(
                vendor => new Vendor {
                        Id = vendor.Key.Id,
                        Name = vendor.Key.Name,
                        Address = vendor.Key.Address,
                        Logo = vendor.Key.Logo
                }

            )
            .Take(6)
            .ToList();
            
            if(vendors.Count == 6)
                return vendors;
            else {
                return await _context.Vendors.AsQueryable().Select(
                    vendor => new Vendor {
                            Id = vendor.Id,
                            Name = vendor.Name,
                            Address = vendor.Address,
                            Logo = vendor.Logo
                    }
                ).Take(6).ToListAsync();
            }
        }

        public async Task<Vendor> GetVendor(int Id)
        {
            return await _context.Vendors.AsQueryable().Where((v => v.Id == Id))
            .Include(ve => ve.Logo).Include(ve => ve.Address)
            .Select(v => new Vendor{
                Id = v.Id,
                Name = v.Name,
                Description = v.Description,
                Address = v.Address,
                Email = v.Email,
                Phone = v.Phone,
                Phone2 = v.Phone2,
                Logo = v.Logo,
                CanDelete = ! v.Products.Any(),
                State = v.State

            } )
            .FirstOrDefaultAsync();
        }

        public async Task<Address> GetVendorAddress(Vendor vendor)
        {
            return await _context.Addresses
            .AsQueryable().Where(v => v.VendorId == vendor.Id).FirstOrDefaultAsync();
        }

        public async Task<Photo> GetVendorPhoto(Vendor vendor)
        {
            return await _context.Photos
            .AsQueryable().Where( p => p.VendorId == vendor.Id).FirstOrDefaultAsync();
        }

        public async Task<PagedList<Vendor>> GetVendors(VendorParams vendorParams)
        {
            if(!string.IsNullOrEmpty(vendorParams.SearchTerm))
            {
                vendorParams.SearchTerm = vendorParams.SearchTerm.ToUpper().Trim();
                var vendors =   _context.Vendors
                .AsQueryable().Where(v => v.SearchParam.Contains(vendorParams.SearchTerm))
                .Include(p => p.Logo)
                .Include(v => v.Address)
                .OrderBy(v => v.Name);

                return await PagedList<Vendor>.CreateAsync(vendors, vendorParams.PageNumber, vendorParams.PageSize);
            }

            var vendors2 = _context.Vendors.Include(p => p.Logo)
            .Include(v => v.Address).OrderBy(v => v.Name);

            return await PagedList<Vendor>
            .CreateAsync(vendors2, vendorParams.PageNumber, vendorParams.PageSize);

        }

        public async Task<ICollection<Vendor>> GetVendors()
        {

           return await _context.Vendors.Include(p => p.Logo)
            .Include(v => v.Address).OrderBy(v => v.Name).ToListAsync();

        }

        public async Task<ICollection<Vendor>> GetVendors(string Search)
        {
            Search = Search.ToUpper().Trim();
            var vendors = await _context.Vendors
            .AsQueryable().Where(v => v.SearchParam.Contains(Search) ) 
            .Include(v => v.Address)
            .Include(v => v.Logo)
            .ToListAsync();
            return vendors;
        }

        public async Task<Vendor> GetVendorWithoutRelation(int Id)
        {
          return await  _context.Vendors.AsQueryable().Where(v => v.Id == Id).FirstOrDefaultAsync();
        }

        public async Task<bool> Save()
        {
             return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> SaveAll()
        {
            return await _context.SaveChangesAsync() > 0; 
        }

        public async Task<bool> VendorHasRecords(int Id)
        {
            return await _context.Products.AsQueryable().Where(p => p.VendorId == Id).AnyAsync();
        }

        public async Task<bool> VendorNameAndAddressExists(int Id, string uniqueParam)
        {
           
          return await _context.Vendors
            .AsQueryable().Where(v => v.UniqueParam.CompareTo(uniqueParam) == 0 && v.Id != Id).AnyAsync();  
        }

        public async Task<bool> VendorNameAndAddressExists(string uniqueParam)
        {
            
          return await _context.Vendors
            .AsQueryable().Where(v => v.UniqueParam.CompareTo(uniqueParam) == 0 ).AnyAsync();         

        }

        public async Task<PagedList<Vendor>> GetVendors(ClientVendorParams param)
        {
            var Ivendors = _context.Vendors.AsQueryable().Where(v => !v.Deactivated);

            if(!string.IsNullOrEmpty(param.SearchTerm))
            {
                param.SearchTerm = param.SearchTerm.ToUpper().Trim();
                 Ivendors = Ivendors.AsQueryable().Where(f => f.SearchParam.Contains(param.SearchTerm));
            }

            if((null != param.Cities && param.Cities.Count() > 0) || !string.IsNullOrEmpty( param.State))
            { 
                Ivendors = Ivendors.AsQueryable().Where(f => null != param.Cities && param.Cities.Contains(f.Address.City) 
                                                        || (
                                                            null != param.State && 
                                                            param.State.CompareTo(f.Address.State) == 0));
            }

            var vendors = Ivendors.Include(p => p.Logo)
                .Include(v => v.Address)
                .OrderBy(v => v.Name);

          return await PagedList<Vendor>.CreateAsync(vendors, param.PageNumber, param.PageSize);
        }

        public async Task<Vendor> GetVendorByProductId(int ProductId){
            var vendor = await  _context.Products.AsQueryable().Where(p => p.Id == ProductId)
                                            .Select(p => p.Vendor).FirstOrDefaultAsync();
                                            return vendor;
        }

          public async Task<PagedList<Vendor>> GetVendorsByService(int ServiceId, ClientVendorParams param)
        {
            var Ivendors = _context.Products.AsQueryable().Where(v => !v.Deactivated && v.ServiceId == ServiceId 
                            && v.Vendor != null);
                                            // .Select(p => p.Vendor);

            if(!string.IsNullOrEmpty(param.SearchTerm))
            {
                param.SearchTerm = param.SearchTerm.ToUpper().Trim();
                 Ivendors = Ivendors.AsQueryable().Where(f => f.Vendor.SearchParam.Contains(param.SearchTerm));
            }

            if(null != param.Cities && param.Cities.Count() > 0)
            { 
                Ivendors = Ivendors.AsQueryable().Where(f => param.Cities.Contains(f.Vendor.Address.City));
            }

           // Applying Distinct first
        var distinctVendors = Ivendors.Select(p => p.Vendor).Distinct();

        // Then include related entities and apply ordering
        var vendors = distinctVendors.Include(v => v.Logo)
                            .Include(v => v.Address)
                            .OrderBy(v => v.Id);

          return await PagedList<Vendor>.CreateAsync(vendors, param.PageNumber, param.PageSize);
        }

         public async Task<ICollection<string>> GetDistinctCitiesForParams(ClientVendorParams param)
        {
            var Ivendors = _context.Vendors.AsQueryable().Where(v => !v.Deactivated);

            if(!string.IsNullOrEmpty(param.SearchTerm))
            {
                param.SearchTerm = param.SearchTerm.ToUpper().Trim();
                 Ivendors = Ivendors.AsQueryable().Where(f => f.SearchParam.Contains(param.SearchTerm));
            }

            var cities = await  Ivendors.Select(d => d.Address.City).Distinct().ToListAsync();
                return cities;
          
        }

        

        // public async Task<ICollection<Vendor>>GetVendorsByService(int Id)
        // {
        //     var vendors = await _context.Products.AsQueryable()
        //     .Where(c => c.ServiceId == Id)
        //     .Select(c => c.Vendor).Distinct().ToListAsync();
        //     return vendors;
        // }
    }
}