using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using QuickMessenger.API.Data.DTO.Staff;
using QuickMessenger.API.Data.DTO.ThirdParty;
using QuickMessenger.API.Data.Model;

namespace QuickMessenger.API.Data.IService
{
    public interface IOrderService
    {
         Task<double> GetTotalCartCost(int Id);
         Task<double> GetTotalCartCost(Cart Cart);
         Task<DailyOrderKpiDto> GetOrderKpiReportWithDate(DateTime Date);
         Task<ICollection<HourlyOrderKpiDto>> GetHourlyOrderKpiReportWithDate(DateTime Date); 
         Task<RangeOrderKpiDto> GetOrderKpiReportWithDateRange(DateTime start, DateTime end);
         Task<ICollection<OrderKpiDto>> GetChartOrderReportWithDateRange(DateTime start, DateTime end);
         Task<MonthOrderKpiDto> GetOrderKpiReportWithMonth(int year, int month);
          Task<bool> Checkout(IFlutterData flutter, int CartId);
         Task<bool> AddPickupPhoto(IFormFile file, int Id, string path, int maxPhotos);
    }
}